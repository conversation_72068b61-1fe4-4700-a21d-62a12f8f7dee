import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as a,f as ze,b as He,L as We}from"./vendor-851db8c1.js";import{d as Z,M as se,H as Fe,E as re,J as oe,G as de,b as R,e as Ue,a as Ye,u as je,R as X}from"./index-9f98cff7.js";import{S as Ke}from"./SportList-cdc2c351.js";import{S as be}from"./SplashScreenPagePreview-9528955f.js";import{B as fe}from"./BottomDrawer-303d7327.js";import{I as Qe}from"./ImageCropModal-c49f0223.js";import{F as Xe}from"./index.esm-09a3a6b8.js";import{B as es,a as ss}from"./index.esm-b72032a7.js";import{A as ts}from"./AuthLayout-dbf1f583.js";import{M as as}from"./MembershipCard-02fbb4c5.js";import{S as is}from"./SportTypeSelection-ee0cc3da.js";import{H as rs}from"./HistoryComponent-af37bc67.js";let Ve=new se;function ls({onClose:s,fetchSettings:i,club:y,clubUser:t}){const[j,o]=a.useState(!1),[l,m]=a.useState(null),_=localStorage.getItem("user"),B=(()=>{try{return JSON.parse((y==null?void 0:y.court_description)||'{"reservation_description":"","payment_description":""}')}catch{return{reservation_description:"",payment_description:""}}})(),[v,O]=a.useState(B.reservation_description),[p,r]=a.useState(B.payment_description),[C,N]=a.useState(v),[D,L]=a.useState(p),k=localStorage.getItem("role"),H=d=>{m(d)},h=async()=>{try{o(!0);const d={reservation_description:C,payment_description:D},c=await Ve.callRawAPI(k==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${t==null?void 0:t.id}`:`/v3/api/custom/courtmatchup/${k}/profile-edit`,{court_description:JSON.stringify(d)},"POST");await Fe(Ve,{user_id:_,activity_type:re.club_ui,action_type:oe.UPDATE,data:d,club_id:y==null?void 0:y.id,description:"Updated court booking descriptions"}),O(C),r(D),m(null),o(!1),i(k==="admin"?t==null?void 0:t.id:null)}catch(d){console.log(d),o(!1)}},x=()=>{N(v),L(p),m(null)};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Reservation Description"}),l!=="reservation"&&e.jsx("button",{onClick:()=>H("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:v?"Edit":"Add Description"})]}),l==="reservation"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:C,onChange:d=>N(d.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:h,loading:j,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:x,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:v||"No reservation description added yet."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Payment Description"}),l!=="payment"&&e.jsx("button",{onClick:()=>H("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:p?"Edit":"Add Description"})]}),l==="payment"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:D,onChange:d=>L(d.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:h,loading:j,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:x,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:p||"No payment description added yet."})]})]})]})})}let ns=new se;function os({clubUser:s,onClose:i,fetchSettings:y,club:t,sports:j}){const{dispatch:o}=a.useContext(de),[l,m]=a.useState(!1),[_,B]=a.useState(!1),v=localStorage.getItem("role"),[O,p]=a.useState([]),[r,C]=a.useState([]),N=()=>{if(j&&j.length>0){const h=[];j.forEach(x=>{var c;((c=x.sport_types)==null?void 0:c.some(w=>w.type&&w.type.trim()!==""))?x.sport_types.forEach(w=>{w.type&&w.type.trim()!==""&&(h.push({id:`${x.id}_type_${w.club_sport_type_id||w.type}`,name:`${x.name} - ${w.type}`,sport_name:x.name,sport_id:x.id,type:w.type,club_sport_type_id:w.club_sport_type_id,threshold:0,level:"type"}),w.subtype&&Array.isArray(w.subtype)&&w.subtype.forEach((V,T)=>{V&&V.trim()!==""&&h.push({id:`${x.id}_subtype_${w.club_sport_type_id||w.type}_${T}`,name:`${x.name} - ${w.type} - ${V}`,sport_name:x.name,sport_id:x.id,type:w.type,subtype:V,club_sport_type_id:w.club_sport_type_id,threshold:0,level:"subtype"})}))}):h.push({id:x.id,name:x.name,status:x.status,club_id:x.club_id,threshold:0,level:"sport",sport_id:x.id})}),p(h),C(JSON.parse(JSON.stringify(h)))}};a.useEffect(()=>{if(t!=null&&t.custom_request_threshold)try{let h=typeof t.custom_request_threshold=="string"?JSON.parse(t.custom_request_threshold):t.custom_request_threshold;if(!Array.isArray(h)){console.warn("Threshold data is not an array, initializing with sports data"),N();return}h.some(d=>d.sport_types&&Array.isArray(d.sport_types))?(console.log("Converting old threshold format to new format"),N()):(p(h),C(JSON.parse(JSON.stringify(h))))}catch(h){console.error("Error parsing threshold data:",h),N()}else N()},[t,j]);const D=()=>{m(!0)},L=async()=>{B(!0);try{await ns.callRawAPI(v==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:`/v3/api/custom/courtmatchup/${v}/profile-edit`,{custom_request_threshold:JSON.stringify(r)},"POST"),p(JSON.parse(JSON.stringify(r))),m(!1),R(o,"Custom request thresholds updated",3e3,"success"),y(v==="admin"?s==null?void 0:s.id:null)}catch(h){console.log(h),h!=null&&h.message?R(o,h==null?void 0:h.message,3e3,"error"):R(o,"Error updating custom request thresholds",3e3,"error")}finally{B(!1)}},k=()=>{C(JSON.parse(JSON.stringify(O))),m(!1)},H=(h,x)=>{try{if(!Array.isArray(r)){console.error("tempThresholdData is not an array");return}const d=r.map(c=>c.id===h?{...c,threshold:parseInt(x)||0}:c);C(d)}catch(d){console.error("Error updating threshold:",d)}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"flex items-center justify-end",children:l?e.jsxs("div",{className:"mt-4 flex gap-4",children:[e.jsx(Z,{onClick:L,loading:_,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:k,className:"font-medium text-primaryBlue underline hover:text-blue-800",children:"Cancel"})]}):e.jsx("button",{onClick:D,className:"font-medium text-primaryBlue underline hover:text-blue-800",children:"Edit"})}),e.jsx("div",{className:"mb-4 text-sm text-gray-500",children:"Set the threshold for custom requests for each sport, type, and subtype. This determines how many players are needed before a custom request is created."}),e.jsx("div",{className:"h-full space-y-4 overflow-y-auto pr-2",children:Array.isArray(r)&&r.length>0?(()=>{const h=r.reduce((x,d)=>{const c=d.sport_id||d.id,w=d.sport_name||d.name;return x[c]||(x[c]={sportName:w,items:[]}),x[c].items.push(d),x},{});return Object.entries(h).map(([x,d])=>e.jsxs("div",{className:"space-y-3 rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"border-b border-gray-100 pb-2 font-medium text-gray-800",children:d.sportName}),e.jsx("div",{className:"space-y-2",children:d.items.map(c=>e.jsxs("div",{className:`flex items-center justify-between rounded p-2 ${c.level==="sport"?"border border-blue-200 bg-blue-50":c.level==="type"?"ml-4 border border-green-200 bg-green-50":"ml-8 border border-yellow-200 bg-yellow-50"}`,children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-700",children:[c.level==="sport"&&"Sport Level",c.level==="type"&&`Type: ${c.type}`,c.level==="subtype"&&`Subtype: ${c.type} - ${c.subtype}`]}),c.level==="sport"&&e.jsx("div",{className:"text-xs text-gray-500",children:"(Only shown when sport has no types/subtypes)"})]}),e.jsx("div",{className:"flex items-center",children:l?e.jsxs(e.Fragment,{children:[e.jsx("label",{className:"mr-2 text-sm text-gray-600",children:"Threshold:"}),e.jsx("input",{type:"number",min:"0",value:c.threshold||0,onChange:w=>H(c.id,w.target.value),className:"w-16 rounded-md border border-gray-300 p-1 text-center"})]}):e.jsxs("div",{className:"text-sm text-gray-600",children:["Threshold:"," ",e.jsx("span",{className:"font-medium",children:c.threshold||0})]})})]},c.id))})]},x))})():e.jsx("div",{className:"py-4 text-center text-gray-500",children:"No sports data available. Please add sports to set thresholds."})})]})})}function ds({clubUser:s,club:i,pricing:y}){return console.log("club user",s),e.jsxs("div",{className:"flex flex-col gap-6",children:[y!=null&&y.length?y==null?void 0:y.map((t,j)=>{var o;return e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:(o=t==null?void 0:t.courts)==null?void 0:o.map((l,m)=>{var _;return e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:l==null?void 0:l.title}),e.jsx("button",{className:"text-gray-400",children:e.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{d:"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"})})})]}),(_=l==null?void 0:l.slots)==null?void 0:_.map((B,v)=>e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:B.time}),e.jsx("span",{className:"rounded-md bg-green-100 px-2 py-1 text-sm text-green-800",children:B.price})]},v))]},m)})},j)}):e.jsx("div",{className:"text-center text-gray-600",children:"No pricing found"}),e.jsxs("button",{className:"mt-4 flex items-center text-gray-600 hover:text-gray-800",children:[e.jsx("span",{className:"mr-2",children:"+"})," Add Pricing"]})]})}let pe=new se;function cs({fetchSettings:s,setShowSplashScreenPreview:i,setPreviewImageList:y,club:t,clubUser:j}){var Le,Pe,Be,Ee;const[o,l]=a.useState(0),[m,_]=a.useState([]),[B,v]=a.useState(null),[O,p]=a.useState(null),r=a.useMemo(()=>{try{return t!=null&&t.splash_screen?JSON.parse(t.splash_screen):null}catch(n){return console.error("Error parsing splash screen data:",n),null}},[t==null?void 0:t.splash_screen]),[C,N]=a.useState(r==null?void 0:r.club_logo),[D,L]=a.useState(!1),[k,H]=a.useState(!1),[h,x]=a.useState(null),[d,c]=a.useState(null),[w,V]=a.useState(!1),[T,U]=a.useState(!1),[S,J]=a.useState((t==null?void 0:t.name)||""),[$,q]=a.useState((t==null?void 0:t.name)||""),G=localStorage.getItem("role"),te=localStorage.getItem("user"),z=a.useRef(null),K=a.useRef(null),Y=a.useRef(null),g=a.useMemo(()=>{if(r!=null&&r.images){const n=new Array(9).fill(null);return r.images.forEach((u,f)=>{u&&u.url&&(n[f]={url:u.url,isDefault:!0,id:u.id||`default-${f}`,type:u.type||"image"})}),n}return new Array(9).fill(null)},[r==null?void 0:r.images]),[M,E]=a.useState(g),[P,me]=a.useState((r==null?void 0:r.slideshow_delay)||6e3),[he,xe]=a.useState(!1),le=a.useRef(),{dispatch:W}=a.useContext(de),[ve,ae]=a.useState(!1);a.useEffect(()=>{r!=null&&r.bio&&le.current&&(le.current.value=r.bio)},[]),a.useEffect(()=>{t!=null&&t.name&&(J(t.name),q(t.name))},[t==null?void 0:t.name]),a.useEffect(()=>{const n=o*3,u=M.slice(n,n+3);_(u)},[o,M]);const ue=async n=>{try{let u=new FormData;u.append("file",n);const f=n.type.startsWith("video/");let b;return b=await pe.uploadImage(u),b.url}catch(u){return console.error("Upload error:",u),R(W,"Failed to upload file. Please try again.",3e3,"error"),null}},F=()=>M.slice(0,3).filter(u=>u!==null).length,I=n=>{const u=F(),f=Math.floor(n/3);if(f===0)return!0;const b=f*3;return M.slice(b,b+3).filter(ce=>ce!==null).length<u},ee=(n,u)=>{const f=n.target.files[0];if(!f)return;const b=o*3+u;if(!I(b)){const Q=F();R(W,`You can only upload up to ${Q} image${Q!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}if(!["image/jpeg","image/png","image/gif","video/mp4","video/quicktime","video/x-m4v","application/pdf"].includes(f.type)){R(W,"Please upload a valid file type (JPEG, PNG, GIF, MP4, or PDF)",3e3,"error");return}if(f.size>50*1024*1024){R(W,"File size must be less than 50MB",3e3,"error");return}const ie=[...M],ce=URL.createObjectURL(f);let ne="image";f.type.startsWith("video/")?ne="video":f.type==="application/pdf"&&(ne="pdf"),ie[b]={file:f,url:ce,id:Date.now(),isDefault:!1,type:ne,previewUrl:ce},E(ie)},ge=n=>{n.preventDefault(),n.stopPropagation()},_e=n=>{n.preventDefault(),n.stopPropagation()},ke=n=>{n.preventDefault(),n.stopPropagation()},Se=(n,u)=>{n.preventDefault(),n.stopPropagation();const f=n.dataTransfer.files;if(f.length>0){const b=o*3+u;if(!I(b)){const ie=F();R(W,`You can only upload up to ${ie} image${ie!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}const A={target:{files:[f[0]]}};ee(A,u)}},Me=n=>{const u=o*3+n,f=[...M],b=f[u];b&&(!b.isDefault&&b.url&&URL.revokeObjectURL(b.url),f[u]=null,E(f))},Re=()=>{l(n=>n+1)},Ze=()=>{l(n=>Math.max(0,n-1))},De=()=>m,Te=async()=>{var n,u,f,b;try{xe(!0);const A=(n=le.current)==null?void 0:n.value;if(!(A!=null&&A.trim())){R(W,"Please enter a bio",3e3,"error");return}const ie=await Promise.all(M.map(async Q=>{if(!Q)return null;if(Q.isDefault)return Q;{const Oe=await ue(Q.file);return Oe?(Q.previewUrl&&URL.revokeObjectURL(Q.previewUrl),{url:Oe,isDefault:!0,id:`default-${Date.now()}-${Math.random()}`,type:Q.file.type.startsWith("video/")?"video":"image"}):null}})),ce={bio:A.trim(),images:ie,slideshow_delay:P,button_text:((u=z.current)==null?void 0:u.value)||"Let the club know you're interested",phone:((f=K.current)==null?void 0:f.value)||"",email:((b=Y.current)==null?void 0:b.value)||""},ne={splash_screen:JSON.stringify(ce),name:S,club_logo:C};G=="admin"||G=="admin_staff"?await pe.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${j==null?void 0:j.id}`,ne,"POST"):await pe.callRawAPI(`/v3/api/custom/courtmatchup/${G}/profile-edit`,ne,"POST"),pe.setTable("activity_logs"),await pe.callRestAPI({user_id:te,activity_type:re.club_ui,action_type:oe.UPDATE,data:JSON.stringify(ne),club_id:t==null?void 0:t.id,description:"Updated club splash screen"},"POST"),R(W,"Splash screen updated",3e3,"success"),await s(G==="admin"?j==null?void 0:j.id:null)}catch(A){console.error("Submission failed:",A),R(W,"Failed to submit. Please try again.",3e3,"error")}finally{xe(!1)}},Ae=n=>{const u=n.target.files[0];if(!u)return;if(u.size>2*1024*1024){alert("File size exceeds 2MB limit. Please choose a smaller file.");return}c(u.type);const f=new FileReader;f.onload=()=>{x(f.result),H(!0)},f.readAsDataURL(u)},$e=async n=>{L(!0),v(URL.createObjectURL(n));const u=d==="image/png",f=await ue(n);N(f),p(new File([n],`cropped_logo.${u?"png":"jpg"}`,{type:u?"image/png":"image/jpeg"})),Ye(URL.createObjectURL(n)),L(!1)};a.useEffect(()=>()=>{B&&URL.revokeObjectURL(B)},[B]);const qe=()=>{q(S||(t==null?void 0:t.name)||""),V(!0)},Je=()=>{J($),q($),V(!1)},Ge=()=>{q(S),V(!1)};return e.jsxs("div",{className:"flex max-w-xl flex-col gap-4 rounded-lg bg-white",children:[D&&e.jsx(Ue,{}),e.jsxs("div",{className:"flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-1 text-sm font-medium text-gray-700",children:"Club Landing Page URL"}),e.jsx("p",{className:"break-all text-sm text-gray-500",children:`${window.location.origin}/club/${t==null?void 0:t.id}`})]}),e.jsxs("button",{onClick:()=>{navigator.clipboard.writeText(`${window.location.origin}/club/${t==null?void 0:t.id}`),R(W,"URL copied to clipboard!",2e3,"success")},className:"flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50",children:[e.jsx(Xe,{className:"h-4 w-4"}),"Copy"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-2 text-lg font-semibold",children:"Tell us about your club"}),e.jsxs("p",{className:"flex items-start gap-2 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{children:"This info will be visible to users who want to book your club. You can edit this content into your Club Panel later."})]})]}),e.jsx("div",{}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"relative  h-[100px] w-[100px] overflow-hidden rounded-lg bg-gray-100",children:e.jsx("img",{src:B||C||(t==null?void 0:t.club_logo)||"/logo.png",alt:"Club logo",className:"h-[100px] w-[100px] object-cover"})}),e.jsxs("div",{className:"w-full space-y-1",children:[e.jsx("span",{children:"Club logo"}),e.jsx("div",{className:"flex justify-between py-1 text-xs text-gray-500",children:e.jsx("span",{children:"Min 400x400px, PNG or JPEG"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(Z,{onClick:()=>{v(null),p(null)},className:"rounded-md border border-red-500 bg-white px-2 py-1 text-xs text-red-500",children:"Remove"}),e.jsx("input",{type:"file",id:"logo-upload",className:"hidden",accept:"image/**",onChange:Ae}),e.jsx(Z,{onClick:()=>document.getElementById("logo-upload").click(),className:"rounded-md border border-gray-400 bg-white px-2 py-1 text-xs text-gray-600",children:"Change Logo"})]})]})]}),e.jsx("div",{className:"mt-4 flex items-center gap-4 border-y border-gray-200 py-3",children:e.jsxs("div",{className:" w-full",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Club name"}),w?e.jsxs("div",{className:"space-y-2",children:[e.jsx("input",{type:"text",value:$,onChange:n=>q(n.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-lg font-medium focus:border-blue-500 focus:outline-none",placeholder:"Enter club name"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(Z,{onClick:Je,loading:T,className:"rounded-lg bg-primaryBlue px-4 py-1 text-sm text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:Ge,className:"text-sm text-primaryBlue hover:underline",children:"Cancel"})]})]}):e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-medium",children:S||"Club name"}),e.jsx("button",{onClick:qe,className:"text-sm text-primaryBlue hover:underline",children:"Edit"})]})]})}),e.jsx("div",{children:e.jsx("button",{className:"underline",onClick:()=>{i(!0),y(M)},children:"Page preview"})}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Upload images"}),e.jsx("p",{className:"mb-2 text-sm text-gray-500",children:"JPEG, PNG, PDF, and MP4 formats, up to 50 MB. Drag and drop files or click to browse."}),o>0&&e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2",children:e.jsxs("p",{className:"text-xs text-blue-700",children:[e.jsx("strong",{children:"Note:"})," You can upload up to"," ",F()," image",F()!==1?"s":""," per slide based on your Slide 1 pattern."]})}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"grid grid-rows-[1fr_1fr] gap-4",children:[e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[0,1].map(n=>{const u=De()[n],f=o*3+n,b=!I(f);return e.jsxs("div",{className:`relative h-[100px] max-h-[100px] w-full rounded-xl border-2 border-dashed transition-colors ${b?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:b?void 0:ge,onDragEnter:b?void 0:_e,onDragLeave:b?void 0:ke,onDrop:b?void 0:A=>Se(A,n),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:A=>ee(A,n),id:`file-input-${n}`,disabled:b}),e.jsx("label",{htmlFor:b?void 0:`file-input-${n}`,className:`absolute inset-0 ${b?"cursor-not-allowed":"cursor-pointer"}`,children:u&&u.url?e.jsxs("div",{className:"relative h-full w-full",children:[u.type==="video"?e.jsxs("video",{src:u.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:u.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):u.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:u.url,alt:`Upload ${n+1}`,className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:A=>{A.preventDefault(),A.stopPropagation(),Me(n)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),u.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:b?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${o}-${n}`)})}),e.jsx("div",{className:"h-[100px] max-h-[100px]",children:(()=>{const n=De()[2],u=o*3+2,f=!I(u);return e.jsxs("div",{className:`relative h-full w-full rounded-xl border-2 border-dashed transition-colors ${f?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:f?void 0:ge,onDragEnter:f?void 0:_e,onDragLeave:f?void 0:ke,onDrop:f?void 0:b=>Se(b,2),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:b=>ee(b,2),id:"file-input-2",disabled:f}),e.jsx("label",{htmlFor:f?void 0:"file-input-2",className:`absolute inset-0 ${f?"cursor-not-allowed":"cursor-pointer"}`,children:n&&n.url?e.jsxs("div",{className:"relative h-full w-full",children:[n.type==="video"?e.jsxs("video",{src:n.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:n.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):n.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:n.url,alt:"Upload 3",className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:b=>{b.preventDefault(),b.stopPropagation(),Me(2)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),n.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:f?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${o}-2`)})()})]},o),e.jsxs("div",{className:"mt-4 flex items-center justify-center gap-4",children:[e.jsx("button",{onClick:Ze,disabled:o===0,className:`rounded-full bg-white p-2 shadow-md ${o===0?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsxs("span",{className:"text-sm",children:["Slide ",o+1]}),e.jsx("button",{onClick:Re,disabled:o===2,className:`rounded-full bg-white p-2 shadow-md ${o===2?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Bio"}),e.jsx("textarea",{ref:le,rows:4,className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Slideshow Delay (seconds)"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"number",min:"1",max:"60",value:P/1e3,onChange:n=>me(Math.max(1e3,Math.min(6e4,n.target.value*1e3))),className:"w-24 rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"}),e.jsx("span",{className:"text-sm text-gray-500",children:"seconds"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose between 1 and 60 seconds (default: 6 seconds)"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Button Text"}),e.jsx("input",{type:"text",ref:z,defaultValue:(r==null?void 0:r.button_text)||"Let the club know you're interested",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter button text"})]}),e.jsxs("div",{className:"mb-4 space-y-4",children:[e.jsx("h3",{className:"text-sm font-medium",children:"Contact Information"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Phone Number"}),e.jsx("input",{type:"tel",ref:K,defaultValue:(r==null?void 0:r.phone)||"",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter phone number"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Email Address"}),e.jsx("input",{type:"email",ref:Y,defaultValue:(r==null?void 0:r.email)||"",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter email address"})]})]}),e.jsx(Z,{onClick:Te,loading:he,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Save changes"}),e.jsx(fe,{isOpen:ve,onClose:()=>ae(!1),title:"Preview",children:e.jsx(be,{clubName:t==null?void 0:t.name,image:t==null?void 0:t.name,description:(Le=le.current)==null?void 0:Le.value,imageList:M,clubLogo:C,slideshowDelay:P,buttonText:((Pe=z.current)==null?void 0:Pe.value)||"Let the club know you're interested",phone:((Be=K.current)==null?void 0:Be.value)||"",email:((Ee=Y.current)==null?void 0:Ee.value)||""})}),e.jsx(Qe,{isOpen:k,onClose:()=>H(!1),image:h,onCropComplete:$e})]})}let Ne=new se;function ms({clubUser:s,onClose:i,fetchSettings:y}){const[t,j]=a.useState(!1),[o,l]=a.useState(null),{club:m}=je(),_=localStorage.getItem("user"),B=(()=>{try{return JSON.parse((m==null?void 0:m.lesson_description)||'{"reservation_description":"","payment_description":""}')}catch{return{reservation_description:"",payment_description:""}}})(),[v,O]=a.useState(B.reservation_description),[p,r]=a.useState(B.payment_description),[C,N]=a.useState(v),[D,L]=a.useState(p),k=localStorage.getItem("role"),H=d=>{l(d)},h=async()=>{try{j(!0);const d={reservation_description:C,payment_description:D},c=await Ne.callRawAPI(k==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:"/v3/api/custom/courtmatchup/club/profile-edit",{lesson_description:JSON.stringify(d)},"POST");Ne.setTable("activity_logs"),await Ne.callRestAPI({user_id:_,activity_type:re.club_ui,action_type:oe.UPDATE,data:JSON.stringify(d),club_id:m==null?void 0:m.id,description:"Updated lesson booking descriptions"},"POST"),O(C),r(D),l(null),j(!1),y(k==="admin"?s==null?void 0:s.id:null)}catch(d){console.log(d),j(!1)}},x=()=>{N(v),L(p),l(null)};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Reservation Description"}),o!=="reservation"&&e.jsx("button",{onClick:()=>H("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:v?"Edit":"Add Description"})]}),o==="reservation"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:C,onChange:d=>N(d.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:h,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:x,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:v||"No reservation description added yet."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Payment Description"}),o!=="payment"&&e.jsx("button",{onClick:()=>H("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:p?"Edit":"Add Description"})]}),o==="payment"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:D,onChange:d=>L(d.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:h,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:x,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:p||"No payment description added yet."})]})]})]})})}let we=new se;function ps({clubUser:s,onClose:i,fetchSettings:y,club:t}){const[j,o]=a.useState(!1),[l,m]=a.useState(null),{dispatch:_}=a.useContext(de),B=localStorage.getItem("user"),v=(()=>{try{return JSON.parse((t==null?void 0:t.clinic_description)||'{"reservation_description":"","payment_description":"","default_view":"table","custom_filters":[]}')}catch{return{reservation_description:"",payment_description:"",default_view:"table",custom_filters:[]}}})(),[O,p]=a.useState(v.reservation_description),[r,C]=a.useState(v.payment_description),[N,D]=a.useState(v.default_view||"table"),[L,k]=a.useState(v.custom_filters||[]),[H,h]=a.useState(O),[x,d]=a.useState(r),[c,w]=a.useState(N),[V,T]=a.useState(L),U=localStorage.getItem("role"),S=g=>{m(g)},J=async()=>{try{o(!0);const g={reservation_description:H,payment_description:x,default_view:c,custom_filters:V},M=await we.callRawAPI(U==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:"/v3/api/custom/courtmatchup/club/profile-edit",{clinic_description:JSON.stringify(g)},"POST");we.setTable("activity_logs"),await we.callRestAPI({user_id:B,activity_type:re.club_ui,action_type:oe.UPDATE,data:JSON.stringify(g),club_id:t==null?void 0:t.id,description:"Updated clinic booking descriptions"},"POST"),R(_,"Descriptions updated successfully",3e3,"success"),p(H),C(x),D(c),k(V),m(null),o(!1),y(U==="admin"?s==null?void 0:s.id:null)}catch(g){console.log(g),o(!1)}},$=()=>{h(O),d(r),w(N),T(L),m(null)},q=g=>{w(g)},G=[{key:"clinic_level",label:"Clinic Level"},{key:"max_participants",label:"Max Participants"},{key:"clinic_cost",label:"Clinic Cost"},{key:"recurring",label:"Recurring"},{key:"sport_name",label:"Sport"},{key:"type",label:"Type"},{key:"sub_type",label:"Sub Type"},{key:"clinic_day",label:"Day of Week"},{key:"time_of_day",label:"Time of Day"}],te=()=>{const g={id:Date.now(),key:"",label:"",enabled:!0};T([...V,g])},z=(g,M)=>{const E=G.find(P=>P.key===M);T(V.map(P=>P.id===g?{...P,key:M,label:(E==null?void 0:E.label)||""}:P))},K=g=>{T(V.filter(M=>M.id!==g))},Y=g=>{T(V.map(M=>M.id===g?{...M,enabled:!M.enabled}:M))};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Reservation Description"}),l!=="reservation"&&e.jsx("button",{onClick:()=>S("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:O?"Edit":"Add Description"})]}),l==="reservation"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:H,onChange:g=>h(g.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:J,loading:j,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:$,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:O||"No reservation description added yet."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Payment Description"}),l!=="payment"&&e.jsx("button",{onClick:()=>S("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:r?"Edit":"Add Description"})]}),l==="payment"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:x,onChange:g=>d(g.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:J,loading:j,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:$,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:r||"No payment description added yet."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Default View"}),l!=="default_view"&&e.jsx("button",{onClick:()=>S("default_view"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:"Edit"})]}),l==="default_view"?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"table",checked:c==="table",onChange:()=>q("table"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Table"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"calendar",checked:c==="calendar",onChange:()=>q("calendar"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Calendar"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"weekly",checked:c==="weekly",onChange:()=>q("weekly"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Weekly"})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:J,loading:j,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:$,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:N==="table"?"Table":N==="calendar"?"Calendar":N==="weekly"?"Weekly":"Table"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Custom Filters"}),l!=="custom_filters"&&e.jsx("button",{onClick:()=>S("custom_filters"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:L.length>0?"Edit Filters":"Add Filters"})]}),l==="custom_filters"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"space-y-4",children:V.map(g=>e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsxs("select",{value:g.key,onChange:M=>z(g.id,M.target.value),className:"flex-1 rounded-lg border border-gray-300 px-3 py-2",children:[e.jsx("option",{value:"",children:"Select a clinic property"}),G.filter(M=>!V.some(E=>E.id!==g.id&&E.key===M.key)).map(M=>e.jsx("option",{value:M.key,children:M.label},M.key))]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:g.enabled,onChange:()=>Y(g.id),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Enabled"})]}),e.jsx("button",{onClick:()=>K(g.id),className:"rounded-lg bg-red-500 px-3 py-2 text-white hover:bg-red-600",children:"Delete"})]}),g.key&&e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Filter:"})," ",g.label,e.jsx("br",{}),e.jsx("strong",{children:"Property:"})," ",g.key]})]},g.id))}),e.jsx("button",{onClick:te,className:"rounded-lg border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50",children:"+ Add Filter Property"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:J,loading:j,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:$,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("div",{className:"text-gray-700",children:L.length>0?e.jsx("div",{className:"space-y-2",children:L.map(g=>e.jsxs("div",{children:[e.jsxs("strong",{children:[g.label,":"]})," ",e.jsx("span",{className:g.enabled?"text-green-600":"text-red-600",children:g.enabled?"Enabled":"Disabled"}),e.jsx("br",{}),e.jsxs("span",{className:"text-sm text-gray-500",children:["Property: ",g.key]})]},g.id))}):"No custom filters configured yet."})]})]})]})})}let Ie=new se;function hs({clubUser:s,onClose:i,fetchSettings:y}){const[t,j]=a.useState(!1),[o,l]=a.useState(null),{club:m}=je(),_=a.useContext(de),B=localStorage.getItem("user"),v=(()=>{try{return JSON.parse((m==null?void 0:m.coach_description)||'{"reservation_description":"","payment_description":""}')}catch{return{reservation_description:"",payment_description:""}}})(),[O,p]=a.useState(v.reservation_description),[r,C]=a.useState(v.payment_description),[N,D]=a.useState(O),[L,k]=a.useState(r),H=localStorage.getItem("role"),h=c=>{l(c)},x=async()=>{try{j(!0);const c={reservation_description:N,payment_description:L},w=await Ie.callRawAPI(H==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:"/v3/api/custom/courtmatchup/club/profile-edit",{coach_description:JSON.stringify(c)},"POST");await Fe(Ie,{user_id:B,activity_type:re.club_ui,action_type:oe.UPDATE,data:c,club_id:m==null?void 0:m.id,description:"Updated coach booking descriptions"}),R(_,"Description saved successfully",3e3,"success"),p(N),C(L),l(null),j(!1),y(H==="admin"?s==null?void 0:s.id:null)}catch(c){console.log(c),j(!1)}},d=()=>{D(O),k(r),l(null)};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),!o&&e.jsx("button",{onClick:()=>h("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:O?"Edit":"Add Reservation Description"})]}),o==="reservation"?e.jsxs(e.Fragment,{children:[e.jsx("textarea",{value:N,onChange:c=>D(c.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:x,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:d,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:O||"No reservation description added yet."}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),!o&&e.jsx("button",{onClick:()=>h("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:r?"Edit":"Add Payment Description"})]}),o==="payment"?e.jsxs(e.Fragment,{children:[e.jsx("textarea",{value:L,onChange:c=>k(c.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:x,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:d,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:r||"No payment description added yet."})]})})}let Ce=new se;function xs({clubUser:s,onClose:i,fetchSettings:y}){const[t,j]=a.useState(!1),[o,l]=a.useState(null),{dispatch:m}=a.useContext(de),{club:_}=je(),B=localStorage.getItem("user"),v=(()=>{try{return JSON.parse((_==null?void 0:_.buddy_description)||'{"reservation_description":"","payment_description":"","default_view":"table"}')}catch{return{reservation_description:"",payment_description:"",default_view:"table"}}})(),[O,p]=a.useState(v.reservation_description),[r,C]=a.useState(v.payment_description),[N,D]=a.useState(v.default_view||"table"),[L,k]=a.useState(O),[H,h]=a.useState(r),[x,d]=a.useState(N),c=localStorage.getItem("role"),w=S=>{l(S)},V=async()=>{try{j(!0);const S={reservation_description:L,payment_description:H,default_view:x},J=await Ce.callRawAPI(c==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:"/v3/api/custom/courtmatchup/club/profile-edit",{buddy_description:JSON.stringify(S)},"POST");Ce.setTable("activity_logs"),await Ce.callRestAPI({user_id:B,activity_type:re.club_ui,action_type:oe.UPDATE,data:JSON.stringify(S),club_id:_==null?void 0:_.id,description:"Updated find a buddy booking descriptions"},"POST"),R(m,"Descriptions updated successfully",3e3,"success"),p(L),C(H),D(x),l(null),j(!1),y(c==="admin"?s==null?void 0:s.id:null)}catch(S){console.log(S),j(!1)}},T=()=>{k(O),h(r),d(N),l(null)},U=S=>{d(S)};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),!o&&e.jsx("button",{onClick:()=>w("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:O?"Edit":"Add Description"})]}),o==="reservation"?e.jsxs(e.Fragment,{children:[e.jsx("textarea",{value:L,onChange:S=>k(S.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:V,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:T,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:O||"No reservation description added yet."}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Payment Description"}),!o&&e.jsx("button",{onClick:()=>w("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:r?"Edit":"Add Description"})]}),o==="payment"?e.jsxs(e.Fragment,{children:[e.jsx("textarea",{value:H,onChange:S=>h(S.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:V,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:T,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:r||"No payment description added yet."}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Default View"}),!o&&e.jsx("button",{onClick:()=>w("default_view"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:"Edit"})]}),o==="default_view"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"table",checked:x==="table",onChange:()=>U("table"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Table"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"calendar",checked:x==="calendar",onChange:()=>U("calendar"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Calendar"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"weekly",checked:x==="weekly",onChange:()=>U("weekly"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Weekly"})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{onClick:V,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:T,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:N==="table"?"Table":N==="calendar"?"Calendar":N==="weekly"?"Weekly":"Table"})]})})}function us({clubName:s,description:i,title:y,image:t}){return e.jsxs("div",{className:"flex min-h-screen flex-col bg-white",children:[e.jsxs("header",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-2xl",children:"🎾"}),e.jsx("span",{className:"text-xl font-semibold",children:s})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{className:"flex items-center gap-2 rounded-xl bg-primaryBlue px-4 py-2 text-white",children:[e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.375 5.62502V3.12502C14.375 2.66478 14.0019 2.29169 13.5417 2.29169H3.12502C2.66478 2.29169 2.29169 2.66478 2.29169 3.12502V13.5417C2.29169 14.0019 2.66478 14.375 3.12502 14.375H5.62502M7.77191 17.7084C8.15596 15.8035 9.75384 14.375 11.6667 14.375C13.5795 14.375 15.1774 15.8035 15.5615 17.7084M7.77191 17.7084H6.45835C5.99812 17.7084 5.62502 17.3353 5.62502 16.875V6.45835C5.62502 5.99812 5.99812 5.62502 6.45835 5.62502H16.875C17.3353 5.62502 17.7084 5.99812 17.7084 6.45835V16.875C17.7084 17.3353 17.3353 17.7084 16.875 17.7084H15.5615M7.77191 17.7084H15.5615M13.5417 10.4167C13.5417 11.4522 12.7022 12.2917 11.6667 12.2917C10.6312 12.2917 9.79169 11.4522 9.79169 10.4167C9.79169 9.38115 10.6312 8.54169 11.6667 8.54169C12.7022 8.54169 13.5417 9.38115 13.5417 10.4167Z",stroke:"white","stroke-width":"1.5","stroke-linecap":"square","stroke-linejoin":"round"})})}),e.jsx("span",{children:"Sign up"})]}),e.jsxs("button",{className:"flex items-center rounded-xl border border-gray-300 px-4 py-2 text-gray-500",children:[e.jsx("span",{children:"Log in"}),e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.2917 3.125L16.0417 3.125C16.5019 3.125 16.875 3.4981 16.875 3.95833V16.0417C16.875 16.5019 16.5019 16.875 16.0417 16.875H12.2917M12.5 10H3.125M12.5 10L9.58333 12.9167M12.5 10L9.58333 7.08334",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]})]})]}),e.jsxs("main",{className:"flex flex-col gap-8 p-4 md:flex-row md:p-8",children:[e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"grid w-full",children:e.jsx("div",{className:" aspect-[4/3] overflow-hidden rounded-lg bg-gray-100",children:e.jsx("img",{src:t||"",alt:`Home logged in page preview of ${s}`,className:"h-full w-full object-cover"})})})}),e.jsxs("div",{className:"flex-1 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-medium",children:y||"Welcome!"}),e.jsxs("button",{className:"flex items-center gap-2 text-gray-600",children:[e.jsx("span",{children:"Visit website"}),e.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.2958 9.99926L7.58331 6.28676L8.64381 5.22626L13.4168 9.99926L8.64381 14.7723L7.58331 13.7118L11.2958 9.99926Z",fill:"#525866"})})]})]}),e.jsx("div",{className:"max-h-[400px] space-y-4 overflow-y-auto",children:e.jsx("p",{className:"text-gray-600",children:i})}),e.jsxs("div",{className:"mt-8 rounded-xl p-4 shadow-5",children:[e.jsx("button",{className:"w-full rounded-xl bg-[#176448] py-2 text-white",children:e.jsxs("span",{className:"flex items-center justify-center gap-2",children:[e.jsx("span",{className:"calendar-icon",children:e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M16 3.5H9V4.75C9 5.16421 8.66421 5.5 8.25 5.5C7.83579 5.5 7.5 5.16421 7.5 4.75V3.5H5.25C4.2835 3.5 3.5 4.2835 3.5 5.25V8H21.5V5.25C21.5 4.2835 20.7165 3.5 19.75 3.5H17.5V4.75C17.5 5.16421 17.1642 5.5 16.75 5.5C16.3358 5.5 16 5.16421 16 4.75V3.5Z",fill:"white"}),e.jsx("path",{d:"M21.5 9.5H3.5V19.75C3.5 20.7165 4.2835 21.5 5.25 21.5H19.75C20.7165 21.5 21.5 20.7165 21.5 19.75V9.5Z",fill:"white"})]})}),"Reserve your slot"]})}),e.jsx("p",{className:"mt-5 text-center text-sm text-gray-500",children:"TAKES ONLY 2 MINUTES!"})]})]})]}),e.jsx("footer",{className:"p-4 text-center text-sm text-gray-500",children:"Powered by Court Matchup"})]})}let ye=new se;function gs({fetchSettings:s,profileSettings:i,onSubmit:y,isOpen:t,onClose:j}){var J,$,q,G,te,z,K,Y,g,M;const[o,l]=a.useState(!1),{dispatch:m}=a.useContext(de),[_,B]=a.useState(""),[v,O]=a.useState(""),[p,r]=a.useState(((J=i==null?void 0:i.club)==null?void 0:J.image)||null),[C,N]=a.useState(!1),{club:D}=je(),L=localStorage.getItem("user"),[k,H]=a.useState({findBuddy:(($=i==null?void 0:i.club)==null?void 0:$.show_buddy)===1,clinic:((q=i==null?void 0:i.club)==null?void 0:q.show_clinic)===1,coach:((G=i==null?void 0:i.club)==null?void 0:G.show_coach)===1,groups:((te=i==null?void 0:i.club)==null?void 0:te.show_groups)===1,court:((z=i==null?void 0:i.club)==null?void 0:z.show_court)===1}),[h,x]=a.useState(!0),[d,c]=a.useState(!1);a.useEffect(()=>{i!=null&&i.features&&H(i.features),i!=null&&i.club&&(B(i.club.title||""),O(i.club.description||""),i.club.home_image&&r({file:null,url:i.club.home_image}))},[i]),a.useEffect(()=>{(i==null?void 0:i.published)!==void 0&&x(i.published)},[i]);const w=E=>{const P=E.target.files[0];if(!P)return;if(!["image/jpeg","image/png","image/gif"].includes(P.type)){R(m,"Please upload a valid image file (JPEG, PNG, or GIF)",3e3,"warning");return}if(P.size>50*1024*1024){R(m,"File size must be less than 50MB",3e3,"warning");return}r({file:P,url:URL.createObjectURL(P)})},V=()=>{p!=null&&p.url&&URL.revokeObjectURL(p.url),r(null)},T=async E=>{try{let P=new FormData;return P.append("file",E),(await ye.uploadImage(P)).url}catch(P){return console.log(P),null}},U=async()=>{try{if(l(!0),!(_!=null&&_.trim()))return R(m,"Please enter a title",3e3,"warning"),l(!1),!1;if(!(v!=null&&v.trim()))return R(m,"Please enter content",3e3,"warning"),l(!1),!1;let E=null;p!=null&&p.file&&(E=await T(p.file));const P={title:_.trim(),description:v.trim(),home_image:E,published:h,show_clinic:k.clinic?1:0,show_coach:k.coach?1:0,show_groups:k.groups?1:0,show_court:k.court?1:0,show_buddy:k.findBuddy?1:0};return await ye.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",{...P},"POST"),ye.setTable("activity_logs"),await ye.callRestAPI({user_id:L,activity_type:re.club_ui,action_type:oe.UPDATE,data:JSON.stringify(P),club_id:D==null?void 0:D.id,description:"Updated club homepage"},"POST"),R(m,"Home content updated",3e3,"success"),s(),!0}catch(E){return console.error("Submission failed:",E),R(m,"Failed to submit. Please try again.",3e3,"warning"),!1}finally{l(!1)}},S=E=>{H(P=>({...P,[E]:!P[E]}))};return e.jsxs("div",{children:[e.jsx(X,{isOpen:t,onClose:j,title:"Home logged in",showFooter:!1,children:e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg bg-white py-5",children:[e.jsx("button",{className:"text-left underline",onClick:()=>N(!0),children:"Page preview"}),e.jsx("div",{className:"mb-4",children:e.jsxs("p",{className:"flex items-start gap-2 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{children:"This content will be shown at the top of homepage for logged-in users."})]})}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Features shown"}),e.jsx("p",{className:"mb-4 text-sm text-gray-500",children:"You can control which features are shown on the homepage:"}),e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:k.findBuddy,onChange:()=>S("findBuddy"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Find Buddy"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:k.coach,onChange:()=>S("coach"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Coach"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:k.groups,onChange:()=>S("groups"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Groups"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:k.court,onChange:()=>S("court"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Court"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:k.clinic,onChange:()=>S("clinic"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Clinic"})]})]})]}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Notification"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>x(!h),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${h?"bg-blue-500":"bg-gray-200"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out ${h?"translate-x-6":"translate-x-1"}`})}),e.jsx("span",{className:"text-sm",children:h?"Published":"Not published"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{onMouseEnter:()=>c(!0),onMouseLeave:()=>c(!1),className:"inline-block cursor-help",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M8.95768 9.16602H9.99934L9.99935 13.541M17.7077 9.99935C17.7077 14.2565 14.2565 17.7077 9.99935 17.7077C5.74215 17.7077 2.29102 14.2565 2.29102 9.99935C2.29102 5.74215 5.74215 2.29102 9.99935 2.29102C14.2565 2.29102 17.7077 5.74215 17.7077 9.99935ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10.0007 6.125C9.7015 6.125 9.45898 6.36751 9.45898 6.66667C9.45898 6.96582 9.7015 7.20833 10.0007 7.20833C10.2998 7.20833 10.5423 6.96582 10.5423 6.66667C10.5423 6.36751 10.2998 6.125 10.0007 6.125Z",fill:"#868C98",stroke:"#868C98",strokeWidth:"0.25"})]})}),d&&e.jsx("div",{className:"absolute bottom-full left-[-50px] mb-2 w-[230px]",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"rounded-lg bg-white p-3 text-sm text-gray-900 shadow-lg",children:"If button is OFF and save changes is pressed, any changes will be saved as a draft. If button is ON and save changed is pressed, changes will be published to the Home Screen."}),e.jsx("div",{className:"absolute left-[40px] top-full h-2 w-2 -translate-y-1/2 rotate-45 transform bg-white shadow-lg"})]})})]})]})]}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Image (optional)"}),e.jsx("p",{className:"mb-2 text-sm text-gray-500",children:"JPEG, PNG formats, up to 50 MB."}),e.jsxs("div",{className:"relative h-[200px] w-full rounded-xl border-2 border-dashed border-gray-300 hover:border-gray-400",children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*",onChange:w,id:"file-input"}),e.jsx("label",{htmlFor:"file-input",className:"absolute inset-0 cursor-pointer",children:p?e.jsxs("div",{className:"relative h-full w-full",children:[e.jsx("img",{src:p.url,alt:"Upload preview",className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:E=>{E.preventDefault(),V()},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627Z",fill:"black"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Title"}),e.jsx("input",{value:_,onChange:E=>B(E.target.value),type:"text",placeholder:"Enter title...",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Content"}),e.jsx("textarea",{value:v,onChange:E=>O(E.target.value),rows:4,placeholder:"Enter content...",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(Z,{loading:o,onClick:U,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white",children:"Save changes"})})]})}),e.jsx(fe,{isOpen:C,onClose:()=>N(!1),title:"Preview",children:e.jsx(us,{clubName:(K=i==null?void 0:i.club)==null?void 0:K.name,image:(Y=i==null?void 0:i.club)==null?void 0:Y.home_image,description:(g=i==null?void 0:i.club)==null?void 0:g.description,title:(M=i==null?void 0:i.club)==null?void 0:M.title})})]})}function ys({club:s}){var o;const[i,y]=a.useState(!1),t=[{value:"first_name",label:"First Name",type:"text",required:!0},{value:"last_name",label:"Last Name",type:"text",required:!0},{value:"email",label:"Email",type:"email",required:!0},{value:"password",label:"Password",type:"password",required:!0},{value:"phone",label:"Phone Number",type:"tel",required:!0},{value:"gender",label:"Gender",type:"select",required:!0,options:["Male","Female","Other"]},{value:"ntrp",label:"NTRP Rating",type:"select",required:!0,options:["2.0","2.5","3.0","3.5","4.0","4.5","5.0","5.5","6.0","6.5","7.0"]}],j=l=>{switch(l.type){case"select":return e.jsxs("select",{className:"w-full rounded-xl border border-zinc-200 px-3 py-2 text-gray-700 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[e.jsxs("option",{value:"",children:["Select ",l.label]}),l.options.map(m=>e.jsx("option",{value:m,children:m},m))]});case"password":return e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:i?"text":"password",className:"w-full rounded-xl border border-zinc-200 px-3 py-2 text-gray-700 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:l.label}),e.jsx("button",{type:"button",className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>y(!i),children:i?e.jsx(es,{className:"h-5 w-5"}):e.jsx(ss,{className:"h-5 w-5"})})]});default:return e.jsx("input",{type:l.type,className:"w-full rounded-xl border border-zinc-200 px-3 py-2 text-gray-700 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:l.label})}};return e.jsx(ts,{children:e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"mx-auto max-w-2xl rounded-xl border border-gray-200 p-6",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[t.map(l=>e.jsxs("div",{className:"space-y-1",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:[l.label,l.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),j(l)]},l.value)),(o=JSON.parse(s==null?void 0:s.custom_fields))==null?void 0:o.map(l=>e.jsxs("div",{className:"space-y-1",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:[l.label,l.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),j(l)]},l.value))]})})})})}function js({club:s}){const i=JSON.parse((s==null?void 0:s.membership_settings)||"[]");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"mb-5 text-center text-2xl font-medium text-gray-900",children:"Membership Plans"}),e.jsx("p",{className:"text-center text-gray-600",children:"Choose a plan that works best for you"})]}),e.jsx("div",{className:"mt-4 grid grid-cols-1 gap-4 md:grid-cols-3",children:i==null?void 0:i.map((y,t)=>e.jsx(as,{...y,popular:t===1,onSelect:()=>{},isCurrentPlan:!1,isActive:!1},y.plan_name))}),e.jsx("div",{className:"mt-8 rounded-lg bg-gray-50 p-4",children:e.jsx("p",{className:"text-center text-sm text-gray-600",children:"All plans include access to basic facilities. Additional features vary by plan. Contact club management for more details."})})]})}function fs({club:s,sports:i,pricing:y,courts:t,isOpen:j,onClose:o}){const[l,m]=a.useState("Sports"),_=p=>{try{const C=JSON.parse(p||"{}").images||[];let N=new Array(9).fill(null);return C.forEach((D,L)=>{D&&D.url&&(N[L]={url:D.url,isDefault:!0,id:D.id||`default-${L}`,type:D.type||"image"})}),N}catch(r){return console.error("Error parsing splash screen:",r),new Array(9).fill(null)}},B=({sport:p,type:r,subType:C})=>{console.log({sport:p,type:r,subType:C})},v=[{title:"Sports",path:"/sports"},{title:"Prices",path:"/prices"},{title:"Home splash screen",path:"/home-splash"},{title:"Home_Logged-in",path:"/home-logged-in"},{title:"Court booking description",path:"/court-booking"},{title:"Lesson booking description",path:"/lesson-booking"},{title:"Find a Buddy booking description",path:"/find-buddy"},{title:"Clinic description",path:"/clinic"},{title:"Custom request threshold",path:"/custom-request"},{title:"Membership and modules",path:"/membership"},{title:"Custom signup form",path:"/custom-signup-form"}],O=()=>{switch(l){case"Sports":return e.jsx("div",{className:"mx-auto max-w-xl",children:e.jsx(is,{sports:i,onSelectionChange:B,isChildren:!1})});case"Custom signup form":return e.jsx(ys,{club:s});case"Membership and modules":return e.jsx(js,{club:s});case"Home splash screen":const p=JSON.parse((s==null?void 0:s.splash_screen)||"{}");return e.jsx("div",{className:"h-full",children:e.jsx(be,{clubName:s==null?void 0:s.name,description:p==null?void 0:p.bio,imageList:_(s==null?void 0:s.splash_screen),clubLogo:s==null?void 0:s.club_logo,slideshowDelay:(p==null?void 0:p.slideshow_delay)||5e3})});case"Home_Logged-in":return e.jsxs("div",{className:"mx-auto max-w-7xl space-y-6 p-3 md:p-6",children:[s!=null&&s.home_image?e.jsx("div",{className:"",children:e.jsxs("div",{className:"flex flex-col gap-4 md:flex-row md:gap-10",children:[e.jsx("div",{className:"relative mb-4 flex-1 overflow-hidden rounded-xl shadow-md transition-all duration-300 hover:shadow-lg md:mb-6",children:e.jsx("div",{className:"relative h-[200px] w-full overflow-hidden md:h-[300px]",children:e.jsx("img",{src:s==null?void 0:s.home_image,alt:"Club Home Image",className:"h-full w-full object-cover transition-transform duration-700 hover:scale-105",loading:"lazy"})})}),e.jsxs("div",{className:"flex-1",children:[(s==null?void 0:s.title)&&e.jsx("h1",{className:"mb-2 text-2xl font-bold tracking-tight md:text-3xl",children:s==null?void 0:s.title}),(s==null?void 0:s.description)&&e.jsx("p",{className:"max-w-2xl text-base md:text-lg",children:s==null?void 0:s.description})]})]})}):e.jsxs("div",{className:"to-navy-900 mb-6 rounded-xl p-4 md:p-6",children:[(s==null?void 0:s.title)&&e.jsx("h1",{className:"mb-2 text-2xl font-bold tracking-tight text-white md:text-3xl",children:s==null?void 0:s.title}),(s==null?void 0:s.description)&&e.jsx("p",{className:"max-w-2xl text-base text-gray-200 md:text-lg",children:s==null?void 0:s.description})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"h-fit rounded-2xl bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"mb-6 flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.75 3C3.7835 3 3 3.7835 3 4.75V19.25C3 20.2165 3.7835 21 4.75 21H19.25C20.2165 21 21 20.2165 21 19.25V4.75C21 3.7835 20.2165 3 19.25 3H4.75ZM8 11C7.44772 11 7 11.4477 7 12C7 12.5523 7.44772 13 8 13C8.55228 13 9 12.5523 9 12C9 11.4477 8.55228 11 8 11ZM8 15C7.44772 15 7 15.4477 7 16C7 16.5523 7.44772 17 8 17C8.55228 17 9 16.5523 9 16C9 15.4477 8.55228 15 8 15ZM11 16C11 15.4477 11.4477 15 12 15C12.5523 15 13 15.4477 13 16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16ZM12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11ZM15 12C15 11.4477 15.4477 11 16 11C16.5523 11 17 11.4477 17 12C17 12.5523 16.5523 13 16 13C15.4477 13 15 12.5523 15 12ZM4.75 4.5C4.61193 4.5 4.5 4.61193 4.5 4.75V7H19.5V4.75C19.5 4.61193 19.3881 4.5 19.25 4.5H4.75Z",fill:"#176448"})}),e.jsx("h2",{className:"text-lg font-medium",children:"Upcoming reservations"})]}),e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsxs("svg",{width:"60",height:"60",viewBox:"0 0 60 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",fill:"url(#paint0_linear_158_11396)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",stroke:"url(#paint1_linear_158_11396)"}),e.jsxs("g",{filter:"url(#filter0_d_158_11396)",children:[e.jsx("rect",{x:"8",y:"8",width:"44",height:"44",rx:"22",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"43",height:"43",rx:"21.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M20 30C20 27.2819 21.0844 24.8171 22.8443 23.0146C25.0536 24.5496 26.5 27.1059 26.5 30C26.5 32.8941 25.0536 35.4504 22.8443 36.9854C21.0844 35.1829 20 32.7181 20 30Z",fill:"#868C98"}),e.jsx("path",{d:"M28 30C28 26.7284 26.4289 23.8237 24 21.9993C25.6713 20.7439 27.7488 20 30 20C32.2512 20 34.3287 20.7439 36 21.9993C33.5711 23.8237 32 26.7284 32 30C32 33.2716 33.5711 36.1763 36 38.0007C34.3287 39.2561 32.2512 40 30 40C27.7488 40 25.6713 39.2561 24 38.0007C26.4289 36.1763 28 33.2716 28 30Z",fill:"#868C98"}),e.jsx("path",{d:"M37.1557 23.0146C38.9156 24.8171 40 27.2819 40 30C40 32.7181 38.9156 35.1829 37.1557 36.9854C34.9464 35.4504 33.5 32.8941 33.5 30C33.5 27.1059 34.9464 24.5496 37.1557 23.0146Z",fill:"#868C98"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_158_11396",x:"4",y:"6",width:"52",height:"52",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[e.jsx("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_158_11396"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_158_11396",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_158_11396",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7",stopOpacity:"0.48"}),e.jsx("stop",{offset:"1",stopColor:"#F7F8F8",stopOpacity:"0"}),e.jsx("stop",{offset:"1",stopColor:"#E4E5E7",stopOpacity:"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_158_11396",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7"}),e.jsx("stop",{offset:"0.765625",stopColor:"#E4E5E7",stopOpacity:"0"})]})]})]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"You have no upcoming reservations"}),e.jsx("span",{className:"font-medium text-blue-600 hover:text-blue-700",children:"Reserve a court"})]})]}),e.jsxs("div",{className:"max-h-fit rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("div",{className:"mb-6 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12 2C9.51472 2 7.5 4.01472 7.5 6.5C7.5 8.98528 9.51472 11 12 11C14.4853 11 16.5 8.98528 16.5 6.5C16.5 4.01472 14.4853 2 12 2Z",fill:"#176448"}),e.jsx("path",{d:"M16.5081 13.8263C16.1908 14.2141 16.0005 14.7098 16.0005 15.25V16H15.2505C14.0078 16 13.0005 17.0074 13.0005 18.25C13.0005 19.4926 14.0078 20.5 15.2505 20.5H16.0005V21H5.59881C4.60008 21 3.69057 20.1119 3.9402 19.0012C4.7686 15.3152 8.21185 12.5 12.0004 12.5C13.6638 12.5 15.2115 12.9805 16.5081 13.8263Z",fill:"#176448"}),e.jsx("path",{d:"M19 15.25C19 14.8358 18.6642 14.5 18.25 14.5C17.8358 14.5 17.5 14.8358 17.5 15.25V17.5H15.25C14.8358 17.5 14.5 17.8358 14.5 18.25C14.5 18.6642 14.8358 19 15.25 19H17.5V21.25C17.5 21.6642 17.8358 22 18.25 22C18.6642 22 19 21.6642 19 21.25V19H21.25C21.6642 19 22 18.6642 22 18.25C22 17.8358 21.6642 17.5 21.25 17.5H19V15.25Z",fill:"#176448"})]}),e.jsx("h2",{className:"text-lg font-medium",children:"Open requests"})]})}),e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsxs("svg",{width:"60",height:"60",viewBox:"0 0 60 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",fill:"url(#paint0_linear_187_35666)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",stroke:"url(#paint1_linear_187_35666)"}),e.jsxs("g",{filter:"url(#filter0_d_187_35666)",children:[e.jsx("rect",{x:"8",y:"8",width:"44",height:"44",rx:"22",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"43",height:"43",rx:"21.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M30 20C27.5147 20 25.5 22.0147 25.5 24.5C25.5 26.9853 27.5147 29 30 29C32.4853 29 34.5 26.9853 34.5 24.5C34.5 22.0147 32.4853 20 30 20Z",fill:"#868C98"}),e.jsx("path",{d:"M34.5081 31.8263C34.1908 32.2141 34.0005 32.7098 34.0005 33.25V34H33.2505C32.0078 34 31.0005 35.0074 31.0005 36.25C31.0005 37.4926 32.0078 38.5 33.2505 38.5H34.0005V39H23.5988C22.6001 39 21.6906 38.1119 21.9402 37.0012C22.7686 33.3152 26.2118 30.5 30.0004 30.5C31.6638 30.5 33.2115 30.9805 34.5081 31.8263Z",fill:"#868C98"}),e.jsx("path",{d:"M37 33.25C37 32.8358 36.6642 32.5 36.25 32.5C35.8358 32.5 35.5 32.8358 35.5 33.25V35.5H33.25C32.8358 35.5 32.5 35.8358 32.5 36.25C32.5 36.6642 32.8358 37 33.25 37H35.5V39.25C35.5 39.6642 35.8358 40 36.25 40C36.6642 40 37 39.6642 37 39.25V37H39.25C39.6642 37 40 36.6642 40 36.25C40 35.8358 39.6642 35.5 39.25 35.5H37V33.25Z",fill:"#868C98"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_187_35666",x:"4",y:"6",width:"52",height:"52",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[e.jsx("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_187_35666"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_187_35666",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_187_35666",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7",stopOpacity:"0.48"}),e.jsx("stop",{offset:"1",stopColor:"#F7F8F8",stopOpacity:"0"}),e.jsx("stop",{offset:"1",stopColor:"#E4E5E7",stopOpacity:"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_187_35666",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7"}),e.jsx("stop",{offset:"0.765625",stopColor:"#E4E5E7",stopOpacity:"0"})]})]})]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"There are no open requests"}),e.jsx("span",{className:"font-medium text-blue-600 hover:text-blue-700",children:"Make request"})]})]})]})]});case"Court booking description":const r=s!=null&&s.court_description?JSON.parse(s==null?void 0:s.court_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-blue-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-blue-900",children:"Court Booking Process"}),e.jsx("p",{className:"text-blue-800",children:"Users can reserve courts by selecting their sport, date, time, and players. The system supports advance booking limits, court selection, and find-a-buddy features."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Step 1: Select Date & Time"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Choose sport, type, and subtype"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Select available date within booking limits"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Pick time slots and court (if enabled)"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Step 2: Add Players"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Select players from club members"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Add family members if available"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Enable find-a-buddy for additional players"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Reservation Details"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• 15-minute payment window after reservation"}),e.jsx("p",{children:"• Automatic court assignment or user selection"}),e.jsx("p",{children:"• Service fees and club fees calculated automatically"}),e.jsx("p",{children:"• Email confirmations and calendar integration"})]})]}),r.reservation_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Reservation Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:r.reservation_description})]}),r.payment_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Payment Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:r.payment_description})]})]});case"Lesson booking description":const C=s!=null&&s.lesson_description?JSON.parse(s==null?void 0:s.lesson_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-purple-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-purple-900",children:"Lesson Booking Process"}),e.jsx("p",{className:"text-purple-800",children:"Users can book lessons with coaches through multiple search methods: by coach, by time availability, or through custom requests."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Find by Coach"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-purple-500"}),e.jsx("span",{children:"Browse available coaches"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-purple-500"}),e.jsx("span",{children:"View coach profiles and rates"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-purple-500"}),e.jsx("span",{children:"Select available time slots"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Find by Time"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Choose preferred date and time"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"System finds available coaches"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Compare options and book"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Custom Request"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Submit specific requirements"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Coaches respond to requests"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Choose from responses"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Lesson Features"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• Hourly rate-based pricing"}),e.jsx("p",{children:"• Multiple player support"}),e.jsx("p",{children:"• Advance booking limits"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• Coach profile and bio viewing"}),e.jsx("p",{children:"• Flexible time slot selection"}),e.jsx("p",{children:"• Automatic fee calculation"})]})]})]}),C.reservation_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Reservation Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:C.reservation_description})]}),C.payment_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Payment Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:C.payment_description})]})]});case"Find a Buddy booking description":return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-orange-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-orange-900",children:"Find a Buddy Process"}),e.jsx("p",{className:"text-orange-800",children:"Users can create requests to find playing partners with similar skill levels and availability. The system matches players based on NTRP ratings and preferences."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Step 1: Create Request"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-orange-500"}),e.jsx("span",{children:"Select sport, type, and subtype"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-orange-500"}),e.jsx("span",{children:"Choose date and time slots"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-orange-500"}),e.jsx("span",{children:"Set NTRP skill level range"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-orange-500"}),e.jsx("span",{children:"Specify number of players needed"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Step 2: Player Matching"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Request appears in open requests"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Other players can join the request"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Automatic skill level filtering"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Real-time request updates"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Request Features"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• NTRP skill level matching (min/max range)"}),e.jsx("p",{children:"• Multiple time slot support"}),e.jsx("p",{children:"• Player count flexibility"}),e.jsx("p",{children:"• Custom notes and requirements"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• Advance booking limits based on membership"}),e.jsx("p",{children:"• Request visibility controls"}),e.jsx("p",{children:"• Automatic court booking integration"}),e.jsx("p",{children:"• Email notifications for matches"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-blue-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-blue-900",children:"How It Works"}),e.jsxs("div",{className:"space-y-2 text-sm text-blue-800",children:[e.jsx("p",{children:"1. Create a buddy request with your preferred date, time, and skill level"}),e.jsx("p",{children:'2. Other club members see your request in the "Open Requests" section'}),e.jsx("p",{children:"3. Players with matching skill levels can join your request"}),e.jsx("p",{children:"4. Once enough players join, the system can automatically book a court"}),e.jsx("p",{children:"5. All participants receive confirmation and payment instructions"})]})]})]});case"Clinic description":const N=s!=null&&s.clinic_description?JSON.parse(s==null?void 0:s.clinic_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-indigo-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-indigo-900",children:"Clinic Booking Process"}),e.jsx("p",{className:"text-indigo-800",children:"Users can join group clinics led by professional coaches. Clinics are pre-scheduled events with fixed dates, times, and participant limits."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Clinic Discovery"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-indigo-500"}),e.jsx("span",{children:"Browse available clinics by sport"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-indigo-500"}),e.jsx("span",{children:"Filter by date, time, and skill level"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-indigo-500"}),e.jsx("span",{children:"View clinic details and coach info"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-indigo-500"}),e.jsx("span",{children:"Check available slots remaining"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Booking Process"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Select players to register"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Review cost per participant"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Complete payment within 15 minutes"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Receive confirmation and details"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Clinic Features"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• Fixed cost per participant"}),e.jsx("p",{children:"• Limited slots per clinic"}),e.jsx("p",{children:"• Professional coach instruction"}),e.jsx("p",{children:"• Group learning environment"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• Advance booking restrictions"}),e.jsx("p",{children:"• Multiple player registration"}),e.jsx("p",{children:"• Automatic service fee calculation"}),e.jsx("p",{children:"• Calendar integration"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-yellow-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-yellow-900",children:"Clinic Types"}),e.jsxs("div",{className:"space-y-2 text-sm text-yellow-800",children:[e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Beginner Clinics:"})," Introduction to basic techniques and rules"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Intermediate Clinics:"})," Skill development and strategy"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Advanced Clinics:"})," Competitive play and advanced techniques"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Specialty Clinics:"})," Focus on specific skills (serving, volleys, etc.)"]})]})]}),N.reservation_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Reservation Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:N.reservation_description})]}),N.payment_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Payment Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:N.payment_description})]})]});default:return e.jsxs("div",{children:[e.jsx("h2",{className:"mb-2 text-lg font-semibold",children:l}),e.jsxs("p",{className:"text-gray-600",children:["Content for ",l," will be displayed here"]})]})}};return e.jsx(fe,{isOpen:j,onClose:o,title:"Preview",children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx("h1",{className:"mb-4 text-xl font-bold",children:s==null?void 0:s.name}),e.jsx("div",{className:"relative",children:e.jsx("div",{className:"scrollbar-hide overflow-x-auto",children:e.jsx("div",{className:"flex space-x-4 border-b border-gray-200 px-4",children:v.map(p=>e.jsx("button",{onClick:()=>m(p.title),className:`whitespace-nowrap px-1 py-2 text-sm transition-colors duration-200 ${l===p.title?"border-b-2 border-blue-600 font-medium text-blue-600":"text-gray-500 hover:text-gray-700"}`,children:p.title},p.title))})})}),e.jsx("div",{className:"mt-3 flex-1 overflow-y-auto bg-white p-4",children:O()})]})})}new se;function Es({selectedClub:s,profileSettings:i,fetchSettings:y,club:t,courts:j,sports:o,pricing:l,clubUser:m}){const _=ze(),[B,v]=a.useState(!1),[O,p]=a.useState(!1),[r,C]=a.useState(!1),[N,D]=a.useState(!1),[L,k]=a.useState(!1),[H,h]=a.useState(!1),[x,d]=a.useState(!1),[c,w]=a.useState(!1),[V,T]=a.useState(!1),[U,S]=a.useState(!1),[J,$]=a.useState(!1),[q,G]=a.useState(!1),{dispatch:te}=He.useContext(de),[z,K]=a.useState(o),[Y,g]=a.useState(""),[M,E]=a.useState(null),P=localStorage.getItem("role"),[me,he]=a.useState([]),xe=[{title:"Sports",path:"/sports"},{title:"Prices",path:"/prices"},{title:"Home splash screen",path:"/home-splash"},{title:"Home logged in",path:"/home-logged-in"},{title:"Court booking",path:"/court-booking"},{title:"Lesson booking",path:"/lesson-booking"},{title:"Find a Buddy booking",path:"/find-buddy"},{title:"Clinic",path:"/clinic"},{title:"Custom request threshold",path:"/custom-request"},{title:"Membership and modules",path:"/membership"},{title:"Custom signup form",path:"/custom-signup-form"}],le=(F,I)=>{F.preventDefault(),I==="/sports"?_(`/${P}/club-ui/sports`,{state:{clubId:s==null?void 0:s.id}}):I==="/prices"?_(`/${P}/club-ui/pricing`,{state:{clubId:s==null?void 0:s.id}}):I==="/surface-types"?setIsSurfaceModalOpen(!0):I==="/court-booking"?h(!0):I==="/custom-request"?d(!0):I==="/home-splash"?C(!0):I==="/lesson-booking"?w(!0):I==="/membership"?_(`/${P}/club-ui/membership`,{state:{clubId:s==null?void 0:s.id}}):I==="/coach-booking"?T(!0):I==="/clinic"?S(!0):I==="/find-buddy"?$(!0):I==="/home-logged-in"?G(!0):I==="/custom-signup-form"&&_(`/${P}/club-ui/custom-signup-form`,{state:{clubId:s==null?void 0:s.id}})},W=()=>{Y.trim()&&(K([...z,{id:z.length+1,label:`Sport ${z.length+1}`,name:Y.trim()}]),g(""))};He.useEffect(()=>{te({type:"SETPATH",payload:{path:"club-ui"}})},[]),a.useState(()=>{if(t!=null&&t.splash_screen)try{const F=JSON.parse(t.splash_screen);if(F.images&&Array.isArray(F.images)){const I=new Array(9).fill(null);return F.images.forEach((ee,ge)=>{ee&&(I[ge]={url:ee.url,isDefault:!0,id:`default-${Date.now()}-${Math.random()}`,type:ee.type||"image"})}),I}}catch(F){console.error("Error parsing splash screen data:",F)}return new Array(9).fill(null)});const ve=a.useCallback(F=>{E(()=>F)},[]),ae=t!=null&&t.splash_screen?JSON.parse(t==null?void 0:t.splash_screen):null,ue=F=>{he(F)};return e.jsxs("div",{className:"h-screen",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h1",{className:"text-xl font-semibold",children:s?s==null?void 0:s.name:"--"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("button",{onClick:()=>k(!0),className:"flex items-center gap-2 rounded-lg border border-gray-200 p-2",children:[e.jsx("span",{children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M13.1251 5.41602C13.1251 7.14191 11.726 8.54102 10.0001 8.54102C8.27419 8.54102 6.87508 7.14191 6.87508 5.41602C6.87508 3.69013 8.27419 2.29102 10.0001 2.29102C11.726 2.29102 13.1251 3.69013 13.1251 5.41602Z",stroke:"#868C98","stroke-width":"1.5","stroke-linejoin":"round"}),e.jsx("path",{d:"M10.0001 11.041C6.83708 11.041 4.52834 13.1439 3.89304 15.9708C3.78537 16.4499 4.17439 16.8743 4.66543 16.8743H15.3347C15.8258 16.8743 16.2148 16.4499 16.1071 15.9708C15.4718 13.1439 13.1631 11.041 10.0001 11.041Z",stroke:"#868C98","stroke-width":"1.5","stroke-linejoin":"round"})]})}),e.jsx("span",{children:"User perspective"})]}),e.jsx(rs,{title:"Club UI History",emptyMessage:"No club UI history found",activityType:re.club_ui})]})]}),e.jsx("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4",children:xe.map((F,I)=>e.jsxs(We,{to:F.path,onClick:ee=>le(ee,F.path),className:"flex items-center justify-between rounded-xl bg-white p-4 shadow-3 transition-shadow duration-200",children:[e.jsx("span",{className:"text-gray-800",children:F.title}),e.jsx("button",{className:"",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]},I))}),e.jsx(X,{isOpen:B,onClose:()=>v(!1),title:"Sports",showFooter:!1,children:e.jsx(Ke,{onClose:()=>v(!1),fetchSettings:y,sports:o,club:t})}),e.jsx(X,{isOpen:H,onClose:()=>h(!1),title:"Court booking",onPrimaryAction:W,showFooter:!1,children:e.jsx(ls,{onClose:()=>h(!1),fetchSettings:y,clubUser:m,club:t})}),e.jsx(X,{isOpen:x,onClose:()=>d(!1),title:"Custom request threshold",onPrimaryAction:W,showFooter:!1,children:e.jsx(os,{onClose:()=>d(!1),fetchSettings:y,clubUser:m,club:t,sports:o})}),e.jsx(X,{isOpen:O,onClose:()=>p(!1),title:"Pricing",showFooter:!1,children:e.jsx(ds,{pricing:l,clubUser:m,club:t})}),e.jsx(X,{isOpen:r,onClose:()=>C(!1),title:"Home splash screen",showFooter:!1,children:e.jsx(cs,{fetchSettings:y,clubUser:m,setShowSplashScreenPreview:D,onImageListChange:ue,setPreviewImageList:he,club:t})}),e.jsx(X,{isOpen:c,onClose:()=>w(!1),title:"Lesson booking",showFooter:!1,children:e.jsx(ms,{fetchSettings:y,clubUser:m,club:t})}),e.jsx(X,{isOpen:V,onClose:()=>T(!1),title:"Coach booking",showFooter:!1,children:e.jsx(hs,{fetchSettings:y,clubUser:m,club:t})}),e.jsx(X,{isOpen:U,onClose:()=>S(!1),title:"Clinic booking",showFooter:!1,children:e.jsx(ps,{fetchSettings:y,xclubUser:m,club:t})}),e.jsx(X,{isOpen:J,onClose:()=>$(!1),title:"Find a buddy booking",showFooter:!1,children:e.jsx(xs,{fetchSettings:y,clubUser:m,club:t})}),e.jsx(gs,{fetchSettings:y,profileSettings:i,onSubmit:ve,club:t,isOpen:q,onClose:()=>G(!1)}),e.jsx(fe,{isOpen:N,onClose:()=>D(!1),title:"Preview",children:e.jsx(be,{clubName:t==null?void 0:t.name,description:ae==null?void 0:ae.bio,imageList:me,clubLogo:t==null?void 0:t.club_logo,slideshowDelay:(ae==null?void 0:ae.slideshow_delay)||5e3,club:t})}),e.jsx(fs,{club:t,isOpen:L,onClose:()=>k(!1),sports:z,pricing:l,courts:j})]})}export{Es as C};
