import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as F}from"./vendor-851db8c1.js";import{u as I}from"./react-hook-form-687afde5.js";import{o as P}from"./yup-2824f222.js";import{c as q,a as o}from"./yup-54691517.js";import{A as D,G as L,M,b as V,t as $}from"./index-a0784e19.js";import{D as G}from"./index-855a436e.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Ce=({setSidebar:l})=>{var g,h,b;const j=q({page:o().required(),key:o().required(),type:o().required(),value:o()}).required(),n=[{key:"text",value:"Text"},{key:"image",value:"Image"},{key:"number",value:"Number"},{key:"kvp",value:"Key-Value Pair"},{key:"image-list",value:"Image List"},{key:"captioned-image-list",value:"Captioned Image List"},{key:"team-list",value:"Team List"}],{dispatch:C}=a.useContext(D),{dispatch:m}=a.useContext(L),[N,w]=a.useState((g=n[0])==null?void 0:g.key),[E,S]=a.useState(""),[c,d]=a.useState(!1),T=F(),{register:r,handleSubmit:p,setError:u,formState:{errors:x},reset:A}=I({resolver:P(j)}),y=async t=>{let f=new M;d(!0),console.log(t);try{f.setTable("cms");const s=await f.cmsAdd(t.page,t.key,t.type,E);if(!s.error)T("/admin/cms"),V(m,"Added"),A();else if(s.validation){const v=Object.keys(s.validation);for(let i=0;i<v.length;i++){const k=v[i];u(k,{type:"manual",message:s.validation[k]})}}}catch(s){console.log("Error",s),u("page",{type:"manual",message:s.message}),$(C,s.message)}d(!1)};return a.useEffect(()=>{m({type:"SETPATH",payload:{path:"cms"}})},[]),e.jsxs("div",{className:"rounded mx-auto",children:[e.jsxs("div",{className:"flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Add CMS Content"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>l(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center py-2 px-3 text-white bg-[#4F46E5] rounded-md shadow-sm",onClick:async()=>{await p(y)(),l(!1)},disabled:c,children:c?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full p-4 text-left",onSubmit:p(y),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"page",children:"Page"}),e.jsx("input",{type:"text",placeholder:"Page",...r("page"),className:`shadow appearance-none border rounded w-full mb-3 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
}`})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"key",children:"Content Identifier"}),e.jsx("input",{type:"text",placeholder:"Content Identifier",...r("key"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=x.key)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(b=x.key)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Content Type"}),e.jsx("select",{name:"type",id:"type",className:"shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...r("type",{onChange:t=>w(t.target.value)}),children:n.map(t=>e.jsx("option",{name:t.name,value:t.key,children:t.value},t.key))})]}),e.jsx(G,{contentType:N,setContentValue:S})]})]})};export{Ce as default};
