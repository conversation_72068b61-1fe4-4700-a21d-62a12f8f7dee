import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{a3 as v,a4 as u}from"./index-a0784e19.js";import{f as h}from"./date-fns-07266b7d.js";function M({selectedSport:x,sports:r,selectedType:n,selectedSubType:l,selectedDate:t,playersNeeded:j,selectedCoach:e,timeRange:d}){var m,a,i,y;return console.log("selectedCoach",e),s.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[s.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:s.jsx("h2",{className:"text-base font-medium",children:"Summary"})}),s.jsxs("div",{className:"divide-y p-4",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"DATE"}),s.jsxs("div",{className:"text-sm font-medium",children:[t&&h(t,"MMMM d, yyyy")," ",v(d==null?void 0:d.start_time)," -"," ",v(d==null?void 0:d.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"Coach"}),e?s.jsxs("div",{className:"flex items-center gap-2 text-sm font-medium",children:[s.jsx("img",{src:(e==null?void 0:e.photo)||((m=e==null?void 0:e.user)==null?void 0:m.photo)||"/default-avatar.png",className:"h-6 w-6 rounded-full"}),s.jsxs("div",{children:[(e==null?void 0:e.first_name)||((a=e==null?void 0:e.user)==null?void 0:a.first_name)," ",(e==null?void 0:e.last_name)||((i=e==null?void 0:e.user)==null?void 0:i.last_name)]})]}):s.jsx("div",{className:"text-sm font-medium",children:"No coach selected"})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm uppercase text-gray-500",children:"Hours selected"}),s.jsxs("div",{className:"flex items-center gap-2 text-sm font-medium",children:[d==null?void 0:d.duration," h"]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"SPORT"}),s.jsxs("div",{className:"text-sm font-medium",children:[x&&((y=r==null?void 0:r.find(N=>N.id===x))==null?void 0:y.name),n&&` • ${n}`,l&&` • (${l})`]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"NUMBER OF PLAYERS"}),s.jsx("div",{className:"text-sm",children:j}),s.jsxs("div",{className:"mt-3 flex items-start gap-2 rounded-lg bg-gray-100 p-2",children:[s.jsx(u,{className:"h-4 w-4 text-gray-500"}),s.jsx("p",{className:"text-xs text-gray-500",children:"If you want more than 4 players, use the custom request feature."})]})]})]})]})}const T=({selectedSport:x,sports:r,selectedType:n,selectedSubType:l,selectedDate:t,selectedTimes:j,selectedCourt:e})=>{var d;return s.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[s.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:s.jsx("h2",{className:"text-base font-medium",children:"Summary"})}),s.jsxs("div",{className:"divide-y p-4",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"SPORT"}),s.jsxs("div",{className:"text-sm",children:[x&&((d=r==null?void 0:r.find(m=>m.id===x))==null?void 0:d.name),n&&` • ${n}`,l&&` • (${l})`]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"DATE"}),s.jsx("div",{className:"text-sm",children:t&&h(t,"MMMM d, yyyy")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"TIME"}),s.jsx("div",{className:"text-sm",children:j.map((m,a)=>{const i=new Date(`2000/01/01 ${m.from}`),N=(new Date(`2000/01/01 ${m.until}`)-i)/(1e3*60*60);return s.jsx("div",{className:"flex flex-col gap-1",children:s.jsxs("p",{children:[m.from," - ",m.until," (",N,"h)"]})},a)})})]}),e&&s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"COURT"}),s.jsx("div",{className:"text-sm",children:e.name})]})]})]})},w=({selectedSport:x,sports:r,selectedType:n,selectedSubType:l,selectedDate:t,selectedTimes:j,timeSlots:e})=>{var d;return console.log("timeSlots",e),s.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[s.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:s.jsx("h2",{className:"text-base font-medium",children:"Summary"})}),s.jsxs("div",{className:"divide-y p-4",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"SPORT"}),s.jsxs("div",{className:"text-sm",children:[x&&((d=r==null?void 0:r.find(m=>m.id===x))==null?void 0:d.name),n&&` • ${n}`,l&&` • ${l}`]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"DATE"}),s.jsx("div",{className:"text-sm",children:t&&h(t,"MMMM d, yyyy")})]}),s.jsxs("div",{className:"space-y-2 py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"TIMESLOTS"}),s.jsx("div",{className:"space-y-1 text-sm",children:e.map((m,a)=>{const i=new Date(`2000/01/01 ${m.from}`),N=(new Date(`2000/01/01 ${m.until}`)-i)/(1e3*60*60);return s.jsx("div",{className:"flex flex-col gap-1",children:s.jsxs("p",{children:[m.from," - ",m.until," (",N,"h)"]})},a)})})]})]})]})};function _({selectedSport:x,sports:r,selectedType:n,selectedSubType:l,selectedDate:t,selectedTimes:j,playersNeeded:e,selectedCoach:d,timeRange:m,clinic:a}){var i;return console.log("clinic",a),s.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[s.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:s.jsx("h2",{className:"text-base font-medium",children:"Summary"})}),s.jsxs("div",{className:"divide-y p-4",children:[s.jsx("p",{className:"py-3 text-sm",children:a==null?void 0:a.description}),s.jsxs("div",{className:"space-y-1 py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"DATE"}),s.jsxs("div",{className:"text-sm font-medium",children:[t&&h(t,"MMMM d, yyyy")," • ",v(a==null?void 0:a.start_time)," -"," ",v(a==null?void 0:a.end_time)]})]}),s.jsxs("div",{className:"space-y-1 py-3",children:[s.jsx("div",{className:"text-sm text-gray-500",children:"SPORT & TYPE"}),s.jsxs("div",{className:"text-sm font-medium",children:[(a==null?void 0:a.sport_id)&&((i=r==null?void 0:r.find(y=>y.id===(a==null?void 0:a.sport_id)))==null?void 0:i.name),(a==null?void 0:a.type)&&` • ${a==null?void 0:a.type}`,(a==null?void 0:a.sub_type)&&` • (${a==null?void 0:a.sub_type})`]})]}),s.jsxs("div",{className:"space-y-1 py-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"SLOTS REMAINING"}),s.jsxs("div",{className:"text-sm",children:[a==null?void 0:a.slots_remaining," (out of ",a==null?void 0:a.max_participants,")"]})]})]})]})}export{w as B,T as C,M as L,_ as a};
