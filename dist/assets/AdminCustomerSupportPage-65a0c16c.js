import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{T as ye,G as ve,M as je,b as E,I as Pe,A as he,au as $e,av as Ae,K as De,t as pe,w as Me}from"./index-a0784e19.js";import{r as N,b as i}from"./vendor-851db8c1.js";import{A as ge}from"./AdminTickets-fe3b90bc.js";import{S as re}from"./react-select-c8303602.js";import{d as Ie}from"./index.esm-b72032a7.js";import{f as Le,p as He}from"./index.esm-09a3a6b8.js";import{h as O}from"./moment-a9aaa855.js";import{I as Oe}from"./react-input-emoji-f9b52b09.js";import{$ as b}from"./@headlessui/react-a5400090.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./index-be4468eb.js";import"./yup-54691517.js";import"./AddButton.module-98aac587.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./FormattedPhoneNumber-40dd7178.js";let xe=new ye;const We=()=>{const[a,g]=N.useState(!1),[q,h]=N.useState([]),[d,M]=N.useState(null),[y,P]=N.useState(!0),{dispatch:$}=N.useContext(ve),[j,x]=N.useState([{from:{value:"09:00:00",label:"9:00 AM"},until:{value:"17:00:00",label:"5:00 PM"}}]),[k,o]=N.useState(!1),[W,A]=N.useState(null),C=new je,S=(()=>Array.from({length:24},(s,l)=>[0,30].map(n=>{const m=l.toString().padStart(2,"0"),p=n.toString().padStart(2,"0"),w=`${m}:${p}:00`,Y=l>=12?"PM":"AM",R=`${l===0?12:l>12?l-12:l}:${p==="00"?"00":n} ${Y}`;return{value:w,label:R}})).flat())(),B=s=>{if(!s||!s.value)return S;const l=s.value;return S.filter(n=>n.value>l)};N.useEffect(()=>{F(),ae()},[]);const F=async()=>{try{g(!0);const s=await xe.getList("clubs",{});s.list&&h(s.list)}catch(s){console.error("Error fetching clubs:",s),E("error","Failed to fetch clubs")}finally{g(!1)}},ae=async()=>{try{o(!0);const s=await xe.getOne("setting",5,{});if(console.log("result",s),s&&s.model){const l=s.model;if(A(l.id),l.setting_value)try{const m=JSON.parse(l.setting_value).map(p=>({from:S.find(w=>w.value===p.from)||{value:p.from,label:U(p.from)},until:S.find(w=>w.value===p.until)||{value:p.until,label:U(p.until)}}));x(m)}catch(n){console.error("Error parsing working hours:",n)}}else A(null)}catch(s){console.error("Error fetching global working hours:",s)}finally{o(!1)}},U=s=>{if(!s)return"";const[l,n]=s.split(":"),m=parseInt(l,10),p=m>=12?"PM":"AM";return`${m===0?12:m>12?m-12:m}:${n} ${p}`},le=async s=>{const l=s?s.value:null;if(M(l),l)try{l.supportchat_enabled!==void 0&&P(l.supportchat_enabled)}catch(n){console.error("Error parsing club support settings:",n),P(!1)}},G=(s,l,n)=>{x(m=>{const p=[...m];return p[s]={...p[s],[l]:n},p})},_=()=>{x(s=>[...s,{from:null,until:null}])},X=s=>{x(l=>l.filter((n,m)=>m!==s))},z=async()=>{if(!d){E("error","Please select a club first");return}try{g(!0),C.setTable("clubs"),await C.callRestAPI({id:d.id,supportchat_enabled:y?1:0},"PUT"),h(s=>s.map(l=>l.id===d.id?{...l,supportchat_enabled:y?1:0}:l)),M(s=>({...s,supportchat_enabled:y?1:0})),E($,"Club support settings saved successfully")}catch(s){console.error("Error saving club support settings:",s),E($,"Failed to save club support settings",3e3,"error")}finally{g(!1)}},D=async()=>{try{o(!0);const s=j.filter(n=>n.from&&n.until);if(s.length===0){E("error","Please add at least one valid working hour"),o(!1);return}const l=s.map(n=>({from:n.from.value,until:n.until.value}));C.setTable("setting"),await C.callRestAPI({setting_key:"support_working_hours",setting_value:JSON.stringify(l),id:W},"PUT"),E("success","Working hours saved successfully")}catch(s){console.error("Error saving working hours:",s),E("error","Failed to save working hours")}finally{o(!1)}};return e.jsxs("div",{className:"max-w-3xl",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Support Configuration"}),e.jsxs("div",{className:"mb-10 rounded-lg border border-gray-200 bg-white p-6 shadow-sm",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Support Chat Working Hours"}),e.jsx("p",{className:"mb-6 text-sm text-gray-500",children:"During these hours, users can chat with support. Outside these hours, they can only create tickets."}),j.map((s,l)=>e.jsxs("div",{className:"mb-4 flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsxs("div",{className:"w-1/2",children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"From"}),e.jsx(re,{className:"basic-single",classNamePrefix:"select",isLoading:k,isClearable:!0,isSearchable:!0,name:`from-${l}`,placeholder:"Select start time",value:s.from,onChange:n=>G(l,"from",n),options:S})]}),e.jsxs("div",{className:"w-1/2",children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Until"}),e.jsx(re,{className:"basic-single",classNamePrefix:"select",isLoading:k,isClearable:!0,isSearchable:!0,name:`until-${l}`,placeholder:"Select end time",value:s.until,onChange:n=>G(l,"until",n),options:B(s.from),isDisabled:!s.from})]})]}),j.length>1&&e.jsx("button",{onClick:()=>X(l),className:"flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 text-gray-500 hover:bg-gray-200",children:e.jsx(Pe,{})})]},l)),e.jsx("button",{onClick:_,className:"mt-2 text-primaryBlue underline hover:text-primaryBlue/80",children:"+ Add another time slot"}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{onClick:D,disabled:k,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/90 disabled:opacity-50",children:k?"Saving...":"Save Working Hours"})})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-white p-6 shadow-sm",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Club-Specific Settings"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Select Club"}),e.jsx(re,{className:"basic-single",classNamePrefix:"select",isLoading:a,isClearable:!0,isSearchable:!0,name:"club",placeholder:"Select a club",onChange:le,options:q.map(s=>({value:s,label:s.name})),value:d?{value:d,label:d.name}:null})]}),d&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:'Enable "Need Help" Support Chat Button'}),e.jsx("button",{type:"button",onClick:()=>P(!y),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${y==1?"bg-primaryBlue":"bg-gray-200"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${y==1?"translate-x-6":"translate-x-1"}`})})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"When disabled, users will only be able to create tickets and cannot chat with support."})]}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{onClick:z,disabled:a,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/90 disabled:opacity-50",children:a?"Saving...":"Save Club Settings"})})]})]})]})},fe=a=>a!=null&&a.first_name&&(a!=null&&a.last_name)?`${a==null?void 0:a.first_name} ${a.last_name}`:a!=null&&a.first_name||a!=null&&a.last_name?a!=null&&a.first_name?a==null?void 0:a.first_name:a==null?void 0:a.last_name:a==null?void 0:a.email;function be({activeTab:a}){const[g,q]=N.useState(""),h=new je,{state:d}=i.useContext(he),{dispatch:M}=i.useContext(he),[y,P]=i.useState([]),[$,j]=i.useState([]),[x,k]=i.useState(),o=i.useRef(),W=i.useRef(null),[A,C]=i.useState(""),[f,S]=i.useState(),[B,F]=i.useState(!1),[ae,U]=i.useState(null),[le,G]=i.useState(!1),[_,X]=i.useState(window.innerWidth),[z,D]=i.useState(!0),[s,l]=i.useState([]),[n,m]=i.useState("incoming"),[p,w]=i.useState(!1),[Y,K]=i.useState(!1),[R,Z]=i.useState(null),[Ne,ne]=i.useState(!1),[I,ie]=i.useState(!1),L=i.useRef(null),ee=i.useRef(null),H=()=>{ee.current&&ee.current.scrollIntoView({behavior:"smooth",block:"end"})},T=t=>{try{return t!=null&&t.first_name||t!=null&&t.last_name||t!=null&&t.email?fe(t):t!=null&&t.user?fe(t.user):`User ${(t==null?void 0:t.user_id)||(t==null?void 0:t.id)||"Unknown"}`}catch(r){return console.log("Error rendering room name:",r,t),`User ${(t==null?void 0:t.user_id)||(t==null?void 0:t.id)||"Unknown"}`}};function oe(t){t.currentTarget.innerWidth>1024&&D(!0),X(t.currentTarget.innerWidth)}const we=t=>{let r=O(new Date),c=O(t);return r.diff(c,"days")>1?O(c).format("Do MMMM"):O(c).format("hh:mm A")};async function J(t){try{if(t){const r=ce(y);return l(r.filter(c=>`${T(c).toLowerCase()}`.includes(t.toLowerCase())))}await te()}catch(r){console.log("Error:",r)}}const ce=t=>{let r=[];if(a==="open"){const c=t.filter(u=>u.resolved===0);n==="incoming"?r=c.filter(u=>u.other_user_id===0&&u.admin_id===0):n==="accepted"&&(r=c.filter(u=>u.other_user_id===d.user&&u.admin_id===d.user))}else r=t.filter(c=>c.resolved===1&&c.admin_id===d.user);return r};async function te(){try{const r=await new ye().getList("room",{join:["user|user_id"]});r&&r.list&&r.list[0]&&(console.log("Room data structure:",r.list[0]),P(r.list),se(r.list))}catch(t){console.log("Error:",t);try{h.setTable("room");const r=await h.callRestAPI({},"GETALL");r&&r.list&&r.list[0]&&(P(r.list),se(r.list))}catch(r){console.log("Fallback error:",r)}}}const se=t=>{const r=ce(t);l(r)};console.log("allRooms",y),i.useEffect(()=>{y.length>0&&se(y)},[a,n,y,d.user]),i.useEffect(()=>{m(a==="resolved"?"accepted":"incoming")},[a]);async function Q(t,r){try{V(),S(t),k(r);let c=new Date().toISOString().split("T")[0];const u=await h.getChats(t,r,c);u&&u.model&&(j(u.model.reverse()),setTimeout(()=>{H()},100))}catch(c){console.log("Error:",c)}}async function Se(t){try{w(!0),h.setTable("room"),await h.callRestAPI({id:t.id,admin_id:d.user,other_user_id:d.user},"PUT"),await te(),m("accepted"),Q(t.id,t.chat_id),o.current=t.user_id,o.currentRoom={...t,admin_id:d.user,other_user_id:d.user},_<1024&&D(!1)}catch(r){console.log("Error accepting chat:",r)}finally{w(!1)}}async function ke(){if(!A.trim())return;const t=A.trim(),r=new Date;try{C("");const c={chat:{user_id:d.user,message:t,date:r.toISOString().split("T")[0]},is_image:!1,timeStamp:r,id:`temp-${Date.now()}`};j(v=>[...v,c]),setTimeout(()=>{H()},50);let u=new Date().toISOString().split("T")[0];await h.postMessage({room_id:f,chat_id:x,user_id:d.user,message:t,date:u}),setTimeout(async()=>{try{const v=await h.getChats(f,x,u);v&&v.model&&(j(v.model.reverse()),setTimeout(()=>{H()},100))}catch(v){console.log("Error refreshing messages:",v)}},1e3)}catch(c){console.log("Error:",c),j(u=>u.filter(v=>v.id!==`temp-${Date.now()}`))}}const V=()=>{console.log("Stopping polling..."),L.current&&(clearTimeout(L.current),L.current=null),ie(!1)};async function Ce(){if(I){console.log("Polling already active, skipping start");return}console.log("Starting polling..."),ie(!0);const t=async()=>{if(!I){console.log("Polling stopped by flag");return}try{const r=await h.startPooling(d.user);if(r.message&&(console.log("New message detected from user:",r.user_id),o.current&&f&&x)){let c=new Date().toISOString().split("T")[0];const u=await h.getChats(f,x,c);u&&u.model&&(j(u.model.reverse()),setTimeout(()=>{H()},100))}I&&(L.current=setTimeout(t,2e3))}catch(r){console.log("Polling error:",r.message),pe(M,r.message),r.message==="TOKEN_EXPIRED"?window.location.replace(`/${d.role}/login/`):I&&(L.current=setTimeout(t,3e3))}};t()}i.useEffect(()=>{(async function(){await J()})()},[]),i.useEffect(()=>(f&&x?I||(console.log("Starting polling for room:",f,"chat:",x),Ce()):(console.log("Stopping polling - no active chat"),V()),()=>{(!f||!x)&&V()}),[f,x]),i.useEffect(()=>{H()},[$]),i.useEffect(()=>()=>{V()},[]),i.useEffect(()=>(window.addEventListener("resize",oe),()=>{window.removeEventListener("resize",oe)}),[_]);const _e=()=>{W.current.click()},Re=async()=>{F(!0);try{o.currentRoom.resolved==0?(h.setTable("room"),await h.callRestAPI({id:f,resolved:1},"PUT"),o.currentRoom.resolved=1,await J(),Q(f,x)):(h.setTable("room"),await h.callRestAPI({id:f,resolved:0},"PUT"),o.currentRoom.resolved=0,await J(),Q(f,x))}catch(t){console.log(t)}F(!1)},Te=async()=>{if(R){ne(!0);try{h.setTable("room"),await h.callRestAPI({id:R.id},"DELETE"),await te(),o.current===R.user_id&&(k(null),S(null),j([]),o.current=null,o.currentRoom=null),K(!1),Z(null)}catch(t){console.error("Error deleting room:",t),pe(M,t.message)}finally{ne(!1)}}},Ee=t=>{Z(t),K(!0)};return e.jsxs("div",{className:"flex h-[calc(100vh-4rem)] gap-4 bg-gray-50 p-4",children:[e.jsx("div",{className:`w-full rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-300 lg:w-1/3 ${_<1024&&!z?"hidden":"block"}`,children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"mb-4 flex items-center justify-between",children:e.jsxs("h2",{className:"text-lg font-semibold text-gray-900",children:[a==="open"?"Open":"Resolved"," Chats (",s.length,")"]})}),a==="open"&&e.jsxs("div",{className:"mb-4 flex space-x-1 rounded-lg bg-gray-100 p-1",children:[e.jsx("button",{onClick:()=>m("accepted"),className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${n==="accepted"?"bg-white text-blue-600 shadow-sm":"text-gray-500 hover:text-gray-700"}`,children:"Accepted"})," ",e.jsx("button",{onClick:()=>m("incoming"),className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${n==="incoming"?"bg-white text-blue-600 shadow-sm":"text-gray-500 hover:text-gray-700"}`,children:"Incoming"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search conversations...",className:"w-full rounded-lg border border-gray-200 py-2.5 pl-10 pr-4 transition-all duration-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500",value:g,onChange:t=>{q(t.target.value),J(t.target.value)}}),e.jsx(Ie,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),e.jsx("div",{className:"mt-4 max-h-[calc(100vh-12rem)] space-y-2 overflow-y-auto",children:s&&s.length>0?s.map((t,r)=>e.jsxs("div",{id:`user-${t.user_id}`,className:`group flex items-center rounded-lg p-3 transition-all duration-200 hover:bg-gray-50 hover:shadow-sm ${o.current===t.user_id?"border border-blue-200 bg-blue-50":""}`,children:[e.jsxs("div",{className:"relative",children:[t.photo?e.jsx("img",{className:"h-12 w-12 rounded-full object-cover",src:t.photo,alt:"user-photo"}):e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-gray-200",children:e.jsx("span",{className:"text-xl text-gray-500",children:T(t).charAt(0)})}),t.unread>0&&e.jsx("div",{className:"absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-blue-500",children:e.jsx("span",{className:"text-xs text-white",children:t.unread})})]}),e.jsx("div",{className:"ml-3 min-w-0 flex-1 cursor-pointer",onClick:()=>{(n==="accepted"||a==="resolved")&&(Q(t.id,t.chat_id),o.current=t.user_id,o.currentRoom=t,_<1024&&D(!1))},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"truncate text-sm font-medium text-gray-900",children:T(t)}),e.jsx("span",{className:"ml-2 whitespace-nowrap text-xs text-gray-500",children:we(t.update_at)})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[n==="incoming"&&a==="open"&&e.jsx("button",{onClick:()=>Se(t),disabled:p,className:"rounded-lg bg-blue-600 px-3 py-1.5 text-sm font-medium text-white transition-colors duration-200 hover:bg-blue-700 disabled:opacity-50",children:p?"Accepting...":"Accept"}),e.jsx("button",{onClick:c=>{c.stopPropagation(),Ee(t)},className:"rounded-lg p-2 text-gray-400 transition-colors duration-200 hover:bg-red-50 hover:text-red-600",title:"Delete chat",children:e.jsx(Le,{className:"h-4 w-4"})})]})]},r)):e.jsxs("div",{className:"flex h-[calc(100vh-20rem)] flex-col items-center justify-center text-center",children:[e.jsx("div",{className:"mb-4 text-6xl text-gray-300",children:"💬"}),e.jsxs("h3",{className:"text-xl font-medium text-gray-900",children:["No"," ",a==="open"?n==="incoming"?"Incoming":"Accepted":"Resolved"," ","Chats"]}),e.jsx("p",{className:"text-gray-500",children:a==="open"?n==="incoming"?"There are no incoming chat requests at the moment":"There are no accepted conversations at the moment":"There are no resolved conversations at the moment"})]})})]})}),e.jsx("div",{className:`flex-1 rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-300 ${_<1024&&z?"hidden":"block"}`,children:o!=null&&o.current?e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200 p-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("button",{className:"mr-4 text-gray-600 hover:text-gray-900 lg:hidden",onClick:()=>D(!0),children:e.jsx($e,{className:"h-6 w-6"})}),e.jsxs("div",{className:"flex items-center",children:[o.currentRoom.photo?e.jsx("img",{className:"h-10 w-10 rounded-full object-cover",src:o.currentRoom.photo,alt:"user-photo"}):e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-gray-200",children:e.jsx("span",{className:"text-lg text-gray-500",children:T(o.currentRoom).charAt(0)})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:T(o.currentRoom)}),e.jsx("p",{className:"text-sm text-gray-500",children:"Online"})]})]})]}),e.jsx("button",{onClick:Re,disabled:B,className:"rounded-xl border border-gray-200 bg-gray-100 p-2 px-3 text-gray-500 disabled:opacity-50 md:text-xs",children:B?"Updating...":o.currentRoom.resolved===0?"Mark as resolved":"Reopen"})]}),e.jsxs("div",{className:"flex-1 space-y-4 overflow-y-auto p-4",children:[$&&$.map((t,r)=>{var c,u,v,de,ue,me;return e.jsxs("div",{className:`flex ${((c=t==null?void 0:t.chat)==null?void 0:c.user_id)===d.user?"justify-end":"justify-start"}`,children:[((u=t==null?void 0:t.chat)==null?void 0:u.user_id)!==d.user&&e.jsx("div",{className:"mr-3 flex-shrink-0",children:o.currentRoom.photo?e.jsx("img",{className:"h-8 w-8 rounded-full object-cover",src:o.currentRoom.photo,alt:"user-photo"}):e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-gray-200",children:e.jsx("span",{className:"text-sm text-gray-500",children:T(o.currentRoom).charAt(0)})})}),e.jsxs("div",{className:`max-w-[70%] ${((v=t==null?void 0:t.chat)==null?void 0:v.user_id)===d.user?"text-right":""}`,children:[e.jsx("div",{className:`inline-block rounded-2xl px-4 py-2 ${((de=t==null?void 0:t.chat)==null?void 0:de.user_id)===d.user?"bg-blue-600 text-white":"bg-gray-100 text-gray-900"}`,children:t.is_image?e.jsx("img",{src:t==null?void 0:t.message,className:"max-h-80 rounded-lg object-cover",alt:"Shared image"}):e.jsx("p",{className:"whitespace-pre-line",children:(ue=t==null?void 0:t.chat)==null?void 0:ue.message})}),e.jsx("div",{className:`mt-1 text-xs text-gray-500 ${((me=t==null?void 0:t.chat)==null?void 0:me.user_id)===d.user?"text-right":""}`,children:O(t.timestamp).format("hh:mm A")})]})]},r)}),e.jsx("div",{ref:ee})]}),o.currentRoom.resolved===0&&(n==="accepted"||a==="resolved")?e.jsx("div",{className:"border-t border-gray-200 p-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:_e,className:"rounded-full p-2 text-gray-500 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900",children:e.jsx(He,{className:"h-5 w-5"})}),e.jsx("input",{className:"hidden",ref:W,type:"file",accept:"image/png, image/gif, image/jpeg",name:"file",onChange:t=>{U(t.target.files),G(!0)}}),e.jsx("div",{className:"flex-1",children:e.jsx(Oe,{value:A,onChange:C,placeholder:"Type a message...",className:"w-full rounded-full border border-gray-200 px-4 py-2 transition-all duration-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"})}),e.jsx("button",{onClick:ke,className:"rounded-full bg-blue-600 p-2 text-white transition-colors duration-200 hover:bg-blue-700",children:e.jsx(Ae,{className:"h-5 w-5"})})]})}):o.currentRoom.resolved===1?e.jsx("div",{className:"border-t border-gray-200 bg-gray-100 p-4",children:e.jsx("p",{className:"text-center text-gray-500",children:"This conversation is resolved"})}):e.jsx("div",{className:"border-t border-gray-200 bg-gray-100 p-4",children:e.jsx("p",{className:"text-center text-gray-500",children:n==="incoming"?"Accept this chat to start messaging":"Select an accepted chat to start messaging"})})]}):e.jsx("div",{className:"flex h-full items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mb-4 text-6xl text-gray-300",children:"💬"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900",children:"Select a conversation"}),e.jsx("p",{className:"text-gray-500",children:"Choose a chat to start messaging"})]})})}),e.jsx(De,{isOpen:Y,onClose:()=>{K(!1),Z(null)},onDelete:Te,title:"Delete Chat",message:`Are you sure you want to delete this chat with ${R?T(R):"this user"}? This action cannot be undone.`,loading:Ne,buttonText:"Delete Chat"})]})}function Be(){const{dispatch:a}=i.useContext(ve);return i.useEffect(()=>{a({type:"SETPATH",payload:{path:"customer-support"}})},[]),e.jsx("div",{className:"flex h-screen w-full flex-col bg-gray-50",children:e.jsx("div",{className:"flex h-full flex-col gap-4 bg-white px-5 py-5",children:e.jsxs(b.Group,{children:[e.jsxs(b.List,{className:"flex space-x-4 border-b border-gray-200",children:[e.jsx(b,{className:({selected:g})=>`px-1 pb-2 ${g?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Open chats"}),e.jsx(b,{className:({selected:g})=>`px-1 pb-2 ${g?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Resolved chats"}),e.jsx(b,{className:({selected:g})=>`px-1 pb-2 ${g?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Open tickets"}),e.jsx(b,{className:({selected:g})=>`px-1 pb-2 ${g?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Resolved tickets"}),e.jsx(b,{className:({selected:g})=>`px-1 pb-2 ${g?"border-b-2 border-blue-600 text-blue-600":"text-gray-500"}`,children:"Support Settings"})]}),e.jsxs(b.Panels,{className:"mt-4 h-full",children:[e.jsx(b.Panel,{className:"h-full",children:e.jsx(be,{activeTab:"open"})}),e.jsx(b.Panel,{className:"h-full",children:e.jsx(be,{activeTab:"resolved"})}),e.jsx(b.Panel,{children:e.jsx(ge,{status:0})}),e.jsx(b.Panel,{children:e.jsx(ge,{status:1})}),e.jsx(b.Panel,{children:e.jsx(We,{})})]})]})})})}const $t=Me(Be,"customer_support","You don't have permission to access customer support");export{$t as default};
