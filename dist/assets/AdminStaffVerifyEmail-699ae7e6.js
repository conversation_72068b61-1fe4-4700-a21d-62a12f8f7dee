import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{k as x,f,r as t}from"./vendor-851db8c1.js";import{A as p,M as u}from"./index-a0784e19.js";import{u as h,v as g}from"./index.esm-09a3a6b8.js";import{I as j}from"./index.esm-67ddadc4.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";function te(){const[s]=x(),r=f(),[i,a]=t.useState(""),[n,l]=t.useState(!0),{dispatch:c}=t.useContext(p);return t.useEffect(()=>{(async()=>{const o=s.get("token");if(!o){a("Verification token is missing"),l(!1);return}try{const d=await new u().verifyEmail(o);c({type:"LOGIN",payload:d}),r("/admin_staff/dashboard")}catch(m){a(m.message||"Email verification failed"),l(!1)}})()},[s,r]),n?e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:e.jsx("div",{className:"w-full max-w-md rounded-lg bg-white p-8 shadow-lg",children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"rounded-full bg-blue-50 p-3",children:e.jsx(j,{className:"h-8 w-8 animate-spin text-blue-500"})}),e.jsx("h2",{className:"text-center text-2xl font-semibold text-gray-900",children:"Verifying Your Email"}),e.jsx("p",{className:"text-center text-gray-600",children:"Please wait while we verify your email address..."})]})})}):i?e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:e.jsx("div",{className:"w-full max-w-md rounded-lg bg-white p-8 shadow-lg",children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"rounded-full bg-red-50 p-3",children:e.jsx(h,{className:"h-8 w-8 text-red-500"})}),e.jsx("h2",{className:"text-center text-2xl font-semibold text-gray-900",children:"Verification Failed"}),e.jsx("p",{className:"text-center text-red-500",children:i}),e.jsx("button",{onClick:()=>r("/admin_staff/login"),className:"mt-4 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Return to Login"})]})})}):e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:e.jsx("div",{className:"w-full max-w-md rounded-lg bg-white p-8 shadow-lg",children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"rounded-full bg-green-50 p-3",children:e.jsx(g,{className:"h-8 w-8 text-green-500"})}),e.jsx("h2",{className:"text-center text-2xl font-semibold text-gray-900",children:"Email Verified!"}),e.jsx("p",{className:"text-center text-gray-600",children:"Your email has been successfully verified. Redirecting to dashboard..."})]})})})}export{te as default};
