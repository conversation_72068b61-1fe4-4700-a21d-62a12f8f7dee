import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as d,b,f as he}from"./vendor-851db8c1.js";import{M as Z,G as ue,A as fe,E as ye,c as z,i as je,v as q,R as Q}from"./index-a0784e19.js";import"./index-be4468eb.js";import"./yup-54691517.js";import{L as ge}from"./index.esm-3a36c7d6.js";import"./AddButton.module-98aac587.js";import{P as be}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import ie from"./Skeleton-1e8bf077.js";import{S as Ne}from"./StripeConnectionStatus-2b96cb55.js";import{G as we}from"./react-icons-51bc3cff.js";import{f as p}from"./date-fns-07266b7d.js";import{H as _e}from"./HistoryComponent-0c9f35b4.js";const Ce=new Z;function Se(){const s=d.useRef(null),[o,r]=d.useState(!1),[h,m]=d.useState(!1),j=n=>{console.log("Stripe connection status changed:",n),r(n.isConnected)},l=async()=>{m(!0);try{const n=localStorage.getItem("role"),g=await Ce.callRawAPI(`/v3/api/custom/courtmatchup/${n}/stripe/onboarding`,{},"POST");window.open(g.url,"_blank")}catch(n){console.error("Error updating Stripe details:",n)}m(!1)};return e.jsxs("div",{className:"flex h-full flex-col gap-6",children:[e.jsx(Ne,{ref:s,onConnectionStatusChange:j,successMessage:"You can now pay staffs and coaches from your club.",noConnectionMessage:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),o&&e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg border border-blue-200 bg-blue-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-lg font-medium text-gray-900",children:"Update Bank Details"})]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Need to update your bank account information or other Stripe details? Click the button below to access your Stripe account settings."}),e.jsx("button",{onClick:l,className:"w-full rounded-xl bg-blue-600 px-4 py-3 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50",disabled:h,children:h?"Opening Stripe...":"Update Stripe Bank Details"})]})]})}function ke(s){return we({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0110.56 0m-10.56 0L6.34 18m10.94-4.171c.************.72.096m-.72-.096L17.66 18m0 0l.229 2.523a1.125 1.125 0 01-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0021 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 00-1.913-.247M6.34 18H5.25A2.25 2.25 0 013 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 011.913-.247m10.5 0a48.536 48.536 0 00-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5zm-3 0h.008v.008H15V10.5z"}}]})(s)}const Me=({invoice:s,onClose:o})=>{const r=(l,n="usd")=>new Intl.NumberFormat("en-US",{style:"currency",currency:n}).format(l||0),h=()=>{const l=document.getElementById("invoice-print-content");document.body.innerHTML;const n=window.open("","_blank");n.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Invoice - ${(s==null?void 0:s.receipt_id)||"Receipt"}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              color: #333;
            }
            .print-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #333;
              padding-bottom: 20px;
            }
            .print-section {
              margin-bottom: 25px;
              page-break-inside: avoid;
            }
            .print-section h3 {
              background-color: #f5f5f5;
              padding: 10px;
              margin: 0 0 15px 0;
              border-left: 4px solid #333;
            }
            .print-row {
              display: flex;
              justify-content: space-between;
              padding: 8px 0;
              border-bottom: 1px solid #eee;
            }
            .print-row:last-child {
              border-bottom: none;
            }
            .print-total {
              border-top: 2px solid #333;
              padding-top: 10px;
              font-weight: bold;
              font-size: 1.1em;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          ${l.innerHTML}
        </body>
      </html>
    `),n.document.close(),n.focus(),setTimeout(()=>{n.print(),n.close()},250)},m=l=>{if(!l)return"--";try{return p(new Date(l),"MM/dd/yyyy")}catch{return"--"}},j=l=>{if(!l)return"--";try{return p(new Date(l),"MM/dd/yyyy 'at' h:mm a")}catch{return"--"}};return console.log("invoice",s),e.jsxs("div",{className:"flex  flex-col",children:[e.jsxs("div",{id:"invoice-print-content",style:{display:"none"},children:[e.jsxs("div",{className:"print-header",children:[e.jsx("h1",{children:"Invoice Receipt"}),e.jsxs("p",{children:["Receipt ID: #",(s==null?void 0:s.receipt_id)||"--"]}),e.jsxs("p",{children:["Date: ",m(s==null?void 0:s.create_at)]})]}),e.jsxs("div",{className:"print-section",children:[e.jsx("h3",{children:"Customer Information"}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Customer name:"}),e.jsx("span",{children:s!=null&&s.first_name&&(s!=null&&s.last_name)?`${s.first_name} ${s.last_name}`:"--"})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Email:"}),e.jsx("span",{children:(s==null?void 0:s.email)||"--"})]})]}),e.jsxs("div",{className:"print-section",children:[e.jsx("h3",{children:"Payment Information"}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Receipt ID:"}),e.jsxs("span",{children:["#",(s==null?void 0:s.receipt_id)||"--"]})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Payment Intent:"}),e.jsx("span",{children:(s==null?void 0:s.payment_intent)||"--"})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Payment method:"}),e.jsxs("span",{children:[(s==null?void 0:s.payment_method)||"Credit card",s!=null&&s.last_4?` ••• ${s.last_4}`:""]})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Status:"}),e.jsx("span",{children:(s==null?void 0:s.status)||"--"})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Currency:"}),e.jsx("span",{children:(s==null?void 0:s.currency)||"USD"})]})]}),e.jsxs("div",{className:"print-section",children:[e.jsx("h3",{children:"Booking Information"}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Booking type:"}),e.jsx("span",{children:(s==null?void 0:s.type)||(s==null?void 0:s.invoice_type)||"--"})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Sport:"}),e.jsx("span",{children:(s==null?void 0:s.sport_name)||"--"})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Booking date:"}),e.jsx("span",{children:m(s==null?void 0:s.date)})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Valid until:"}),e.jsx("span",{children:m(s==null?void 0:s.valid_until)})]})]}),e.jsxs("div",{className:"print-section",children:[e.jsx("h3",{children:"Amount Breakdown"}),(s==null?void 0:s.amount)&&e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Base amount:"}),e.jsx("span",{children:r(s.amount,s==null?void 0:s.currency)})]}),(s==null?void 0:s.court_fee)>0&&e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Court fee:"}),e.jsx("span",{children:r(s.court_fee,s==null?void 0:s.currency)})]}),(s==null?void 0:s.coach_fee)>0&&e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Coach fee:"}),e.jsx("span",{children:r(s.coach_fee,s==null?void 0:s.currency)})]}),(s==null?void 0:s.clinic_fee)>0&&e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Clinic fee:"}),e.jsx("span",{children:r(s.clinic_fee,s==null?void 0:s.currency)})]}),(s==null?void 0:s.club_fee)>0&&e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Club fee:"}),e.jsx("span",{children:r(s.club_fee,s==null?void 0:s.currency)})]}),(s==null?void 0:s.service_fee)>0&&e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Service fee:"}),e.jsx("span",{children:r(s.service_fee,s==null?void 0:s.currency)})]}),e.jsxs("div",{className:"print-row print-total",children:[e.jsx("span",{children:"Total amount:"}),e.jsx("span",{children:r((s==null?void 0:s.total_amount)||(s==null?void 0:s.amount),s==null?void 0:s.currency)})]})]}),e.jsxs("div",{className:"print-section",children:[e.jsx("h3",{children:"Important Dates"}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Created:"}),e.jsx("span",{children:m(s==null?void 0:s.create_at)})]}),e.jsxs("div",{className:"print-row",children:[e.jsx("span",{children:"Last updated:"}),e.jsx("span",{children:j(s==null?void 0:s.update_at)})]})]})]}),e.jsxs("div",{className:"flex flex-1 flex-col gap-6 overflow-y-auto rounded-xl bg-gray-100 p-3 pb-20",children:[e.jsx("div",{className:"text-sm text-gray-500",children:(s==null?void 0:s.invoice_type)||"Payment"}),e.jsxs("div",{className:"flex flex-col gap-4 rounded-xl bg-white p-4",children:[e.jsx("h3",{className:"border-b border-gray-200 pb-2 text-lg font-semibold text-gray-900",children:"Customer Information"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Customer name"}),e.jsx("span",{className:"font-medium",children:s!=null&&s.first_name&&(s!=null&&s.last_name)?`${s.first_name} ${s.last_name}`:"--"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Email"}),e.jsx("span",{className:"font-medium",children:(s==null?void 0:s.email)||"--"})]})]}),e.jsxs("div",{className:"flex flex-col gap-4 rounded-xl bg-white p-4",children:[e.jsx("h3",{className:"border-b border-gray-200 pb-2 text-lg font-semibold text-gray-900",children:"Payment Information"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Receipt ID"}),e.jsxs("span",{className:"font-medium",children:["#",(s==null?void 0:s.receipt_id)||"--"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payment Intent"}),e.jsx("span",{className:"text-xs font-medium",children:(s==null?void 0:s.payment_intent)||"--"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsxs("span",{className:"font-medium",children:[(s==null?void 0:s.payment_method)||"Credit card",s!=null&&s.last_4?` ••• ${s.last_4}`:""]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Status"}),e.jsx("span",{className:`font-medium capitalize ${(s==null?void 0:s.status)==="completed"?"text-green-600":(s==null?void 0:s.status)==="pending"?"text-yellow-600":"text-gray-600"}`,children:(s==null?void 0:s.status)||"--"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Currency"}),e.jsx("span",{className:"font-medium uppercase",children:(s==null?void 0:s.currency)||"USD"})]})]}),e.jsxs("div",{className:"flex flex-col gap-4 rounded-xl bg-white p-4",children:[e.jsx("h3",{className:"border-b border-gray-200 pb-2 text-lg font-semibold text-gray-900",children:"Booking Information"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Booking type"}),e.jsx("span",{className:"font-medium",children:(s==null?void 0:s.type)||(s==null?void 0:s.invoice_type)||"--"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Sport"}),e.jsx("span",{className:"font-medium",children:(s==null?void 0:s.sport_name)||"--"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Booking date"}),e.jsx("span",{className:"font-medium",children:m(s==null?void 0:s.date)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{className:"font-medium",children:m(s==null?void 0:s.valid_until)})]})]}),e.jsxs("div",{className:"flex flex-col gap-4 rounded-xl bg-white p-4",children:[e.jsx("h3",{className:"border-b border-gray-200 pb-2 text-lg font-semibold text-gray-900",children:"Amount Breakdown"}),(s==null?void 0:s.amount)&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Base amount"}),e.jsx("span",{className:"font-medium",children:r(s.amount,s==null?void 0:s.currency)})]}),(s==null?void 0:s.court_fee)>0&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Court fee"}),e.jsx("span",{className:"font-medium",children:r(s.court_fee,s==null?void 0:s.currency)})]}),(s==null?void 0:s.coach_fee)>0&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Coach fee"}),e.jsx("span",{className:"font-medium",children:r(s.coach_fee,s==null?void 0:s.currency)})]}),(s==null?void 0:s.clinic_fee)>0&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Clinic fee"}),e.jsx("span",{className:"font-medium",children:r(s.clinic_fee,s==null?void 0:s.currency)})]}),(s==null?void 0:s.club_fee)>0&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Club fee"}),e.jsx("span",{className:"font-medium",children:r(s.club_fee,s==null?void 0:s.currency)})]}),(s==null?void 0:s.service_fee)>0&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Service fee"}),e.jsx("span",{className:"font-medium",children:r(s.service_fee,s==null?void 0:s.currency)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-gray-200 pt-2",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:"Total amount"}),e.jsx("span",{className:"text-lg font-bold",children:r((s==null?void 0:s.total_amount)||(s==null?void 0:s.amount),s==null?void 0:s.currency)})]})]}),e.jsxs("div",{className:"flex flex-col gap-4 rounded-xl bg-white p-4",children:[e.jsx("h3",{className:"border-b border-gray-200 pb-2 text-lg font-semibold text-gray-900",children:"Important Dates"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Created"}),e.jsx("span",{className:"font-medium",children:m(s==null?void 0:s.create_at)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Last updated"}),e.jsx("span",{className:"font-medium",children:j(s==null?void 0:s.update_at)})]})]})]}),e.jsx("div",{className:"flex-shrink-0 border-t border-gray-200 bg-white p-4",children:e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:h,className:"flex flex-1 items-center justify-center gap-2 rounded-xl bg-gray-600 py-3 text-center font-medium text-white hover:bg-gray-700",children:[e.jsx(ke,{className:"h-5 w-5"}),"Print"]}),e.jsx("button",{onClick:o,className:"flex-1 rounded-xl bg-primaryBlue py-3 text-center font-medium text-white hover:bg-blue-700",children:"Close"})]})})]})};new Z;const Y=[{header:"Date/time",accessor:"created"},{header:"Invoice ID",accessor:"invoice_id"},{header:"User name",accessor:"user_name"},{header:"User email",accessor:"user_email"},{header:"Booking type",accessor:"invoice_type"},{header:"Sport",accessor:"sport_name"},{header:"Total",accessor:"total"},{header:"",accessor:"actions"}],ze=({fetchProfile:s,club:o,sports:r,fetchInvoices:h,invoices:m})=>{const{dispatch:j}=b.useContext(ue);b.useContext(fe);const[l,n]=b.useState(10),[g,M]=b.useState(1),[B,De]=b.useState(!1);he();const[K,D]=d.useState(!1),[i,L]=d.useState(""),[N,P]=d.useState(""),[w,T]=d.useState(""),[_,F]=d.useState(new Date),[C,A]=d.useState(new Date),[H,J]=d.useState("00:00"),[U,X]=d.useState("23:59"),[f,v]=d.useState("desc"),[S,R]=d.useState(""),[ee,se]=d.useState(null),[te,V]=d.useState(!1),[k,E]=d.useState(""),[ae,O]=d.useState(!1),[y,re]=d.useState("users"),u=(t=1,c={})=>{const a={};i&&(a.invoice_id=i),N&&(a.firstName=N),w&&(a.lastName=w),ae&&_&&C&&(a.from=p(_,"yyyy-MM-dd"),a.until=p(C,"yyyy-MM-dd"),a.timeFrom=H,a.timeUntil=U),f&&(a.sort=f),S&&(a.bookingType=S),k&&(a.sportId=k),a.tabType=y;const x={...a,...c};h(t,l,x)},$=t=>{re(t),M(1),L(""),P(""),T(""),R(""),E(""),O(!1),F(new Date),A(new Date),h(1,l,{tabType:t})},le=t=>{const c=t.target.value;L(c),u(1)},ne=t=>{const c=t.target.value;P(c),u(1)},de=t=>{const c=t.target.value;T(c),u(1)},W=(t,c)=>{const a=new Date(t.target.value);O(!0),c==="from"?F(a):A(a),u(1)},ce=()=>{v(f==="asc"?"desc":"asc"),u(1)},me=t=>{const c=t.target.value;R(c),u(1)},xe=t=>{const c=t.target.value;E(c),u(1)},G=t=>{se(t),V(!0)},pe=()=>{const t=g-1;M(t),u(t)},oe=()=>{const t=g+1;M(t),u(t)};b.useEffect(()=>{j({type:"SETPATH",payload:{path:"invoicing"}}),u(1)},[]);const I=localStorage.getItem("role");return e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Invoices"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_e,{title:"Invoice History",emptyMessage:"No invoice history found",activityType:ye.invoice}),I==="club"&&e.jsxs("button",{onClick:()=>D(!0),disabled:I!=="club",className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white disabled:cursor-not-allowed disabled:opacity-50",children:[e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.041 7.70703V14.3737M12.7077 14.3737V7.70703M3.95768 7.70703V14.3737M7.29102 14.3737V7.70703M16.1536 14.3737H3.84511C3.41468 14.3737 3.03254 14.6491 2.89642 15.0575L2.72976 15.5575C2.51391 16.205 2.99588 16.8737 3.67844 16.8737H16.3203C17.0028 16.8737 17.4848 16.205 17.2689 15.5575L17.1023 15.0575C16.9662 14.6491 16.584 14.3737 16.1536 14.3737ZM16.7077 7.70703H3.29102C2.73873 7.70703 2.29102 7.25932 2.29102 6.70703V6.59143C2.29102 6.21416 2.50335 5.86899 2.84009 5.69887L9.54843 2.30984C9.83198 2.16659 10.1667 2.16659 10.4503 2.30984L17.1586 5.69887C17.4953 5.86899 17.7077 6.21415 17.7077 6.59143V6.70703C17.7077 7.25932 17.26 7.70703 16.7077 7.70703Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"})})}),"Stripe connect"]})]})]}),e.jsxs("div",{className:"flex gap-8 border-b border-gray-200",children:[e.jsx("button",{onClick:()=>$("users"),className:`px-4 py-2 text-sm font-medium ${y==="users"?"border-b-2 border-[#1D275F] text-[#1D275F]":"text-gray-500 hover:text-gray-700"}`,children:"Users"}),e.jsx("button",{onClick:()=>$("coaches"),disabled:I!=="club",className:`px-4 py-2 text-sm font-medium disabled:cursor-not-allowed disabled:opacity-50 ${y==="coaches"?"border-b-2 border-[#1D275F] text-[#1D275F]":"text-gray-500 hover:text-gray-700"}`,children:"Coaches"}),e.jsx("button",{onClick:()=>$("staff"),disabled:I!=="club",className:`px-4 py-2 text-sm font-medium disabled:cursor-not-allowed disabled:opacity-50 ${y==="staff"?"border-b-2 border-[#1D275F] text-[#1D275F]":"text-gray-500 hover:text-gray-700"}`,children:"Staff"})]}),e.jsxs("div",{className:"flex flex-col gap-6 py-4",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"relative w-full max-w-[300px]",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(z,{className:"h-5 w-5 text-gray-500"})}),e.jsx("input",{type:"text",value:i,onChange:le,className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-5 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search by invoice ID"})]}),e.jsxs("div",{className:"relative w-full max-w-[300px]",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(z,{className:"h-5 w-5 text-gray-500"})}),e.jsx("input",{type:"text",value:N,onChange:ne,className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-5 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search by first name"})]}),e.jsxs("div",{className:"relative w-full max-w-[300px]",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(z,{className:"h-5 w-5 text-gray-500"})}),e.jsx("input",{type:"text",value:w,onChange:de,className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-5 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search by last name"})]})]}),e.jsx("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:e.jsxs("div",{className:"flex flex-wrap items-end gap-3",children:[e.jsx("div",{className:"flex flex-wrap items-center gap-2 border-r border-gray-200 pr-3",children:e.jsx("div",{className:"flex flex-col",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"date",value:p(_,"yyyy-MM-dd"),onChange:t=>W(t,"from"),className:"appearance-none rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"from"}),e.jsx("span",{children:"-"}),e.jsx("input",{type:"date",value:p(C,"yyyy-MM-dd"),onChange:t=>W(t,"until"),className:"appearance-none rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"until"})]})})}),e.jsx("div",{className:"border-r border-gray-200 pr-3",children:e.jsxs("button",{onClick:ce,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-r border-gray-200  px-4 py-2 pr-3 text-sm hover:bg-gray-50 sm:w-auto",children:[f==="asc"?"Oldest first":"Latest first",e.jsx(ge,{className:`transform ${f==="desc"?"rotate-180":""}`})]})}),e.jsx("div",{className:"border-r border-gray-200 pr-3",children:e.jsxs("select",{className:"w-full rounded-lg border border-r border-gray-200  px-3 py-2 pr-8 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:w-auto",value:S,onChange:me,children:[e.jsx("option",{value:"",children:"Booking type: All"}),je.map(t=>e.jsx("option",{value:t.value,children:t.label},t.value))]})}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:w-auto",value:k,onChange:xe,children:[e.jsx("option",{value:"",children:"Sport: All"}),r==null?void 0:r.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]})]})})]}),B?e.jsx(ie,{}):e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs("table",{className:"w-full min-w-[600px] table-auto divide-y overflow-x-auto",children:[e.jsx("thead",{children:e.jsx("tr",{children:Y.map((t,c)=>e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:t.header},c))})}),e.jsx("tbody",{className:"divide-y-8 divide-gray-50",children:m.map((t,c)=>e.jsx("tr",{className:"hover:bg-gray-40 cursor-pointer bg-gray-100 px-4 py-3 text-gray-500",onClick:()=>G(t),children:Y.map((a,x)=>a.accessor==="actions"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:()=>G(t),children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})})},x):a.accessor==="user_email"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(t==null?void 0:t.email)||"--"},x):a.accessor==="user_name"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:[(t==null?void 0:t.first_name)||"--"," ",(t==null?void 0:t.last_name)||"--"]},x):a.accessor==="created"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.update_at&&p(new Date(t.update_at),"EEEE, MMMM d • h:mm a")},x):a.accessor==="invoice_id"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.receipt_id},x):a.accessor==="invoice_type"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(t==null?void 0:t.invoice_type)||"--"},x):a.accessor==="amount_remaining"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:q((t==null?void 0:t.amount_remaining)||0)},x):a.accessor==="total"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:q((t==null?void 0:t.total_amount)||0)},x):e.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:t[a.accessor]},x))},c))})]}),B&&e.jsx("div",{className:"px-6 py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading..."})}),!B&&m.length===0&&e.jsxs("div",{className:"w-full px-6 py-8 text-center",children:[e.jsx("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gray-100",children:e.jsx("svg",{className:"h-8 w-8 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No invoices found"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:y==="users"?"No user invoices found for the selected filters.":y==="coaches"?"No coach invoices found for the selected filters.":"No staff invoices found for the selected filters."}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{onClick:()=>{L(""),P(""),T(""),R(""),E(""),O(!1),F(new Date),A(new Date),J("00:00"),X("23:59"),h(1,l,{tabType:y})},className:"inline-flex items-center rounded-md border border-transparent bg-[#1D275F] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-[#1D275F] focus:ring-offset-2",children:"Clear filters"})})]})]}),e.jsx(be,{currentPage:g,pageCount:Math.ceil(m.length/l),pageSize:l,canPreviousPage:g>1,canNextPage:m.length===l,updatePageSize:t=>{n(t),h(1,t,{search:i,firstName:N,lastName:w,date_from:p(_,"yyyy-MM-dd"),date_until:p(C,"yyyy-MM-dd"),time_from:H,time_until:U,sort:f,booking_type:S,sport_id:k})},previousPage:pe,nextPage:oe,gotoPage:t=>{M(t),h(t,l,{search:i,firstName:N,lastName:w,date_from:p(_,"yyyy-MM-dd"),date_until:p(C,"yyyy-MM-dd"),time_from:H,time_until:U,sort:f,booking_type:S,sport_id:k})}}),e.jsx(Q,{isOpen:K,onClose:()=>D(!1),title:"Banking details",primaryButtonText:"Save changes",onPrimaryAction:()=>{D(!1)},showFooter:!1,children:e.jsx(Se,{account_details:o==null?void 0:o.account_details,account_settings:o==null?void 0:o.account_settings,fetchProfile:s,onClose:()=>D(!1)})}),e.jsx(Q,{isOpen:te,onClose:()=>V(!1),showFooter:!1,title:"Invoice details",children:e.jsx(Me,{invoice:ee,onClose:()=>V(!1)})})]})};export{ze as I};
