import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{j as Q,r as m,b as W}from"./vendor-851db8c1.js";import{G as X,e as Z,M as ee,T as se,H as b,E as j,J as v,b as u,aP as ae,d as L,z as te,C as re,v as le}from"./index-a0784e19.js";import"./BottomDrawer-4cdfc0e3.js";let n=new ee;const ie=new se;function me({club:o}){var I,U,$,A,D,k;const{id:g}=Q(),[e,y]=m.useState(null),{dispatch:d}=W.useContext(X),[S,N]=m.useState(null),[h,x]=m.useState(""),[_,c]=m.useState(!1),[C,ne]=m.useState("personal"),[z,E]=m.useState(!0),[w,B]=m.useState(!1),[T,P]=m.useState(!1),G=localStorage.getItem("role"),f=async()=>{try{E(!0);const a=await ie.getOne("staff",g,{join:["user|user_id"]});y(a.model)}catch(a){console.error(a)}finally{E(!1)}},R=async a=>{var i;N(a);const r=["first_name","last_name","email","phone","role"].includes(a)?(i=e==null?void 0:e.user)==null?void 0:i[a]:e==null?void 0:e[a];x(r||"")},H=async a=>{var l,t;c(!0);try{const i=["first_name","last_name","email","phone","role"].includes(a);n.setTable(i?"user":"staff"),i?await n.callRestAPI({id:e.user.id,[a]:h},"PUT"):await n.callRestAPI({id:g,[a]:h},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,[a]:h},club_id:o==null?void 0:o.id,description:`Updated ${a} for ${(l=e==null?void 0:e.user)==null?void 0:l.first_name} ${(t=e==null?void 0:e.user)==null?void 0:t.last_name}`}),await f(),N(null),u(d,"Updated successfully")}catch(r){console.error(r),u(d,"Error updating field","error")}finally{c(!1)}},F=()=>{N(null),x("")},M=async a=>{var l,t;try{c(!0);let r=new FormData;r.append("file",a);let i=await n.uploadImage(r);n.setTable("user"),await n.callRestAPI({id:g,photo:i==null?void 0:i.url},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,photo:null},club_id:o==null?void 0:o.id,description:`Modified profile picture for ${(l=e==null?void 0:e.user)==null?void 0:l.first_name} ${(t=e==null?void 0:e.user)==null?void 0:t.last_name}`}),y({...e,photo:i==null?void 0:i.url}),u(d,"Photo updated successfully",3e3,"success")}catch(r){u(d,r==null?void 0:r.message,3e3,"error"),console.log(r)}finally{c(!1)}},V=async()=>{var a,l;try{c(!0),n.setTable("user"),await n.callRestAPI({id:g,photo:null},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,photo:null},club_id:o==null?void 0:o.id,description:`Removed photo for ${(a=e==null?void 0:e.user)==null?void 0:a.first_name} ${(l=e==null?void 0:e.user)==null?void 0:l.last_name}`}),y({...e,photo:null}),u(d,"Photo removed successfully",3e3,"success")}catch(t){u(d,t==null?void 0:t.message,3e3,"error"),console.log(t)}finally{c(!1)}};m.useEffect(()=>{f()},[g]);const p=(a,l,t,r=!1)=>s.jsx("div",{className:"border-b pb-4",children:S===a?s.jsxs("div",{children:[s.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[s.jsx("label",{className:"block text-gray-500",children:l}),s.jsx("button",{onClick:F,className:"text-primaryBlue hover:underline",children:"Cancel"})]}),a==="bio"?s.jsx("textarea",{value:h,onChange:i=>x(i.target.value),className:"mb-3 h-32 w-full rounded-lg border border-gray-300 p-2",placeholder:"Enter your bio..."}):a==="role"?s.jsxs("div",{className:"relative mb-3",children:[s.jsxs("div",{className:"flex w-full cursor-pointer items-center justify-between rounded-lg border border-gray-300 p-2",onClick:()=>P(!T),children:[s.jsx("span",{children:h||"Select role"}),s.jsx(ae,{className:"text-gray-500"})]}),T&&s.jsxs("div",{className:"absolute z-10 mt-1 w-full rounded-lg border border-gray-300 bg-white shadow-lg",children:[s.jsx("div",{className:"cursor-pointer p-2 hover:bg-gray-100",onClick:()=>{x("staff"),P(!1)},children:"staff"}),s.jsx("div",{className:"cursor-pointer p-2 hover:bg-gray-100",onClick:()=>{x("admin"),P(!1)},children:"admin"})]})]}):s.jsx("input",{type:"text",value:h,onChange:i=>x(i.target.value),className:"mb-3 w-full rounded-lg border border-gray-300 p-2"}),s.jsx(L,{loading:_,onClick:()=>H(a),className:"rounded-lg bg-[#1E335F] px-6 py-2 text-white hover:bg-[#162544]",children:"Save"})]}):s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-gray-500",children:l}),s.jsx("div",{className:"font-medium",children:t}),r&&s.jsxs("p",{className:"text-sm text-gray-400",children:["Your ",l.toLowerCase()," is not shared with other users."]})]}),(a!=="role"||["admin","club"].includes(G))&&s.jsx("button",{onClick:()=>R(a),className:"text-blue-600 hover:underline",children:"Edit"})]})}),O=async()=>{var a,l,t;try{c(!0);const r=((a=e==null?void 0:e.user)==null?void 0:a.status)===1?0:1;n.setTable("user"),await n.callRestAPI({id:e.user.id,status:r},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,status:r},club_id:o==null?void 0:o.id,description:`Changed status to ${r===1?"Active":"Inactive"} for ${(l=e==null?void 0:e.user)==null?void 0:l.first_name} ${(t=e==null?void 0:e.user)==null?void 0:t.last_name}`}),y({...e,user:{...e.user,status:r}}),u(d,`Staff is now ${r===1?"Active":"Inactive"}`,3e3,"success")}catch(r){u(d,r==null?void 0:r.message,3e3,"error"),console.log(r)}finally{c(!1)}},J=async()=>{var a,l;try{c(!0);const t=parseFloat(h);if(isNaN(t))throw new Error("Please enter a valid number for pay rate");n.setTable("staff"),await n.callRestAPI({id:e.id,hourly_rate:t},"PUT"),await b(n,{user_id:localStorage.getItem("user"),activity_type:j.staff_management,action_type:v.UPDATE,data:{staff_id:e.id,hourly_rate:t},club_id:o==null?void 0:o.id,description:`Updated pay rate for ${(a=e==null?void 0:e.user)==null?void 0:a.first_name} ${(l=e==null?void 0:e.user)==null?void 0:l.last_name}`}),y({...e,hourly_rate:t}),N(null),u(d,"Pay rate updated successfully",3e3,"success")}catch(t){u(d,t==null?void 0:t.message,3e3,"error"),console.log(t)}finally{c(!1)}},K=()=>s.jsxs("div",{className:"mb-6 border-b pb-4",children:[s.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[s.jsx("h3",{className:"text-lg font-medium",children:"Pay Rate"}),s.jsx("button",{onClick:()=>B(!w),className:"flex items-center gap-1 text-blue-600 hover:underline",children:w?s.jsxs(s.Fragment,{children:[s.jsx(te,{size:16}),s.jsx("span",{children:"Hide Pay Rate"})]}):s.jsxs(s.Fragment,{children:[s.jsx(re,{size:16}),s.jsx("span",{children:"Show Pay Rate"})]})})]}),w&&s.jsx("div",{className:"mt-2",children:S==="hourly_rate"?s.jsxs("div",{children:[s.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[s.jsx("label",{className:"block text-gray-500",children:"Hourly Rate"}),s.jsx("button",{onClick:F,className:"text-primaryBlue hover:underline",children:"Cancel"})]}),s.jsxs("div",{className:"relative mb-3",children:[s.jsx("span",{className:"absolute left-3 top-2 text-gray-500",children:"$"}),s.jsx("input",{type:"number",step:"0.01",min:"0",value:h,onChange:a=>x(a.target.value),className:"mb-3 w-full rounded-lg border border-gray-300 p-2 pl-8",placeholder:"0.00"})]}),s.jsx(L,{loading:_,onClick:J,className:"rounded-lg bg-[#1E335F] px-6 py-2 text-white hover:bg-[#162544]",children:"Save"})]}):s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-gray-500",children:"Hourly Rate"}),s.jsx("div",{className:"font-medium",children:e!=null&&e.hourly_rate?le(e.hourly_rate):"Not set"})]}),s.jsx("button",{onClick:()=>{R("hourly_rate")},className:"text-blue-600 hover:underline",children:"Edit"})]})})]}),Y=()=>{var a,l,t,r;return s.jsx("div",{className:"mb-6 border-b pb-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"mb-1 text-lg font-medium",children:"Staff Status"}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:`h-3 w-3 rounded-full ${((a=e==null?void 0:e.user)==null?void 0:a.status)===1?"bg-green-500":"bg-red-500"}`}),s.jsx("span",{className:"font-medium",children:((l=e==null?void 0:e.user)==null?void 0:l.status)===1?"Active":"Inactive"})]})]}),s.jsx("button",{onClick:O,disabled:_,className:`rounded-lg px-4 py-2 text-white ${((t=e==null?void 0:e.user)==null?void 0:t.status)===1?"bg-red-500 hover:bg-red-600":"bg-green-500 hover:bg-green-600"} disabled:opacity-50`,children:((r=e==null?void 0:e.user)==null?void 0:r.status)===1?"Set Inactive":"Set Active"})]})})},q=()=>{var a,l,t,r,i;switch(C){case"personal":return s.jsxs("div",{className:"space-y-4",children:[Y(),K(),p("first_name","First Name",(a=e==null?void 0:e.user)==null?void 0:a.first_name),p("last_name","Last name",(l=e==null?void 0:e.user)==null?void 0:l.last_name),p("email","Email",(t=e==null?void 0:e.user)==null?void 0:t.email,!0),p("phone","Phone number",(r=e==null?void 0:e.user)==null?void 0:r.phone,!0),p("role","Role",((i=e==null?void 0:e.user)==null?void 0:i.role)||"staff"),p("bio","Bio",(e==null?void 0:e.bio)||"No bio provided")]});default:return null}};return s.jsxs("div",{children:[_||z?s.jsx(Z,{}):null,s.jsxs("div",{className:"mx-auto max-w-3xl",children:[s.jsx("h1",{className:"mb-6 text-2xl font-bold capitalize",children:!((I=e==null?void 0:e.user)!=null&&I.first_name)||!((U=e==null?void 0:e.user)!=null&&U.last_name)?"Staff's profile":`${($=e==null?void 0:e.user)==null?void 0:$.first_name} ${(A=e==null?void 0:e.user)==null?void 0:A.last_name}'s profile`}),s.jsx("div",{className:"rounded-lg bg-white p-6 shadow",children:s.jsxs("div",{className:"space-y-6",children:[C==="personal"&&s.jsx("div",{className:"mb-6",children:s.jsxs("div",{className:"flex items-end gap-4",children:[s.jsx("img",{src:((D=e==null?void 0:e.user)==null?void 0:D.photo)||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("p",{className:"mb-1",children:"Upload Image"}),s.jsx("p",{className:"my-2 text-sm text-gray-600",children:"Min 400x400px, PNG or JPEG"}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx("button",{disabled:!((k=e==null?void 0:e.user)!=null&&k.photo),className:"rounded-lg border border-red-600 px-2 py-1 text-red-600 disabled:opacity-50",onClick:V,children:"Remove"}),s.jsxs("label",{className:"cursor-pointer rounded-lg border border-gray-300 px-2 py-1 text-gray-500",children:[s.jsx("input",{type:"file",accept:".jpg,.jpeg,.png",className:"hidden",onChange:a=>{const l=a.target.files[0];l&&M(l)}}),"Change Photo"]})]})]})]})}),q()]})})]})]})}export{me as V};
