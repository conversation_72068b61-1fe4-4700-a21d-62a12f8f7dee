import{j as i}from"./@nivo/heatmap-ba1ecfff.js";import{C as v}from"./CourtManagement-5ab6fb97.js";import{w as L,M as C,e as T}from"./index-a0784e19.js";import{r as c}from"./vendor-851db8c1.js";import{S as M}from"./react-select-c8303602.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-hook-form-687afde5.js";import"./BottomDrawer-4cdfc0e3.js";import"./HistoryComponent-0c9f35b4.js";import"./date-fns-07266b7d.js";import"./PencilIcon-35185602.js";import"./TrashIcon-7d213648.js";import"./TimeSlotGrid-3140c36d.js";import"./@headlessui/react-a5400090.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";let s=new C;function N(){var h;const[o,d]=c.useState(null),[g,m]=c.useState(!1),[f,y]=c.useState([]),[E,A]=c.useState([]),[I,b]=c.useState([]),[w,n]=c.useState([]),S=async()=>{var r,a,l,p;m(!0);try{const t=await s.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${(r=o==null?void 0:o.user)==null?void 0:r.id}`,{},"GET");d(t==null?void 0:t.model);try{const e=(l=(a=t.model)==null?void 0:a.club)!=null&&l.exceptions?JSON.parse((p=t==null?void 0:t.model)==null?void 0:p.club.exceptions):[];n(Array.isArray(e)?e:[])}catch(e){console.error("Error parsing exceptions:",e),n([])}console.log("response",t)}catch(t){console.log(t)}finally{m(!1)}};async function _(){m(!0);try{s.setTable("clubs");const r=await s.callRestAPI({},"GETALL");y(r.list)}catch(r){console.error("Error fetching data:",r)}finally{m(!1)}}const j=async r=>{var a,l,p;m(!0);try{const t=await s.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${r.user_id}`,{},"GET");s.setTable("sports");const e=await s.callRestAPI({filter:[`club_id,cs,${r.value}`]},"GETALL");A(e==null?void 0:e.list),s.setTable("surface");const x=await s.callRestAPI({},"GETALL");b(x==null?void 0:x.list),d(t==null?void 0:t.model);try{const u=(l=(a=t==null?void 0:t.model)==null?void 0:a.club)!=null&&l.exceptions?JSON.parse((p=t==null?void 0:t.model)==null?void 0:p.club.exceptions):[];n(Array.isArray(u)?u:[])}catch(u){console.error("Error parsing exceptions:",u),n([])}}catch(t){console.error("Error fetching data:",t)}finally{m(!1)}};return c.useEffect(()=>{_()},[]),i.jsxs("div",{children:[g&&i.jsx(T,{}),i.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[i.jsxs("div",{className:"mb-4 max-w-xl",children:[i.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),i.jsx(M,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:f.map(r=>({value:r==null?void 0:r.id,label:r.name,user_id:r.user_id})),isMulti:!1,onChange:j})]}),i.jsx(v,{profileSettings:o,setProfileSettings:d,sports:E,exceptions:w,fetchSettings:S,setExceptions:n,club:o==null?void 0:o.club,courts:(o==null?void 0:o.courts.length)>0?o==null?void 0:o.courts.map(r=>({...r,court_settings:r.court_settings?(()=>{try{return JSON.parse(r.court_settings)}catch(a){return console.error("Error parsing court_setting:",a),{}}})():{}})):[],edit_api:`/v3/api/custom/courtmatchup/admin/profile-edit/${(h=o==null?void 0:o.user)==null?void 0:h.id}`})]})]})}const yt=L(N,"court_management","You don't have permission to access court management");export{yt as default};
