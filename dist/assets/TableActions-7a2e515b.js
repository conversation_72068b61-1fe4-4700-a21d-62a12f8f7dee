import{j as p}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{S as l}from"./index-a0784e19.js";import{A as n}from"./AddButton-df0c3574.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./AddButton.module-98aac587.js";const U=({actions:i,selectedItems:o})=>p.jsx("div",{className:"flex gap-2",children:Object.keys(i).map(r=>i[r].show).includes(!0)?p.jsx(p.Fragment,{children:Object.keys(i).map(r=>{var m,a;if(i[r].show&&!["select","add","export"].includes(r)){if(o&&(o==null?void 0:o.length)===1&&!((m=i[r])!=null&&m.multiple))return p.jsx(n,{showPlus:!1,className:`cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var t;(t=i[r])!=null&&t.action&&i[r].action(o)},children:l(r,{casetype:"capitalize",separator:" "})},r);if(o&&(o==null?void 0:o.length)>=1&&((a=i[r])!=null&&a.multiple))return p.jsx(n,{showPlus:!1,className:`cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var t;(t=i[r])!=null&&t.action&&i[r].action(o)},children:l(r,{casetype:"capitalize",separator:" "})},r)}}).filter(Boolean)}):null});export{U as default};
