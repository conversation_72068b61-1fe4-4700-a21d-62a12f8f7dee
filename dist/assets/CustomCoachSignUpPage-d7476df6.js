import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,b as k,u as te,f as ae,L as le}from"./vendor-851db8c1.js";import{u as re}from"./react-hook-form-687afde5.js";import{o as oe}from"./yup-2824f222.js";import{a as c,d as ie,e as ne,c as ce}from"./yup-54691517.js";import{M as me,A as ue,G as xe,e as B,d as de,b as $}from"./index-9f98cff7.js";import{A as pe}from"./AuthLayout-dbf1f583.js";import{B as A,a as O}from"./index.esm-b72032a7.js";import{P as he}from"./react-phone-input-2-57d1f0dd.js";/* empty css              */import{V as fe}from"./VerificationModal-3c93a328.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let v=new me;const os=()=>{const[x,Z]=i.useState(null),[h,z]=i.useState(!1),[f,I]=i.useState(!1);i.useState(!1);const[g,b]=i.useState(!1),V=s=>{const o={};return m.forEach(t=>{let a=c().required(`${t.label} is required`);t.type==="email"?a=a.email("Invalid email format"):t.type==="tel"?a=c().matches(/^\+?[1-9]\d{1,14}$/,"Invalid phone number").required(`${t.label} is required`):t.type==="select"&&(a=c().oneOf(t.options,`Please select a valid ${t.label.toLowerCase()}`).required(`${t.label} is required`)),o[t.value]=a}),o.confirm_password=c().required("Confirm Password is required").oneOf([ie("password")],"Passwords must match"),s==null||s.forEach(t=>{if(!m.find(a=>a.value===t.value)&&t.required){let a=c();t.type==="email"?a=a.email("Invalid email format"):t.type==="number"?(a=ne(),t.min&&(a=a.min(t.min)),t.max&&(a=a.max(t.max))):t.type==="tel"?a=c().matches(/^\+?[1-9]\d{1,14}$/,"Invalid phone number"):t.type==="select"&&(a=c().oneOf(t.options,`Please select a valid ${t.label.toLowerCase()}`)),o[t.value]=a.required(`${t.label} is required`)}}),ce(o).required()},m=[{value:"first_name",label:"First Name",type:"text",required:!0},{value:"last_name",label:"Last Name",type:"text",required:!0},{value:"phone",label:"Phone Number",type:"tel",required:!0},{value:"email",label:"Email",type:"email",required:!0},{value:"password",label:"Password",type:"password",required:!0},{value:"confirm_password",label:"Confirm Password",type:"password",required:!0}],[G,we]=i.useState(m),[R,je]=i.useState(V(m));i.useEffect(()=>{Y()},[]),k.useContext(ue);const{dispatch:y}=k.useContext(xe),[U,C]=i.useState(!1),[D,N]=i.useState(!1),[H,d]=i.useState(!1),T=te(),S=new URLSearchParams(T.search),_=S.get("club_id");S.get("redirect_uri");const[E,K]=i.useState("");ae();const{register:u,handleSubmit:W,setError:L,reset:J,formState:{errors:r},watch:Q,setValue:X}=re({resolver:oe(R)}),Y=async()=>{N(!0);try{const s=await v.callRawAPI(`/v3/api/custom/courtmatchup/coach/club/${_}`,{},"GET");Z(s.model)}catch(s){console.log("Error",s)}finally{N(!1)}},ee=async s=>{var o,t,a,q;try{d(!0);const l=m.map(n=>n.value),w={},P={};Object.keys(s).forEach(n=>{l.includes(n)?w[n]=s[n]:P[n]=s[n]}),delete w.confirm_password;const se={...w,role:"coach",verify:!1,is_refresh:!1,club_id:_,...P},p=await v.callRawAPI("/v3/api/custom/courtmatchup/coach/register",se,"POST");if(!p.error)$(y,"Succesfully Registered",4e3,"success"),b(!0),J(),d(!1);else if(d(!1),p.validation){const n=Object.keys(p.validation);for(let j=0;j<n.length;j++){const M=n[j];L(M,{type:"manual",message:p.validation[M]})}}}catch(l){d(!1),console.log("Error",l),$(y,l==null?void 0:l.message,4e3,"error"),L("email",{type:"manual",message:(t=(o=l==null?void 0:l.response)==null?void 0:o.data)!=null&&t.message?(q=(a=l==null?void 0:l.response)==null?void 0:a.data)==null?void 0:q.message:l==null?void 0:l.message})}},F=async s=>{C(!0);let o="coach";const t=await v.oauthLoginApi(s,o);window.open(t,"_self"),C(!1)};return e.jsxs(pe,{children:[e.jsxs("div",{className:"flex min-h-screen flex-col bg-white",children:[U&&e.jsx(B,{}),D&&e.jsx(B,{}),e.jsx("main",{className:"flex flex-grow flex-col bg-white",children:e.jsx("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("section",{className:"mt-6 flex w-full justify-center max-md:max-w-full",children:e.jsx("div",{className:"flex w-[553px] min-w-[240px] flex-col items-center pt-12",children:e.jsxs("div",{className:"flex w-[392px] max-w-full flex-col",children:[e.jsxs("div",{className:"mb-4 flex w-full flex-col",children:[e.jsx("div",{className:"flex w-[74px] items-start gap-4 self-center overflow-hidden rounded-[96px] p-2",children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_139_24350)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_139_24350)"}),e.jsxs("g",{filter:"url(#filter0_d_139_24350)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M24.5 37C24.5 33.6024 25.8556 30.5213 28.0554 28.2682C30.817 30.187 32.625 33.3824 32.625 37C32.625 40.6176 30.817 43.813 28.0554 45.7318C25.8556 43.4787 24.5 40.3976 24.5 37Z",fill:"#176448"}),e.jsx("path",{d:"M34.5 37C34.5 32.9105 32.5361 29.2796 29.5 26.9991C31.5892 25.4299 34.186 24.5 37 24.5C39.814 24.5 42.4109 25.4299 44.5 26.9991C41.4639 29.2796 39.5 32.9105 39.5 37C39.5 41.0895 41.4639 44.7204 44.5 47.0009C42.4109 48.5701 39.814 49.5 37 49.5C34.186 49.5 31.5892 48.5701 29.5 47.0009C32.5361 44.7204 34.5 41.0895 34.5 37Z",fill:"#176448"}),e.jsx("path",{d:"M45.9446 28.2683C48.1444 30.5213 49.5 33.6024 49.5 37C49.5 40.3976 48.1444 43.4787 45.9446 45.7317C43.183 43.813 41.375 40.6176 41.375 37C41.375 33.3824 43.183 30.187 45.9446 28.2683Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_139_24350",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_139_24350"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_139_24350",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_139_24350",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_139_24350",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("h1",{className:"mt-4 w-full text-center text-2xl font-medium leading-none text-gray-950",children:"Set up your profile"})]}),e.jsxs("form",{className:"flex w-full flex-col gap-4",onSubmit:W(ee),children:[G.map(s=>{var o;return s.value==="phone"?e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-950",htmlFor:s.value,children:[s.label,s.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(he,{country:"us",value:Q(s.value),onChange:t=>X(s.value,t),containerClass:"mt-1",inputClass:"!w-full !h-[42px] !rounded-xl !border-zinc-200",buttonClass:"!border-zinc-200 !rounded-l-xl",placeholder:"(*************"}),r[s.value]&&e.jsx("span",{className:"mt-1 text-sm text-red-500",children:r[s.value].message})]},s.value):s.type==="select"?e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-950",htmlFor:s.value,children:[s.label,s.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("select",{className:"mt-1 flex w-full items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-2.5 pl-3 pr-2.5",id:s.value,...u(s.value),children:[e.jsxs("option",{value:"",children:["Select ",s.label]}),(o=s.options)==null?void 0:o.map(t=>e.jsx("option",{value:t,children:t},t))]}),r[s.value]&&e.jsx("span",{className:"mt-1 text-sm text-red-500",children:r[s.value].message})]},s.value):s.value==="email"?e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-950",htmlFor:s.value,children:[s.label,s.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{className:"mt-1 flex w-full items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-2.5 pl-3 pr-2.5",id:s.value,type:s.type||"text",placeholder:s.label,...u(s.value,{onChange:t=>K(t.target.value)})}),r[s.value]&&e.jsx("span",{className:"mt-1 text-sm text-red-500",children:r[s.value].message}),e.jsxs("div",{className:" mt-2 flex items-center gap-1",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("p",{className:" text-sm text-gray-500",children:"We will send activation link to this email."})]})]},s.value):s.value==="password"?e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-950",htmlFor:s.value,children:[s.label,s.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"mt-1 flex w-full items-center justify-between overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-1 pr-2.5",children:[e.jsx("input",{id:s.value,type:h?"text":"password",className:"h-full w-full border-none px-3 outline-none focus:outline-none focus:ring-0",placeholder:s.label,...u(s.value)}),e.jsx("button",{type:"button",className:"text-gray-500",onClick:t=>{t.preventDefault(),t.stopPropagation(),z(!h)},children:h?e.jsx(A,{className:"h-5 w-5"}):e.jsx(O,{className:"h-5 w-5"})})]}),r[s.value]&&e.jsx("span",{className:"mt-1 text-sm text-red-500",children:r[s.value].message})]},s.value):s.value==="confirm_password"?e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-950",htmlFor:s.value,children:[s.label,s.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"mt-1 flex w-full items-center justify-between overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-1 pr-2.5",children:[e.jsx("input",{id:s.value,type:f?"text":"password",className:"h-full w-full border-none px-3 outline-none focus:outline-none focus:ring-0",placeholder:s.label,...u(s.value)}),e.jsx("button",{type:"button",className:"text-gray-500",onClick:t=>{t.preventDefault(),t.stopPropagation(),I(!f)},children:f?e.jsx(A,{className:"h-5 w-5"}):e.jsx(O,{className:"h-5 w-5"})})]}),r[s.value]&&e.jsx("span",{className:"mt-1 text-sm text-red-500",children:r[s.value].message})]},s.value):e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-950",htmlFor:s.value,children:[s.label,s.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{className:"mt-1 flex w-full items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-2.5 pl-3 pr-2.5",id:s.value,type:s.type||"text",placeholder:s.label,...u(s.value)}),r[s.value]&&e.jsx("span",{className:"mt-1 text-sm text-red-500",children:r[s.value].message})]},s.value)}),e.jsx(de,{type:"submit",loading:H,className:"mt-6 w-full gap-2.5 self-stretch overflow-hidden whitespace-nowrap rounded-xl bg-emerald-800 px-2.5 py-3 text-center text-lg font-medium leading-none tracking-tight text-white shadow-sm",children:"Sign up"})]}),e.jsxs("div",{className:"my-6 flex items-center justify-center",children:[e.jsx("div",{className:"h-[1px] flex-1 bg-zinc-200"}),e.jsx("span",{className:"mx-4 text-sm text-gray-500",children:"OR"}),e.jsx("div",{className:"h-[1px] flex-1 bg-zinc-200"})]}),e.jsxs("div",{children:[e.jsxs("button",{onClick:()=>F("apple"),className:"mb-4 flex w-full items-center justify-center gap-2 rounded-xl border border-solid border-zinc-200 bg-white px-6 py-3",children:[e.jsx("div",{children:e.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.5923 10.0523C14.6133 12.3223 16.5836 13.0777 16.6055 13.0873C16.5888 13.1406 16.2906 14.1638 15.5674 15.2207C14.9422 16.1345 14.2933 17.0449 13.2712 17.0638C12.2668 17.0823 11.9439 16.4682 10.7956 16.4682C9.64763 16.4682 9.28881 17.0449 8.33805 17.0823C7.35141 17.1197 6.60006 16.0942 5.9697 15.1838C4.68158 13.3215 3.69718 9.92136 5.01897 7.62624C5.67561 6.48647 6.84907 5.76472 8.12276 5.74621C9.09162 5.72773 10.0061 6.39803 10.5984 6.39803C11.1903 6.39803 12.3016 5.59194 13.4698 5.71032C13.9589 5.73068 15.3318 5.90788 16.2133 7.19823C16.1423 7.24226 14.5752 8.15453 14.5923 10.0523ZM12.7047 4.47826C13.2285 3.8442 13.581 2.96153 13.4848 2.08328C12.7298 2.11363 11.8168 2.58642 11.2752 3.22013C10.7899 3.78131 10.3648 4.67951 10.4795 5.54038C11.3211 5.60549 12.1808 5.11272 12.7047 4.47826Z",fill:"black"})})}),e.jsx("span",{className:"text-sm font-medium text-gray-950",children:"Continue with Apple"})]}),e.jsxs("button",{onClick:()=>F("google"),className:"flex w-full items-center justify-center gap-2 rounded-xl border border-solid border-zinc-200 bg-white px-6 py-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1531 8.63647V11.541H14.2718C14.091 12.4751 13.5482 13.2661 12.7342 13.7979L15.218 15.6866C16.6651 14.3775 17.5 12.4547 17.5 10.1706C17.5 9.63884 17.4513 9.12742 17.3609 8.63656L10.1531 8.63647Z",fill:"#4285F4"}),e.jsx("path",{d:"M3.32103 6.63879C2.79926 7.64785 2.50012 8.78651 2.50012 10.0001C2.50012 11.2138 2.79926 12.3524 3.32103 13.3615C3.32103 13.3682 5.86747 11.4251 5.86747 11.4251C5.71441 10.9751 5.62394 10.4978 5.62394 10.0001C5.62394 9.50226 5.71441 9.02501 5.86747 8.57501L3.32103 6.63879Z",fill:"#FBBC05"}),e.jsx("path",{d:"M10.153 5.48638C11.2801 5.48638 12.2819 5.86819 13.082 6.60457L15.2736 4.45685C13.9447 3.24323 12.2194 2.5 10.153 2.5C7.16135 2.5 4.5802 4.1841 3.32092 6.63866L5.86728 8.57504C6.47254 6.80228 8.1632 5.48638 10.153 5.48638Z",fill:"#EA4335"}),e.jsx("path",{d:"M5.86399 11.4276L5.30381 11.8479L3.32092 13.3615C4.5802 15.8092 7.1612 17.5001 10.1528 17.5001C12.2191 17.5001 13.9515 16.8319 15.2178 15.6865L12.734 13.7978C12.0522 14.2478 11.1825 14.5206 10.1528 14.5206C8.16304 14.5206 6.47245 13.2047 5.86712 11.4319L5.86399 11.4276Z",fill:"#34A853"})]})}),e.jsx("span",{className:"text-sm font-medium text-gray-950",children:"Continue with Google"})]})]}),e.jsx("div",{className:"mt-5 text-center",children:e.jsxs(le,{to:x?`/coach/login?club_id=${x==null?void 0:x.id}`:"/coach/login",className:"text-gray-500",children:["Already a member?"," ",e.jsx("span",{className:"text-black underline underline-offset-2",children:"Log in"})]})})]})})})})})]}),g&&e.jsx(fe,{isOpen:g,onClose:()=>b(!1),email:E,title:"Link Sent",description:`We've send the link to: ${E} Check yor email and follow the instructions there.`})]})};export{os as default};
