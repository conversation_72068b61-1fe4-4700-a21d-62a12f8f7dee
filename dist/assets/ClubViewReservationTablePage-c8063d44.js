import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,j as n}from"./vendor-851db8c1.js";import"./yup-54691517.js";import{M as x,G as o,t as p}from"./index-9f98cff7.js";import{S as f}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let a=new x;const Z=()=>{r.useContext(o);const{dispatch:l}=r.useContext(o),[e,d]=r.useState({}),[c,i]=r.useState(!0),m=n();return r.useEffect(function(){(async function(){try{i(!0),a.setTable("reservation");const t=await a.callRestAPI({id:Number(m==null?void 0:m.id),join:""},"GET");t.error||(d(t.model),i(!1))}catch(t){i(!1),console.log("error",t),p(l,t.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:c?s.jsx(f,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Reservation"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Booking Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.booking_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Buddy Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.buddy_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"User Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{Z as default};
