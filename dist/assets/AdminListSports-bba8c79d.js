import{j as i}from"./@nivo/heatmap-ba1ecfff.js";import{S as g}from"./SportList-f554881c.js";import{r as o,u as L,b as j}from"./vendor-851db8c1.js";import{G as y,e as E,M as A}from"./index-a0784e19.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./PlusIcon-7e8d14d7.js";import"./PencilIcon-35185602.js";import"./TrashIcon-aaaccaf2.js";import"./InformationCircleIcon-d35f3488.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let C=new A;function lt(){var p;const[l,n]=o.useState(null),[u,s]=o.useState(!1),[d,f]=o.useState(null),[S,b]=o.useState([]),x=L(),{dispatch:h}=j.useContext(y),r=(p=x.state)==null?void 0:p.clubId,m=async()=>{var a,e,c;if(r){s(!0);try{const t=await C.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${r}`,{},"GET");f((a=t==null?void 0:t.model)==null?void 0:a.club),b(((e=t==null?void 0:t.model)==null?void 0:e.sports)||[]),n((c=t==null?void 0:t.model)==null?void 0:c.user)}catch(t){console.log(t)}finally{s(!1)}}};return o.useEffect(()=>{h({type:"SETPATH",payload:{path:"sports"}}),m()},[r]),i.jsxs("div",{className:"h-screen p-8",children:[u&&i.jsx(E,{}),i.jsx(g,{club:d,fetchSettings:m,sports:S,isAdmin:!0,clubUser:l})]})}export{lt as default};
