import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as o}from"./vendor-851db8c1.js";import{M as T,A as z,G as F,t as I}from"./index-a0784e19.js";import{o as G}from"./yup-2824f222.js";import{u as L}from"./react-hook-form-687afde5.js";import{c as M}from"./yup-54691517.js";import{P as $}from"./index-eb1bc208.js";import"./AddButton.module-98aac587.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";const h=[{header:"Product",type:"metadata",pre_accessor:"metadata",accessor:"app_product_name"},{header:"Status",accessor:"status"},{header:"Currency",accessor:"currency"},{header:"Amount",accessor:"amount",type:"currency"},{header:"Amount captured",accessor:"amount_captured",type:"currency"},{header:"Amount refunded",accessor:"amount_refunded",type:"currency"},{header:"Created at",accessor:"created",type:"timestamp"}],De=()=>{var u;const l=new T,{dispatch:g,state:B}=o.useContext(z),{dispatch:c}=o.useContext(F),[p,x]=o.useState(null),[r,f]=o.useState({}),[m,d]=o.useState(10),[y,H]=o.useState(0),[w,K]=o.useState(0),[b,j]=o.useState(!1),[S,v]=o.useState(!1),N=M({}),{register:O,handleSubmit:P,formState:{errors:U}}=L({resolver:G(N)});function C(s){(async function(){d(s),await i({limit:s})})()}function k(){(async function(){await i({limit:m,before:r==null?void 0:r.data[0].id})})()}function A(){(async function(){await i({limit:m,after:r==null?void 0:r.data[(r==null?void 0:r.data.length)-1].id})})()}async function i(s){var n,a;try{const{list:t,limit:D,error:E,message:R}=await l.getStripeCharges(s);if(console.log(t),E&&showToast(c,R,5e3),!t)return;p||x(((n=t==null?void 0:t.data[0])==null?void 0:n.id)??""),f(t),d(+D),j(p&&p!==((a=t.data[0])==null?void 0:a.id)),v(t.has_more)}catch(t){console.error("ERROR",t),showToast(c,t.message,5e3),I(g,t.message)}}const _=s=>{i({})};return o.useEffect(()=>{c({type:"SETPATH",payload:{path:"orders"}}),async function(){i({})}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"p-5 bg-white shadow rounded mb-10",onSubmit:P(_),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsx("div",{className:"filter-form-holder mt-10 flex flex-wrap"}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block px-6 py-2.5 bg-blue-600 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg transition duration-150 ease-in-out",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>i(1),className:"inline-block px-6 py-2.5 bg-gray-800 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg transition duration-150 ease-in-out",children:"Reset"})]})]}),e.jsxs("div",{className:"overflow-x-auto  p-5 bg-white shadow rounded",children:[e.jsx("div",{className:"mb-3 text-center justify-between w-full flex  ",children:e.jsx("h4",{className:"text-2xl font-medium",children:"Prices "})}),e.jsx("div",{className:"shadow overflow-x-auto border-b border-gray-200 ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:h.map((s,n)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:[s.header,e.jsx("span",{children:s.isSorted?s.isSortedDesc?" ▼":" ▲":""})]},n))})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:(u=r==null?void 0:r.data)==null?void 0:u.map((s,n)=>e.jsx("tr",{children:h.map((a,t)=>a.accessor==""?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap"},t):a.mapping?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.mapping[s[a.accessor]]},t):a.type==="timestamp"?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:new Date(s[a.accessor]*1e3).toLocaleString("en-US")},t):a.type==="currency"?e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:["$",Number(s[a.accessor]/100).toFixed(2)]},t):a.type==="metadata"?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s[a.pre_accessor][a.accessor]??"n/a"},t):e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s[a.accessor]},t))},n))})]})})]}),e.jsx($,{currentPage:w,pageCount:y,pageSize:m,canPreviousPage:b,canNextPage:S,updatePageSize:C,previousPage:k,nextPage:A})]})};export{De as default};
