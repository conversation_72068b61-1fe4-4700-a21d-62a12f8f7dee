import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{e as ve,d as se,M as he,G as Ne,b as X,a as Pe,a1 as Be,ac as De,ad as Je,a3 as be,R as qe,A as Ye,u as We,m as Ke,t as Te}from"./index-a0784e19.js";import{r as a,b as je,f as Qe}from"./vendor-851db8c1.js";import{A as Xe}from"./AuthLayout-63e138af.js";import{u as Ve,b as es}from"./react-hook-form-687afde5.js";import{u as ss,G as ts,M as rs}from"./@react-google-maps/api-bec1613d.js";import{u as ls,g as as,a as ns}from"./use-places-autocomplete-4cb4aca6.js";import{a as fe,q as is}from"./@headlessui/react-a5400090.js";import{S as os}from"./SplashScreenPagePreview-46f2da97.js";import{B as cs}from"./BottomDrawer-4cdfc0e3.js";import{I as ds}from"./ImageCropModal-bf06efce.js";import{I as ps}from"./InformationCircleIcon-d35f3488.js";import{P as ms}from"./PencilIcon-35185602.js";import{T as xs}from"./TrashIcon-aaaccaf2.js";import{P as Ae}from"./PlusIcon-7e8d14d7.js";import"./lodash-91d5d207.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-image-crop-1f5038af.js";const hs=["places"],us="AIzaSyC6zItKyKbnIcdpgwNoRIByQEvezUbdFAA",gs=({onNext:G,register:x,errors:_,setValue:j,defaultValues:r,isSubmitting:l})=>{const{isLoaded:f,loadError:b}=ss({googleMapsApiKey:us,libraries:hs}),[u,N]=a.useState(()=>{if(r!=null&&r.club_location)try{const U=JSON.parse(r.club_location);return{lat:U.lat,lng:U.lng}}catch{return null}return null}),[p,T]=a.useState(null),R=a.useCallback(U=>{T(U)},[]),L=u||{lat:51.5074,lng:-.1278};return b?e.jsx("div",{children:"Error loading maps"}):f?e.jsxs("div",{className:"w-full max-w-xl",children:[e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg"}),e.jsx("h2",{className:"text-2xl font-bold",children:"Location"})]}),e.jsx(fs,{setSelectedLocation:N,selectedLocation:u,map:p,onLoad:R,defaultCenter:L,onNext:G,setValueProp:j,isLoaded:f,defaultValues:r,isSubmitting:l})]}):e.jsx(ve,{})},fs=({setSelectedLocation:G,selectedLocation:x,map:_,onLoad:j,defaultCenter:r,onNext:l,setValueProp:f,isLoaded:b,defaultValues:u,isSubmitting:N})=>{const p=je.useMemo(()=>{try{if(u!=null&&u.club_location)return JSON.parse(u.club_location).address||""}catch(A){console.error("Error parsing club_location:",A)}return""},[u]),{ready:T,value:R,suggestions:{status:L,data:U},setValue:$,clearSuggestions:ee}=ls({debounce:300,initOnMount:!0,cache:!1,googleMaps:b?window.google.maps:void 0,defaultValue:p});a.useEffect(()=>{p&&$(p,!1)},[p,$]);const q=async A=>{$(A,!1),ee();try{const w=await as({address:A}),{lat:h,lng:c}=await ns(w[0]);G({lat:h,lng:c}),f("club_location",JSON.stringify({lat:h,lng:c,address:w[0].formatted_address})),_==null||_.panTo({lat:h,lng:c})}catch(w){console.error("Error: ",w)}},Q=A=>{$(A.target.value)};return e.jsxs("form",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Club location"}),e.jsx(fe,{onChange:q,children:e.jsxs("div",{className:"relative mt-1",children:[e.jsx("div",{className:"relative w-full cursor-default overflow-hidden rounded bg-white text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm",children:e.jsx(fe.Input,{className:"mb-3 w-full rounded border-[1px] border-[#C6C6C6] bg-[#F8F8F8] p-2 px-4 py-2 leading-tight text-gray-700 focus:border-blue-500 focus:ring-1 focus:ring-blue-500",displayValue:A=>A||R,onChange:Q,value:R,disabled:!T,placeholder:T?"Search location":"Loading..."})}),e.jsx(is,{as:a.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",afterLeave:()=>$(null),children:e.jsx(fe.Options,{className:"absolute z-[999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base ring-1 ring-black/5 focus:outline-none sm:text-sm",children:L==="OK"&&e.jsx(e.Fragment,{children:U.length===0&&R!==""?e.jsx("div",{className:"relative cursor-default select-none px-4 py-2 text-gray-700",children:"Nothing found."}):U.map(({place_id:A,description:w})=>e.jsx(fe.Option,{className:({active:h})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${h?"bg-blue-600 text-white":"text-gray-900"}`,value:w,children:({selected:h,active:c})=>e.jsxs(e.Fragment,{children:[e.jsx("span",{className:`block truncate ${h?"font-medium":"font-normal"}`,children:w}),h?e.jsx("span",{className:`absolute inset-y-0 left-0 flex items-center pl-3 ${c?"text-white":"text-blue-600"}`,children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",viewBox:"0 0 50 50",children:[e.jsx("path",{fill:"currentColor",d:"M25 42c-9.4 0-17-7.6-17-17S15.6 8 25 8s17 7.6 17 17s-7.6 17-17 17m0-32c-8.3 0-15 6.7-15 15s6.7 15 15 15s15-6.7 15-15s-6.7-15-15-15"}),e.jsx("path",{fill:"currentColor",d:"m23 32.4l-8.7-8.7l1.4-1.4l7.3 7.3l11.3-11.3l1.4 1.4z"})]})}):null]})},A))})})})]})}),e.jsxs("div",{className:"mt-3 flex items-center gap-2",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{className:"text-xs text-gray-500",children:"Search and drop pin to the location"})]})]}),e.jsx("div",{className:"mt-4 h-[300px] w-full",children:e.jsx(ts,{mapContainerClassName:"w-full h-full rounded-xl",center:x||r,zoom:13,onLoad:j,children:x&&e.jsx(rs,{position:x})})}),e.jsx(se,{onClick:l,loading:N,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"})]})};let ys=new he;function bs({onNext:G,register:x,errors:_,setValue:j,clubProfile:r,defaultValues:l}){var _e,Se,ke,Ee,Me,Le,Ie;const[f,b]=a.useState(0),[u,N]=a.useState([]),[p,T]=a.useState(null),[R,L]=a.useState(null),[U,$]=a.useState(l==null?void 0:l.club_logo),[ee,q]=a.useState(!1),[Q,A]=a.useState(!1),[w,h]=a.useState(null),[c,C]=a.useState(null),[I,z]=a.useState(!1),[t,k]=a.useState(!1),[S,Z]=a.useState(l==null?void 0:l.name),[W,K]=a.useState(S),[te,re]=a.useState(((_e=l==null?void 0:l.splash_screen)==null?void 0:_e.slideshow_delay)||6e3),[d,s]=a.useState(()=>{var o;if((o=l==null?void 0:l.splash_screen)!=null&&o.images){const y=new Array(9).fill(null);return l.splash_screen.images.forEach((F,O)=>{F&&F.url&&(y[O]={url:F.url,isDefault:!0,id:F.id||`default-${O}`,type:F.type||"image"})}),y}return new Array(9).fill(null)}),[m,n]=a.useState(!1),H=a.useRef(),v=a.useRef(),Y=a.useRef(),D=a.useRef(),{dispatch:J}=a.useContext(Ne),[oe,i]=a.useState(!1);a.useEffect(()=>{if(l!=null&&l.splash_screen)try{const o=l==null?void 0:l.splash_screen;H.current&&o.bio&&(H.current.value=o.bio)}catch(o){console.error("Error parsing splash screen data:",o)}},[l]),a.useEffect(()=>{const o=f*3,y=d.slice(o,o+3);N(y)},[f,d]);const g=async o=>{try{let y=new FormData;y.append("file",o);const F=o.type.startsWith("video/");let O;return O=await ys.uploadImage(y),O.url}catch(y){return console.error("Upload error:",y),X(J,"Failed to upload file. Please try again.",3e3,"error"),null}},E=()=>d.slice(0,3).filter(y=>y!==null).length,M=o=>{const y=E(),F=Math.floor(o/3);if(F===0)return!0;const O=F*3;return d.slice(O,O+3).filter(me=>me!==null).length<y},P=(o,y)=>{const F=o.target.files[0];if(!F)return;const O=f*3+y;if(!M(O)){const xe=E();X(J,`You can only upload up to ${xe} image${xe!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}if(!["image/jpeg","image/png","image/gif","video/mp4","video/quicktime","video/x-m4v","application/pdf"].includes(F.type)){X(J,"Please upload a valid file type (JPEG, PNG, GIF, MP4, or PDF)",3e3,"error");return}if(F.size>50*1024*1024){X(J,"File size must be less than 50MB",3e3,"error");return}const ie=[...d],me=URL.createObjectURL(F);let ae="image";F.type.startsWith("video/")?ae="video":F.type==="application/pdf"&&(ae="pdf"),ie[O]={file:F,url:me,id:Date.now(),isDefault:!1,type:ae,previewUrl:me},s(ie)},le=o=>{o.preventDefault(),o.stopPropagation()},ne=o=>{o.preventDefault(),o.stopPropagation()},pe=o=>{o.preventDefault(),o.stopPropagation()},ue=(o,y)=>{o.preventDefault(),o.stopPropagation();const F=o.dataTransfer.files;if(F.length>0){const O=f*3+y;if(!M(O)){const ie=E();X(J,`You can only upload up to ${ie} image${ie!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}const V={target:{files:[F[0]]}};P(V,y)}},ge=o=>{const y=f*3+o,F=[...d],O=F[y];O&&(!O.isDefault&&O.url&&URL.revokeObjectURL(O.url),F[y]=null,s(F))},B=()=>{b(o=>o+1)},ce=()=>{b(o=>Math.max(0,o-1))},Ce=()=>u,Ue=async()=>{var o,y,F,O;try{n(!0);const V=(o=H.current)==null?void 0:o.value;if(!(V!=null&&V.trim())){X(J,"Please enter a bio",3e3,"error");return}const ie=await Promise.all(d.map(async ae=>{if(!ae)return null;if(ae.isDefault)return ae;{const xe=await g(ae.file);return xe?(ae.previewUrl&&URL.revokeObjectURL(ae.previewUrl),{url:xe,isDefault:!0,id:`default-${Date.now()}-${Math.random()}`,type:ae.file.type.startsWith("video/")?"video":"image"}):null}})),me={bio:V.trim(),images:ie,slideshow_delay:te,button_text:((y=v.current)==null?void 0:y.value)||"Let the club know you're interested",phone:((F=Y.current)==null?void 0:F.value)||"",email:((O=D.current)==null?void 0:O.value)||""};j("splash_screen",JSON.stringify(me)),j("name",S),j("club_logo",U),await G(),s(new Array(9).fill(null)),H.current.value="",b(0)}catch(V){console.error("Submission failed:",V),X(J,"Failed to submit. Please try again.",3e3,"error")}finally{n(!1)}},Ge=o=>{const y=o.target.files[0];if(!y)return;if(y.size>2*1024*1024){alert("File size exceeds 2MB limit. Please choose a smaller file.");return}C(y.type);const F=new FileReader;F.onload=()=>{h(F.result),A(!0)},F.readAsDataURL(y)},Ze=async o=>{q(!0),T(URL.createObjectURL(o));const y=c==="image/png",F=await g(o);$(F),L(new File([o],`cropped_logo.${y?"png":"jpg"}`,{type:y?"image/png":"image/jpeg"})),Pe(URL.createObjectURL(o)),q(!1)};a.useEffect(()=>()=>{p&&URL.revokeObjectURL(p)},[p]);const Re=()=>{K((l==null?void 0:l.name)||""),z(!0)},$e=()=>{Z(W),K(W),z(!1)},ze=()=>{K(S),z(!1)};return e.jsxs("div",{className:"flex max-w-xl flex-col gap-4 rounded-lg bg-white",children:[ee&&e.jsx(ve,{}),e.jsxs("div",{className:"flex flex-col items-center justify-center text-center",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_507_13438)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_507_13438)"}),e.jsxs("g",{filter:"url(#filter0_d_507_13438)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.5 29.1875C24.5 27.9794 25.4794 27 26.6875 27H47.3125C48.5206 27 49.5 27.9794 49.5 29.1875V44.8125C49.5 46.0206 48.5206 47 47.3125 47H26.6875C25.4794 47 24.5 46.0206 24.5 44.8125V29.1875ZM39.1875 33.25C37.9794 33.25 37 34.2294 37 35.4375V38.5625C37 39.7706 37.9794 40.75 39.1875 40.75H42.3125C43.5206 40.75 44.5 39.7706 44.5 38.5625V35.4375C44.5 34.2294 43.5206 33.25 42.3125 33.25H39.1875ZM30.4375 33.5625C29.9197 33.5625 29.5 33.9822 29.5 34.5C29.5 35.0178 29.9197 35.4375 30.4375 35.4375H33.5625C34.0803 35.4375 34.5 35.0178 34.5 34.5C34.5 33.9822 34.0803 33.5625 33.5625 33.5625H30.4375ZM30.4375 38.5625C29.9197 38.5625 29.5 38.9822 29.5 39.5C29.5 40.0178 29.9197 40.4375 30.4375 40.4375H33.5625C34.0803 40.4375 34.5 40.0178 34.5 39.5C34.5 38.9822 34.0803 38.5625 33.5625 38.5625H30.4375Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_507_13438",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_507_13438"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_507_13438",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_507_13438",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_507_13438",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"text-2xl font-medium",children:"Description"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-2 text-lg font-semibold",children:"Tell us about your club"}),e.jsxs("p",{className:"flex items-start gap-2 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{children:"This info will be visible to users who want to book your club. You can edit this content into your Club Panel later."})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"relative h-[100px] w-[100px] overflow-hidden rounded-lg bg-gray-100",children:e.jsx("img",{src:p||U||((Se=r==null?void 0:r.club)==null?void 0:Se.club_logo)||"/logo.png",alt:"Club logo",className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"w-full space-y-1",children:[e.jsx("span",{children:"Club logo"}),e.jsx("div",{className:"flex justify-between py-1 text-xs text-gray-500",children:e.jsx("span",{children:"Min 400x400px, PNG or JPEG"})}),e.jsx("p",{className:"mb-2 text-xs text-gray-500",children:"This logo will be displayed on your club portal and as a favicon in browser tabs. For best results, use a square image with a clear, recognizable design."}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(se,{onClick:()=>{T(null),L(null)},className:"rounded-md border border-red-500 bg-white px-2 py-1 text-xs text-red-500",children:"Remove"}),e.jsx("input",{type:"file",id:"logo-upload",className:"hidden",accept:"image/**",onChange:Ge}),e.jsx(se,{onClick:()=>document.getElementById("logo-upload").click(),className:"rounded-md border border-gray-400 bg-white px-2 py-1 text-xs text-gray-600",children:"Change Logo"})]})]})]}),e.jsx("div",{className:"mt-4 flex items-center gap-4 border-y border-gray-200 py-3",children:e.jsxs("div",{className:" w-full",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Club name"}),I?e.jsxs("div",{className:"space-y-2",children:[e.jsx("input",{type:"text",value:W,onChange:o=>K(o.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-lg font-medium focus:border-blue-500 focus:outline-none",placeholder:"Enter club name"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(se,{onClick:$e,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-1 text-sm text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:ze,className:"text-sm text-primaryBlue hover:underline",children:"Cancel"})]})]}):e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-medium",children:S||"Club name"}),e.jsx("button",{onClick:Re,className:"text-sm text-primaryBlue hover:underline",children:"Edit"})]})]})}),e.jsx("div",{children:e.jsx("button",{className:"underline",onClick:()=>i(!0),children:"Page preview"})}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Upload images"}),e.jsx("p",{className:"mb-2 text-sm text-gray-500",children:"JPEG, PNG, PDF, and MP4 formats, up to 50 MB. Drag and drop files or click to browse."}),f>0&&e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2",children:e.jsxs("p",{className:"text-xs text-blue-700",children:[e.jsx("strong",{children:"Note:"})," You can upload up to"," ",E()," image",E()!==1?"s":""," per slide based on your Slide 1 pattern."]})}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"grid grid-cols-2 items-center justify-items-center gap-x-2 gap-y-4",children:[0,1,2].map(o=>{const y=Ce()[o],F=f*3+o,O=!M(F);return e.jsxs("div",{className:`relative h-[100px] max-h-[100px] w-full rounded-xl border-2 border-dashed transition-colors ${o===0&&"col-span-3"} ${O?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:O?void 0:le,onDragEnter:O?void 0:ne,onDragLeave:O?void 0:pe,onDrop:O?void 0:V=>ue(V,o),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:V=>P(V,o),id:`file-input-${o}`,disabled:O}),e.jsx("label",{htmlFor:O?void 0:`file-input-${o}`,className:`absolute inset-0 ${O?"cursor-not-allowed":"cursor-pointer"}`,children:y&&y.url?e.jsxs("div",{className:"relative h-full w-full",children:[y.type==="video"?e.jsxs("video",{src:y.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:y.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):y.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:y.url,alt:`Upload ${o+1}`,className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:V=>{V.preventDefault(),V.stopPropagation(),ge(o)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),y.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:O?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${f}-${o}`)})},f),e.jsxs("div",{className:"mt-4 flex items-center justify-center gap-4",children:[e.jsx("button",{onClick:ce,disabled:f===0,className:`rounded-full bg-white p-2 shadow-md ${f===0?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsxs("span",{className:"text-sm",children:["Slide ",f+1]}),e.jsx("button",{onClick:B,disabled:f===2,className:`rounded-full bg-white p-2 shadow-md ${f===2?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})})]})]})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Slideshow Delay (seconds)"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"number",min:"1",max:"60",value:te/1e3,onChange:o=>re(Math.max(1e3,Math.min(6e4,o.target.value*1e3))),className:"w-24 rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"}),e.jsx("span",{className:"text-sm text-gray-500",children:"seconds"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose between 1 and 60 seconds (default: 6 seconds)"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Bio"}),e.jsx("textarea",{ref:H,rows:4,className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Button Text"}),e.jsx("input",{type:"text",ref:v,defaultValue:(l==null?void 0:l.button_text)||"Let the club know you're interested",onChange:o=>{const y={...JSON.parse((l==null?void 0:l.splash_screen)||"{}"),button_text:o.target.value};j("splash_screen",JSON.stringify(y))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter button text"})]}),e.jsxs("div",{className:"mb-4 space-y-4",children:[e.jsx("h3",{className:"text-sm font-medium",children:"Contact Information"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Phone Number"}),e.jsx("input",{type:"tel",ref:Y,defaultValue:(l==null?void 0:l.phone)||"",onChange:o=>{const y={...JSON.parse((l==null?void 0:l.splash_screen)||"{}"),phone:o.target.value};j("splash_screen",JSON.stringify(y))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter phone number"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Email Address"}),e.jsx("input",{type:"email",ref:D,defaultValue:(l==null?void 0:l.email)||"",onChange:o=>{const y={...JSON.parse((l==null?void 0:l.splash_screen)||"{}"),email:o.target.value};j("splash_screen",JSON.stringify(y))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter email address"})]})]}),e.jsx(se,{onClick:Ue,loading:m,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"}),e.jsx(cs,{isOpen:oe,onClose:()=>i(!1),title:"Preview",children:e.jsx(os,{clubName:(ke=r==null?void 0:r.club)==null?void 0:ke.name,image:r==null?void 0:r.name,description:(Ee=H.current)==null?void 0:Ee.value,imageList:d,clubLogo:U,slideshowDelay:te,buttonText:((Me=v.current)==null?void 0:Me.value)||"Let the club know you're interested",phone:((Le=Y.current)==null?void 0:Le.value)||"",email:((Ie=D.current)==null?void 0:Ie.value)||""})}),e.jsx(ds,{isOpen:Q,onClose:()=>A(!1),image:w,onCropComplete:Ze})]})}const de=new he;function js({onNext:G,register:x,errors:_,setValue:j,clubProfile:r,defaultValues:l,defaultSports:f,fetchClubProfile:b}){const[u,N]=a.useState(!1),[p,T]=a.useState(!1),[R,L]=a.useState(!1),[U,$]=a.useState("add"),[ee,q]=a.useState(null),[Q,A]=a.useState(!1),[w,h]=a.useState(!1),[c,C]=a.useState(!1),[I,z]=a.useState({name:"",types:[],sport_id:null}),{dispatch:t}=a.useContext(Ne),[k,S]=a.useState([]);a.useEffect(()=>{var i,g;if(((i=l==null?void 0:l.sports)==null?void 0:i.length)>0){const E=(g=l==null?void 0:l.sports)==null?void 0:g.map(M=>{var P;return{sport_id:M.id,name:M.name,club_id:M.club_id,types:((P=M.sport_types)==null?void 0:P.map(le=>({name:le.type,sub_type:le.subtype||[],club_sport_type_id:le.club_sport_type_id})))||[],status:M.status||0}});S(E)}else S([])},[l==null?void 0:l.sports]);const Z=async i=>{A(!0);try{de.setTable("sports");const g=await de.callRestAPI({id:i.sport_id,status:i.status===1?0:1},"PUT");await b()}catch(g){console.error(g)}finally{A(!1)}},W=async i=>{A(!0);try{de.setTable("sports");const g=await de.callRestAPI({id:i.sport_id},"DELETE");S(E=>E.filter(M=>M.sport_id!==i.sport_id))}catch(g){console.error(g)}finally{A(!1)}},K=i=>{$("edit"),q(i),z({name:i.name,sport_id:i.sport_id,types:i.types||[]}),L(!0)},te=()=>{$("add"),q(null),z({name:"",types:[],sport_id:null}),L(!0)},re=()=>{L(!1),q(null),z({name:"",types:[],sport_id:null})},d=()=>{z(i=>({...i,types:[...i.types,{name:"",sub_type:[]}]}))},s=async(i,g)=>{g&&await oe(g),z(E=>({...E,types:E.types.filter((M,P)=>P!==i)}))},m=(i,g)=>{z(E=>({...E,types:E.types.map((M,P)=>P===i?{...M,name:g}:M)}))},n=i=>{z(g=>({...g,types:g.types.map((E,M)=>M===i?{...E,sub_type:[...E.sub_type,""]}:E)}))},H=(i,g)=>{z(E=>({...E,types:E.types.map((M,P)=>P===i?{...M,sub_type:M.sub_type.filter((le,ne)=>ne!==g)}:M)}))},v=(i,g,E)=>{z(M=>({...M,types:M.types.map((P,le)=>le===i?{...P,sub_type:P.sub_type.map((ne,pe)=>pe===g?E:ne)}:P)}))},Y=async()=>{var i;if(I.name.trim())try{h(!0);const g={name:I.name,club_id:(i=r==null?void 0:r.club)==null?void 0:i.id,types:I.types.map(M=>({name:M.name,sub_type:M.sub_type,...M.club_sport_type_id&&{club_sport_type_id:M.club_sport_type_id}}))};U==="edit"&&(g.sport_id=ee.sport_id);const E=await de.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",{sports:[g]},"POST");U==="edit"?(await b(),X(t,"Sport updated successfully",3e3,"success")):(await b(),X(t,"Sport added successfully",3e3,"success")),re()}catch(g){console.error(g),X(t,g.message,3e3,"error")}finally{h(!1)}},D=async()=>{try{N(!0),await G()}catch(i){console.error(i),X(t,"Failed to save sports. Please try again.",3e3,"error")}finally{N(!1)}},J=k.some(i=>i.status===1),oe=async i=>{C(!0);try{de.setTable("club_sport_type");const g=await de.callRestAPI({id:i},"DELETE");console.log("response",g)}catch(g){console.error(g)}finally{C(!1)}};return e.jsxs("div",{className:"w-full max-w-xl",children:[(Q||c)&&e.jsx(ve,{}),e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_25940)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_25940)"}),e.jsxs("g",{filter:"url(#filter0_d_397_25940)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M48.25 27.9375C48.25 26.7294 47.2706 25.75 46.0625 25.75H27.9375C26.7294 25.75 25.75 26.7294 25.75 27.9375V46.0625C25.75 47.2706 26.7294 48.25 27.9375 48.25H46.0625C47.2706 48.25 48.25 47.2706 48.25 46.0625V27.9375ZM33.25 32C33.25 32.8629 32.5504 33.5625 31.6875 33.5625C30.8246 33.5625 30.125 32.8629 30.125 32C30.125 31.1371 30.8246 30.4375 31.6875 30.4375C32.5504 30.4375 33.25 31.1371 33.25 32ZM33.25 37C33.25 37.8629 32.5504 38.5625 31.6875 38.5625C30.8246 38.5625 30.125 37.8629 30.125 37C30.125 36.1371 30.8246 35.4375 31.6875 35.4375C32.5504 35.4375 33.25 36.1371 33.25 37ZM31.6875 43.5625C32.5504 43.5625 33.25 42.8629 33.25 42C33.25 41.1371 32.5504 40.4375 31.6875 40.4375C30.8246 40.4375 30.125 41.1371 30.125 42C30.125 42.8629 30.8246 43.5625 31.6875 43.5625Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_25940",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_25940"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_25940",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-2xl font-medium",children:"Select the sports offered at your club"}),e.jsxs("div",{className:"relative",children:[e.jsx(ps,{className:"h-6 w-6 cursor-pointer text-gray-400 hover:text-gray-600",onMouseEnter:()=>T(!0),onMouseLeave:()=>T(!1)}),p&&e.jsxs("div",{className:"absolute left-1/2 top-8 z-50 w-64 -translate-x-1/2 transform rounded-xl bg-white p-4 shadow-lg",children:[e.jsx("p",{className:"mb-2 font-medium",children:"An example of Sport, Type, and Sub-Type:"}),e.jsx("p",{children:"Sport: Tennis"}),e.jsx("p",{children:"Type: Indoor (optional)"}),e.jsx("p",{children:"Sub-type: Grass court (optional)"})]})]})]})]}),e.jsx("div",{className:"mx-auto mb-6 flex max-w-fit flex-col justify-center gap-6",children:k.map(i=>{var g;return e.jsxs("div",{className:"flex w-full flex-col gap-3",children:[e.jsxs("div",{className:"flex w-fit items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",className:"h-5 w-5 rounded-md border-gray-300 text-primaryGreen focus:ring-primaryGreen",value:i.name,checked:i.status==1,onChange:()=>Z(i)}),e.jsx("label",{className:"font-medium capitalize",children:i.name})]}),e.jsxs("button",{onClick:()=>K(i),className:"flex items-center gap-1 rounded-lg px-2 py-1 text-sm text-gray-600 hover:bg-gray-100",children:[e.jsx(ms,{className:"h-4 w-4"}),"Edit"]}),e.jsxs("button",{onClick:()=>W(i),className:"flex items-center gap-1 rounded-lg px-2 py-1 text-sm text-gray-600 hover:bg-gray-100",children:[e.jsx(xs,{className:"h-4 w-4"}),"Delete"]})]}),i.status===1&&e.jsx("div",{className:"ml-7 flex flex-col gap-2",children:(g=i.types)==null?void 0:g.map((E,M)=>{var P;return e.jsxs("div",{className:"flex flex-col gap-1",children:[E.name&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Type:"," ",e.jsx("span",{className:"capitalize text-gray-900",children:E.name})]}),((P=E.sub_type)==null?void 0:P.length)>0&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Sub-types:"," ",e.jsx("span",{className:"capitalize text-gray-900",children:E.sub_type.join(", ")})]})]},E.club_sport_type_id||M)})})]},i.sport_id||i.name)})}),e.jsx("button",{className:"mb-5 text-primaryBlue underline",onClick:te,children:"+Add sport"}),R&&e.jsx("div",{className:"fixed inset-0 z-50 flex min-h-screen items-center justify-center bg-black/30 p-4",children:e.jsxs("div",{className:"w-full max-w-lg rounded-3xl bg-white p-8",children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:U==="add"?"Add New Sport":"Edit Sport"}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-base",children:"Sport name"}),e.jsx("input",{type:"text",className:"w-full rounded-xl border border-gray-200 px-4 py-3 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:I.name,onChange:i=>z(g=>({...g,name:i.target.value})),placeholder:"Enter sport name"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("label",{className:"mb-2 block text-base",children:["Types ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:d,className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-primaryBlue/80",children:[e.jsx(Ae,{className:"h-4 w-4"}),"Add Type"]})]}),e.jsx("div",{className:"space-y-4",children:I.types.map((i,g)=>e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:i.name,onChange:E=>m(g,E.target.value),placeholder:"Enter type name"}),e.jsx("button",{onClick:()=>s(g,i==null?void 0:i.club_sport_type_id),className:"text-gray-400 hover:text-gray-600",children:e.jsx(Be,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Sub-types"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:()=>n(g),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-primaryBlue/80",children:[e.jsx(Ae,{className:"h-4 w-4"}),"Add Sub-type"]})]}),e.jsx("div",{className:"space-y-2",children:i.sub_type.map((E,M)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:E,onChange:P=>v(g,M,P.target.value),placeholder:"Enter sub-type name"}),e.jsx("button",{onClick:()=>H(g,M),className:"text-gray-400 hover:text-gray-600",children:e.jsx(Be,{className:"h-5 w-5"})})]},M))})]})]},i.club_sport_type_id||g))})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(se,{onClick:re,className:"w-full rounded-xl border border-gray-200 bg-white py-3 text-base font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(se,{onClick:Y,loading:w,className:"w-full rounded-xl bg-primaryGreen py-3 text-base font-medium text-white hover:bg-primaryGreen/90",children:U==="add"?"Add Sport":"Save Changes"})]})]})}),e.jsx(se,{className:`w-full rounded-xl py-3 text-white ${J?"bg-primaryGreen hover:bg-primaryGreen/90":"cursor-not-allowed bg-gray-400"}`,onClick:D,loading:u,disabled:!J,children:"Continue"})]})}const ws=["00:00:00","01:00:00","02:00:00","03:00:00","04:00:00","05:00:00","06:00:00","07:00:00","08:00:00","09:00:00","10:00:00","11:00:00","12:00:00","13:00:00","14:00:00","15:00:00","16:00:00","17:00:00","18:00:00","19:00:00","20:00:00","21:00:00","22:00:00","23:00:00"],vs=G=>{const[x,_]=G.split(":"),j=parseInt(x,10),r=j>=12?"PM":"AM";return`${j%12||12}:${_} ${r}`},we=({label:G,value:x,onChange:_,timeOptions:j=ws,minTime:r=null})=>{const l=r?j.filter(f=>{const[b]=r.split(":"),[u]=f.split(":");return parseInt(u)>parseInt(b)}):j;return e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"gap-2 self-stretch whitespace-nowrap bg-slate-50 px-2 py-2.5 text-neutral-400",children:G}),e.jsxs("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:x,onChange:_,children:[e.jsx("option",{value:"",children:"Select time"}),l.map(f=>e.jsx("option",{value:f,children:vs(f)},f))]})]})};function Ns({onNext:G,register:x,errors:_,setValue:j,defaultValues:r,isSubmitting:l}){const[f,b]=a.useState(()=>r!=null&&r.days_off?Array.isArray(r.days_off)?r.days_off:[]:[]),[u,N]=a.useState(()=>(r==null?void 0:r.times.length)>0?r.times:[{from:"",until:""}]),[p,T]=a.useState(()=>{var w;return((w=r==null?void 0:r.max_players)==null?void 0:w.toString())||""});console.log("time slots",u),a.useEffect(()=>{r&&(r.days_off&&b(Array.isArray(r.days_off)?r.days_off:[]),r.opening_time&&r.closing_time&&N([{from:r.opening_time,until:r.closing_time}]),r.max_players&&T(r.max_players.toString()))},[r]);const[R,L]=a.useState(!1),U=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],$=(w,h)=>{b(h?c=>c.filter(I=>I!==w):c=>[...c,w])},ee=()=>{j("days_off",f),j("times",u),j("max_players",Number(p)),G()},q=()=>{N(w=>[...w,{from:"",until:""}])},Q=w=>{N(h=>h.filter((c,C)=>C!==w))},A=(w,h,c)=>{N(C=>{const I=[...C];return I[w]={...I[w],[h]:c},I})};return e.jsx("div",{className:"flex min-h-screen items-center justify-center",children:e.jsxs("div",{className:"w-full max-w-xl rounded-2xl bg-white p-8 shadow-lg",children:[e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_25940)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_25940)"}),e.jsxs("g",{filter:"url(#filter0_d_397_25940)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M48.25 27.9375C48.25 26.7294 47.2706 25.75 46.0625 25.75H27.9375C26.7294 25.75 25.75 26.7294 25.75 27.9375V46.0625C25.75 47.2706 26.7294 48.25 27.9375 48.25H46.0625C47.2706 48.25 48.25 47.2706 48.25 46.0625V27.9375ZM33.25 32C33.25 32.8629 32.5504 33.5625 31.6875 33.5625C30.8246 33.5625 30.125 32.8629 30.125 32C30.125 31.1371 30.8246 30.4375 31.6875 30.4375C32.5504 30.4375 33.25 31.1371 33.25 32ZM33.25 37C33.25 37.8629 32.5504 38.5625 31.6875 38.5625C30.8246 38.5625 30.125 37.8629 30.125 37C30.125 36.1371 30.8246 35.4375 31.6875 35.4375C32.5504 35.4375 33.25 36.1371 33.25 37ZM31.6875 43.5625C32.5504 43.5625 33.25 42.8629 33.25 42C33.25 41.1371 32.5504 40.4375 31.6875 40.4375C30.8246 40.4375 30.125 41.1371 30.125 42C30.125 42.8629 30.8246 43.5625 31.6875 43.5625Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_25940",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_25940"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_25940",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("p",{className:"text-2xl font-medium",children:"Other details"})]}),e.jsx("p",{className:"mb-2",children:"Opening hours for:"}),e.jsx("div",{className:"mb-6 flex flex-wrap justify-start gap-4",children:U.map(w=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",className:"h-5 w-5 rounded-md border-gray-300 text-primaryGreen focus:ring-primaryGreen",checked:!f.includes(w),onChange:h=>$(w,h.target.checked)}),e.jsx("label",{className:"",children:w})]},w))}),u.map((w,h)=>e.jsxs("div",{className:"mb-5 flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsx(we,{label:"From",timeOptions:De,value:w.from,onChange:c=>A(h,"from",c.target.value)}),e.jsx(we,{label:"Until",timeOptions:Je(w.from),value:w.until,onChange:c=>A(h,"until",c.target.value),disabled:!w.from})]}),u.length>1&&e.jsx("button",{onClick:()=>Q(h),className:" text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]},h)),e.jsx("button",{onClick:q,className:"mb-5 text-primaryBlue underline hover:text-primaryBlue/80",children:"+Add another time slot"}),e.jsxs("div",{className:"mb-5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("p",{children:"Max number of players for a normal lesson:"}),e.jsxs("div",{className:"relative",onMouseEnter:()=>L(!0),onMouseLeave:()=>L(!1),children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25ZM11.1158 13.2086L11.2156 12.8006C11.164 12.8249 11.0807 12.8526 10.9665 12.8841C10.852 12.9157 10.7489 12.9318 10.6583 12.9318C10.4654 12.9318 10.3295 12.9001 10.2507 12.8366C10.1724 12.773 10.1333 12.6534 10.1333 12.4783C10.1333 12.4089 10.1451 12.3054 10.1697 12.17C10.1936 12.0337 10.2211 11.9126 10.2516 11.8067L10.6242 10.4876C10.6607 10.3665 10.6857 10.2334 10.6992 10.0882C10.7129 9.94325 10.7193 9.84185 10.7193 9.78429C10.7193 9.50614 10.6218 9.28041 10.4268 9.10629C10.2317 8.93229 9.95393 8.84529 9.59396 8.84529C9.39365 8.84529 9.18188 8.88088 8.95776 8.952C8.73363 9.02294 8.49933 9.1084 8.25421 9.2082L8.15415 9.6165C8.22719 9.58949 8.31419 9.56043 8.41598 9.53034C8.51732 9.50038 8.61674 9.48489 8.71347 9.48489C8.91096 9.48489 9.04399 9.51856 9.1137 9.58488C9.18342 9.65139 9.21844 9.7697 9.21844 9.93883C9.21844 10.0324 9.20736 10.1363 9.18438 10.2492C9.16172 10.3628 9.13342 10.483 9.10013 10.6098L8.72595 11.9342C8.69266 12.0734 8.66834 12.1979 8.65304 12.3084C8.63786 12.4189 8.63057 12.5272 8.63057 12.6326C8.63057 12.9048 8.73114 13.1292 8.93222 13.3063C9.13329 13.4826 9.41523 13.5714 9.77769 13.5714C10.0137 13.5714 10.2209 13.5406 10.3992 13.4785C10.5773 13.4167 10.8164 13.3268 11.1158 13.2086ZM11.0495 7.8502C11.2235 7.68882 11.3101 7.49254 11.3101 7.26272C11.3101 7.03341 11.2236 6.83675 11.0495 6.67331C10.8758 6.51032 10.6666 6.42857 10.4219 6.42857C10.1765 6.42857 9.96635 6.51013 9.79107 6.67331C9.61579 6.83675 9.52796 7.03334 9.52796 7.26272C9.52796 7.49254 9.61579 7.68875 9.79107 7.8502C9.96667 8.01217 10.1764 8.09321 10.4219 8.09321C10.6666 8.09321 10.8758 8.01217 11.0495 7.8502Z",fill:"#CDD0D5"})}),R&&e.jsx("div",{className:"absolute z-10 -translate-x-1/2 transform",children:e.jsx("div",{className:"mt-2 w-[400px] rounded-lg bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsx("p",{className:"text-sm text-gray-600",children:"This is the maximum number of players that can be booked with the “find by coach” and “find by time” options in the lesson section. If the booking party exceeds this number, they cannot outright book the lesson, and will need to submit a request through the “custom request” section. This ensures that coaches can effectively manage group sizes, while also allowing flexibility for larger parties through the “custom request” process."})})})]})]}),e.jsx("input",{type:"text",className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2",value:p,onChange:w=>T(w.target.value)})]}),e.jsx(se,{className:"w-full rounded-xl bg-primaryGreen py-3 text-white",onClick:ee,loading:l,children:"Continue"})]})})}let Fe=new he;const Cs=G=>{const x={};return G&&G.forEach(_=>{_.sport_id&&(x[_.sport_id]||(x[_.sport_id]={total:0,types:{}}),x[_.sport_id].total+=1,_.type&&(x[_.sport_id].types[_.type]||(x[_.sport_id].types[_.type]=0),x[_.sport_id].types[_.type]+=1))}),x};function _s({onNext:G,register:x,errors:_,values:j,setValue:r,watch:l,defaultValues:f,isSubmitting:b}){var re;console.log("default values",f);const[u,N]=a.useState(!1),p=a.useMemo(()=>{var d;return(d=f==null?void 0:f.sports)==null?void 0:d.map(s=>({...s,sport_types:s.sport_types||[]}))},[f==null?void 0:f.sports]),[T,R]=a.useState(()=>{var d;return((d=j==null?void 0:j.courts)==null?void 0:d.length)>0?j.courts.map(s=>({...s})):[]}),[L,U]=a.useState(null),$=a.useRef(),[ee,q]=a.useState(!((re=j==null?void 0:j.courts)!=null&&re.length)),[Q,A]=a.useState(""),[w,h]=a.useState({}),c=l("courts"),[C,I]=a.useState({}),z=()=>{const d=parseInt(Q);if(d>0){const s=Array.from({length:d},(m,n)=>({sport_id:null,name:`Court ${n+1}`,type:null,sub_type:null}));R(s),r("courts",s),q(!1)}};a.useEffect(()=>{var d;if(((d=j==null?void 0:j.courts)==null?void 0:d.length)>0&&!T.length){const s=j.courts.map(m=>({...m}));R(s),r("courts",s),q(!1)}},[j]),a.useEffect(()=>{const d=s=>{$.current&&!$.current.contains(s.target)&&U(null)};return document.addEventListener("mousedown",d),()=>document.removeEventListener("mousedown",d)},[]);const t=()=>{const d={sport_id:null,name:`Court ${((c==null?void 0:c.length)||0)+1}`,type:null,sub_type:null};r("courts",[...c||[],d])},k=async(d,s)=>{N(!0);try{if(s!=null&&s.id){Fe.setTable("club_court");const m=await Fe.callRestAPI({id:s==null?void 0:s.id},"DELETE");r("courts",c.filter((n,H)=>H!==d))}else r("courts",c.filter((m,n)=>n!==d))}catch(m){console.log(m)}finally{N(!1)}},S=d=>{U(L===d?null:d)},Z=(d,s)=>{var v,Y;const m=parseInt(s),n=(v=p==null?void 0:p.filter(D=>D.status!==0))==null?void 0:v.find(D=>D.id===m),H=(Y=n==null?void 0:n.sport_types)==null?void 0:Y.some(D=>D.type);r("courts",c.map((D,J)=>J===d?{...D,sport_id:m,type:null,sub_type:null,hasTypes:H}:D))},W=(d,s)=>{r("courts",c.map((m,n)=>n===d?{...m,type:s,sub_type:null}:m))},K=(d,s)=>{r("courts",c.map((m,n)=>n===d?{...m,sub_type:s}:m))};a.useEffect(()=>{T.length>0&&!c&&r("courts",T)},[T,r,c]),a.useEffect(()=>{if((c==null?void 0:c.length)>=0){const d=Cs(c);h(d)}},[c]);const te=d=>{d.preventDefault();const s={};let m=!1;c==null||c.forEach((n,H)=>{var v,Y,D;if(n.sport_id||(s[`courts.${H}.sport_id`]="Sport is required",m=!0),n.sport_id){const J=p==null?void 0:p.find(i=>i.id===n.sport_id);if(((v=J==null?void 0:J.sport_types)==null?void 0:v.some(i=>i.type))&&!n.type&&(s[`courts.${H}.type`]="Type is required for this sport",m=!0),n.type){const i=(Y=J==null?void 0:J.sport_types)==null?void 0:Y.find(g=>g.type===n.type);((D=i==null?void 0:i.subtype)==null?void 0:D.length)>0&&!n.sub_type&&(s[`courts.${H}.sub_type`]="Sub-type is required for this type",m=!0)}}}),I(s),m||G()};return e.jsxs("div",{className:"w-full max-w-xl",children:[u&&e.jsx(ve,{}),ee&&e.jsx("div",{className:"fixed inset-0 z-50 flex min-h-screen items-center justify-center bg-black/30 p-4",children:e.jsxs("div",{className:"w-full max-w-lg rounded-3xl bg-white p-8",children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Number of courts/spaces"}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("label",{className:"mb-4  flex items-center  text-base",children:[e.jsx("span",{children:"How many courts/spaces does your club have"}),e.jsx("span",{className:"inline-block",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25ZM11.1158 13.2086L11.2156 12.8006C11.164 12.8249 11.0807 12.8526 10.9665 12.8841C10.852 12.9157 10.7489 12.9318 10.6583 12.9318C10.4654 12.9318 10.3295 12.9001 10.2507 12.8366C10.1724 12.773 10.1333 12.6534 10.1333 12.4783C10.1333 12.4089 10.1451 12.3054 10.1697 12.17C10.1936 12.0337 10.2211 11.9126 10.2516 11.8067L10.6242 10.4876C10.6607 10.3665 10.6857 10.2334 10.6992 10.0882C10.7129 9.94325 10.7193 9.84185 10.7193 9.78429C10.7193 9.50614 10.6218 9.28041 10.4268 9.10629C10.2317 8.93229 9.95393 8.84529 9.59396 8.84529C9.39365 8.84529 9.18188 8.88088 8.95776 8.952C8.73363 9.02294 8.49933 9.1084 8.25421 9.2082L8.15415 9.6165C8.22719 9.58949 8.31419 9.56043 8.41598 9.53034C8.51732 9.50038 8.61674 9.48489 8.71347 9.48489C8.91096 9.48489 9.04399 9.51856 9.1137 9.58488C9.18342 9.65139 9.21844 9.7697 9.21844 9.93883C9.21844 10.0324 9.20736 10.1363 9.18438 10.2492C9.16172 10.3628 9.13342 10.483 9.10013 10.6098L8.72595 11.9342C8.69266 12.0734 8.66834 12.1979 8.65304 12.3084C8.63786 12.4189 8.63057 12.5272 8.63057 12.6326C8.63057 12.9048 8.73114 13.1292 8.93222 13.3063C9.13329 13.4826 9.41523 13.5714 9.77769 13.5714C10.0137 13.5714 10.2209 13.5406 10.3992 13.4785C10.5773 13.4167 10.8164 13.3268 11.1158 13.2086ZM11.0495 7.8502C11.2235 7.68882 11.3101 7.49254 11.3101 7.26272C11.3101 7.03341 11.2236 6.83675 11.0495 6.67331C10.8758 6.51032 10.6666 6.42857 10.4219 6.42857C10.1765 6.42857 9.96635 6.51013 9.79107 6.67331C9.61579 6.83675 9.52796 7.03334 9.52796 7.26272C9.52796 7.49254 9.61579 7.68875 9.79107 7.8502C9.96667 8.01217 10.1764 8.09321 10.4219 8.09321C10.6666 8.09321 10.8758 8.01217 11.0495 7.8502Z",fill:"#CDD0D5"})})})]}),e.jsx("input",{type:"number",className:"mb-4 w-full rounded-xl border border-gray-200 px-4 py-3 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:Q,onChange:d=>A(d.target.value),placeholder:"Enter number of courts"})]}),e.jsx(se,{onClick:z,className:"w-full rounded-xl bg-primaryGreen py-3 text-base font-medium text-white hover:bg-primaryGreen/90",children:"Continue"})]})}),c==null?void 0:c.map((d,s)=>{var m,n,H,v,Y,D,J,oe,i,g,E,M,P,le,ne,pe,ue,ge;return e.jsxs("div",{className:"mb-4 rounded-xl bg-[#F6F8FA] p-5",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsxs("span",{className:"text-base font-medium",children:["Space ",s+1]}),e.jsxs("div",{className:"relative",ref:$,children:[e.jsx("button",{className:"text-sm text-gray-500",onClick:()=>S(s),children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.0013 10.8337C10.4615 10.8337 10.8346 10.4606 10.8346 10.0003C10.8346 9.54009 10.4615 9.16699 10.0013 9.16699C9.54107 9.16699 9.16797 9.54009 9.16797 10.0003C9.16797 10.4606 9.54107 10.8337 10.0013 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M16.8763 10.8337C17.3365 10.8337 17.7096 10.4606 17.7096 10.0003C17.7096 9.54009 17.3365 9.16699 16.8763 9.16699C16.4161 9.16699 16.043 9.54009 16.043 10.0003C16.043 10.4606 16.4161 10.8337 16.8763 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M3.1263 10.8337C3.58654 10.8337 3.95964 10.4606 3.95964 10.0003C3.95964 9.54009 3.58654 9.16699 3.1263 9.16699C2.66606 9.16699 2.29297 9.54009 2.29297 10.0003C2.29297 10.4606 2.66606 10.8337 3.1263 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M10.0013 10.8337C10.4615 10.8337 10.8346 10.4606 10.8346 10.0003C10.8346 9.54009 10.4615 9.16699 10.0013 9.16699C9.54107 9.16699 9.16797 9.54009 9.16797 10.0003C9.16797 10.4606 9.54107 10.8337 10.0013 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M16.8763 10.8337C17.3365 10.8337 17.7096 10.4606 17.7096 10.0003C17.7096 9.54009 17.3365 9.16699 16.8763 9.16699C16.4161 9.16699 16.043 9.54009 16.043 10.0003C16.043 10.4606 16.4161 10.8337 16.8763 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M3.1263 10.8337C3.58654 10.8337 3.95964 10.4606 3.95964 10.0003C3.95964 9.54009 3.58654 9.16699 3.1263 9.16699C2.66606 9.16699 2.29297 9.54009 2.29297 10.0003C2.29297 10.4606 2.66606 10.8337 3.1263 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),L===s&&e.jsx("div",{className:"absolute right-0 z-10 mt-2 w-48 rounded-lg bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("button",{className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-50",onClick:()=>{k(s,d),U(null)},children:[e.jsxs("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M3 6H5H21",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Delete court"]})})]})]}),e.jsxs("div",{className:"space-y-4 rounded-xl bg-white p-4 ",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"mb-2 block",children:"Court name"}),e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 px-3 py-2",...x(`courts.${s}.name`),defaultValue:d.name})]})}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block",children:["Sport ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(m=p==null?void 0:p.filter(B=>B.status!==0))==null?void 0:m.map(B=>e.jsxs("label",{className:"flex items-center gap-1 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.sport_id`,value:B.id,checked:d.sport_id===B.id,className:"h-5 w-5",onChange:ce=>Z(s,ce.target.value),required:!0}),B.name," "]},B.id))}),C[`courts.${s}.sport_id`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:C[`courts.${s}.sport_id`]})]}),d.sport_id&&((v=(H=(n=p==null?void 0:p.filter(B=>B.status!==0))==null?void 0:n.find(B=>B.id===d.sport_id))==null?void 0:H.sport_types)==null?void 0:v.length)>0&&e.jsxs("div",{children:[((J=(D=(Y=p==null?void 0:p.find(B=>B.id===d.sport_id))==null?void 0:Y.sport_types)==null?void 0:D.filter(B=>B.type))==null?void 0:J.length)>0&&e.jsxs("label",{className:"mb-2 block",children:["Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(g=(i=(oe=p==null?void 0:p.find(B=>B.id===d.sport_id))==null?void 0:oe.sport_types)==null?void 0:i.filter(B=>B.type))==null?void 0:g.map((B,ce)=>e.jsxs("label",{className:"flex items-center gap-2 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.type`,value:B.type,checked:d.type===B.type,className:"h-5 w-5",onChange:Ce=>W(s,Ce.target.value),required:!0}),B.type]},`${s}-${ce}`))}),C[`courts.${s}.type`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:C[`courts.${s}.type`]})]}),d.sport_id&&d.type&&e.jsx("div",{children:((le=(P=(M=(E=p==null?void 0:p.find(B=>B.id===d.sport_id))==null?void 0:E.sport_types)==null?void 0:M.find(B=>B.type===d.type))==null?void 0:P.subtype)==null?void 0:le.length)>0&&e.jsxs(e.Fragment,{children:[e.jsxs("label",{className:"mb-2 block",children:["Sub-type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(ge=(ue=(pe=(ne=p==null?void 0:p.find(B=>B.id==d.sport_id))==null?void 0:ne.sport_types)==null?void 0:pe.find(B=>B.type===d.type))==null?void 0:ue.subtype)==null?void 0:ge.map(B=>e.jsxs("label",{className:"flex items-center gap-2 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.sub_type`,value:B,checked:d.sub_type===B,className:"h-5 w-5",onChange:ce=>K(s,ce.target.value),required:!0}),B]},`${s}-${B}`))}),C[`courts.${s}.sub_type`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:C[`courts.${s}.sub_type`]})]})})]})]},s)}),e.jsx("button",{className:"mb-6 text-blue-600 underline",onClick:t,type:"button",children:"+ Add another court/space"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("p",{className:"mb-4 text-lg font-medium",children:"Summary:"}),e.jsxs("div",{className:"mb-4 space-y-2 rounded-xl bg-gray-50 p-4",children:[Object.entries(w).map(([d,s])=>{const m=p==null?void 0:p.find(v=>v.id===parseInt(d)),n=c==null?void 0:c.filter(v=>v.sport_id===parseInt(d)),H=[...new Set(n==null?void 0:n.map(v=>v.sub_type).filter(Boolean))];return e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{children:[(m==null?void 0:m.name)||"Unknown Sport"," ",e.jsx("span",{className:"text-sm capitalize text-gray-500",children:H.length>0?`(${H.join(", ")})`:""})]}),e.jsxs("div",{className:"flex gap-4",children:[Object.entries(s.types).map(([v,Y])=>e.jsxs("span",{className:"flex items-center gap-2 capitalize",children:[v," ",e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:Y})]},v)),Object.keys(s.types).length===0&&e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:s.total})]})]},d)}),e.jsxs("div",{className:"mt-4 flex justify-between border-t border-gray-200 pt-4",children:[e.jsx("span",{children:"Courts/spaces total"}),e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:(c==null?void 0:c.length)||0})]})]})]}),e.jsx(se,{className:"w-full rounded-xl bg-[#176448] py-3 text-white disabled:opacity-50",onClick:te,loading:b,type:"button",children:"Continue"})]})}const He=new he;function Ss({onNext:G,stripeConnectionData:x,isSubmitting:_,setStripeConnectionData:j}){const[r]=je.useState(_||!1),[l,f]=a.useState(!1),[b,u]=a.useState(!1),N=localStorage.getItem("role"),p=async()=>{try{const L=await He.callRawAPI(`/v3/api/custom/courtmatchup/${N}/stripe/account/verify`,{},"POST");return j&&j(L),L}catch(L){return console.error("Error checking Stripe connection:",L),!1}},T=async()=>{f(!0);try{const L=await He.callRawAPI(`/v3/api/custom/courtmatchup/${N}/stripe/onboarding`,{},"POST");L&&L.url&&window.open(L.url,"_blank")}catch(L){console.error("Error connecting to Stripe:",L)}f(!1)};a.useEffect(()=>{if(l===!1){const L=setTimeout(()=>{p()},2e3);return()=>clearTimeout(L)}},[l]);const R=async()=>{u(!0),await G(),u(!1)};return e.jsx("div",{className:"flex flex-col bg-white pb-7",children:e.jsxs("section",{className:"flex w-[432px] max-w-full flex-col justify-center",children:[e.jsx("div",{className:"flex w-full max-w-[432px] flex-col self-center max-md:max-w-full",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_26852)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_26852)"}),e.jsxs("g",{filter:"url(#filter0_d_397_26852)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M47.3125 34.4999H47V42.7054C47.6877 42.8975 48.2531 43.4195 48.4868 44.1207L48.9035 45.3707C49.3756 46.7872 48.3213 48.2499 46.8282 48.2499H27.1718C25.6787 48.2499 24.6244 46.7872 25.0965 45.3707L25.5132 44.1207C25.7469 43.4195 26.3123 42.8975 27 42.7054V34.4999H26.6875C25.4794 34.4999 24.5 33.5206 24.5 32.3124V31.7352C24.5 30.9099 24.9645 30.1549 25.7011 29.7828L36.0136 24.5729C36.6339 24.2596 37.3661 24.2596 37.9864 24.5729L48.2989 29.7828C49.0355 30.1549 49.5 30.9099 49.5 31.7352V32.3124C49.5 33.5206 48.5206 34.4999 47.3125 34.4999ZM42 34.4999H45.125V42.6249H42V34.4999ZM32 42.6249H28.875V34.4999H32V42.6249ZM33.875 42.6249V34.4999H40.125V42.6249H33.875Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_26852",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_26852"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_26852",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"w-full text-center text-2xl font-medium leading-none text-gray-950 max-md:max-w-full",children:"Connect stripe"})]})}),e.jsxs("div",{className:"mt-10 flex w-full flex-col gap-4 self-center max-md:max-w-full",children:[((x==null?void 0:x.complete)||(x==null?void 0:x.details_submitted))&&e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-lg font-medium text-gray-900",children:x!=null&&x.complete?"Stripe account connected":"Stripe account details submitted"})]}),e.jsxs("div",{className:"mt-2 grid grid-cols-1 gap-3",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-500",children:"Account ID"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:x.account_id})]}),e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${x.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${x.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${x.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("div",{className:"mt-2 text-sm text-gray-600",children:x!=null&&x.complete?"You can now receive payments from your club members.":"Your Stripe account details have been submitted and are pending approval. You can continue with the setup process."}),e.jsx(se,{onClick:R,className:"mt-4 w-full rounded-xl bg-primaryGreen px-4 py-3 text-sm font-medium text-white",loading:r||b,children:r?"Processing...":"Continue"})]}),!(x!=null&&x.complete||x!=null&&x.details_submitted)&&e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"No Stripe account connected"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),x&&e.jsxs("div",{className:"mt-3 grid grid-cols-1 gap-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${x.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${x.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${x.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("button",{onClick:T,className:"w-full rounded-xl bg-primaryBlue px-4 py-3 text-sm font-medium text-white",disabled:l,children:l?"Connecting...":"Connect Stripe Account"})]})]})]})})}const Oe=({label:G,value:x,onChange:_})=>e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"flex w-10 items-center justify-center gap-2 self-stretch whitespace-nowrap bg-slate-50 px-2 py-2.5 text-neutral-400",children:"$"}),e.jsx("input",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:x,type:"number",onChange:_})]});function ks({handleSave:G,setValue:x,setPricingRows:_,pricingRows:j,editingPricing:r=null,values:l,isSubmitting:f,sportsOffered:b}){var c,C,I,z;const[u,N]=a.useState(()=>{var t;return r?{sport_id:r.sport_id,type:r.type,sub_type:r.sub_type||"",is_general:r.is_general||!1,general_rate:r.general_rate||"",price_by_hours:(t=r==null?void 0:r.price_by_hours)==null?void 0:t.map(k=>({start_time:k.start_time,end_time:k.end_time,rate:k.rate,showInput:!1})),showSportInput:!1,showTypeInput:!1}:{sport_id:"",type:"",sub_type:"",is_general:!1,general_rate:"",price_by_hours:[{start_time:"",end_time:"",rate:"",showInput:!0}],showSportInput:!0,showTypeInput:!0}}),{dispatch:p}=a.useContext(Ne),T=b==null?void 0:b.find(t=>t.id==u.sport_id),R=((c=T==null?void 0:T.sport_types)==null?void 0:c.filter(t=>t.type))||[],L=((I=(C=T==null?void 0:T.sport_types)==null?void 0:C.find(t=>t.type===u.type))==null?void 0:I.subtype)||[],U=()=>{const t=u;if(t.is_general){if(!t.general_rate){X(p,"Please enter a general rate",3e3,"warning");return}}else{if(t.price_by_hours.length===0){X(p,"Please add pricing details",3e3,"warning");return}if(t.price_by_hours.some(Z=>!Z.start_time||!Z.end_time||!Z.rate)){X(p,"Please fill all time slot details",3e3,"warning");return}}const k={sport_id:t.sport_id,type:t.type,sub_type:t.sub_type,is_general:t.is_general,general_rate:t.is_general?t.general_rate:null,price_by_hours:t.is_general?[{rate:t.general_rate}]:t.price_by_hours.map(S=>({start_time:S.start_time,end_time:S.end_time,rate:S.rate}))};_(r?S=>S.map((Z,W)=>W===r.index?k:Z):S=>[...S,k]),N({sport_id:"",type:"",sub_type:"",is_general:!1,general_rate:"",price_by_hours:[{start_time:"",end_time:"",rate:"",showInput:!0}],showSportInput:!0,showTypeInput:!0})},$=(t,k)=>{var Z;const S=((Z=k==null?void 0:k.target)==null?void 0:Z.value)||k;N(W=>({...W,[t]:S}))},ee=()=>{N(t=>({...t,showSportInput:!1}))},q=()=>{N(t=>({...t,showSportInput:!0}))},Q=(t,k,S)=>{var W;const Z=((W=S==null?void 0:S.target)==null?void 0:W.value)||S;N(K=>({...K,price_by_hours:K.price_by_hours.map((te,re)=>re===t?{...te,[k]:Z}:te)}))},A=()=>{N(t=>({...t,price_by_hours:[...t.price_by_hours,{start_time:"",end_time:"",rate:"",showInput:!0}]}))};console.log({sportsOffered:b});const w=t=>{const k=u.price_by_hours[t];if(!k.start_time||!k.end_time||!k.rate){X(p,"Please fill all hours and rate fields",3e3,"warning");return}N(S=>({...S,price_by_hours:S.price_by_hours.map((Z,W)=>W===t?{...Z,showInput:!1}:Z)}))},h=t=>{N(k=>({...k,price_by_hours:k.price_by_hours.map((S,Z)=>Z===t?{...S,showInput:!0}:S)}))};return e.jsx("div",{className:"space-y-5",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"mb-6 divide-y",children:[e.jsx("div",{className:"py-4",children:u.showSportInput?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("label",{className:"text-base font-medium text-gray-900",children:["Sport"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("button",{onClick:()=>N(t=>({...t,showSportInput:!1})),className:"text-sm text-primaryBlue hover:text-blue-700",children:"Cancel"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:u.sport_id,onChange:t=>{N(k=>({...k,sport_id:t.target.value,type:"",sub_type:""}))},children:[e.jsx("option",{value:"",children:"-All sports-"}),(z=b==null?void 0:b.filter(t=>t.status===1))==null?void 0:z.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),u.sport_id&&R.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:u.type,onChange:t=>{$("type",t.target.value),N(k=>({...k,type:t.target.value,sub_type:""}))},children:[e.jsx("option",{value:"",children:"-Select Type-"}),R.map(t=>e.jsx("option",{value:t.type,children:t.type},t.type))]})]}),u.sport_id&&u.type&&L.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Sub-type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:u.sub_type,onChange:t=>$("sub_type",t.target.value),children:[e.jsx("option",{value:"",children:"-Select Sub-type-"}),L.map(t=>e.jsx("option",{value:t,children:t},t))]})]}),e.jsx("button",{onClick:ee,className:"mt-4 w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white hover:bg-blue-700",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium text-gray-900",children:["Sport"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize text-gray-500",children:(T==null?void 0:T.name)||"All sports"})]}),e.jsx("button",{onClick:q,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})}),R.length>0&&e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium capitalize text-gray-900",children:["Type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize text-gray-500",children:u.type||"Not set"})]}),e.jsx("button",{onClick:q,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})}),L.length>0&&e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium text-gray-900",children:["Sub type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize capitalize text-gray-500",children:u.sub_type||"Not set"})]}),e.jsx("button",{onClick:q,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})})]}),e.jsxs("div",{className:"mt-6 border-t pt-4",children:[e.jsxs("div",{className:"mb-4 flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",id:"is_general",name:"is_general",checked:u.is_general,onChange:t=>{N({...u,is_general:t.target.checked})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("label",{htmlFor:"is_general",className:"text-sm font-medium",children:"General price"}),e.jsxs("div",{className:"group relative",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4 text-gray-500",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."})]})]})]}),u.is_general?e.jsx("div",{className:"mb-6 space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsx("div",{className:"flex w-full max-w-[300px]",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"General rate per hour"}),e.jsx("div",{className:"relative",children:e.jsx(Oe,{value:u.general_rate,onChange:t=>N({...u,general_rate:t.target.value})})})]})})}):e.jsxs("div",{className:"space-y-2",children:[u.price_by_hours.map((t,k)=>e.jsx("div",{className:"space-y-4",children:t.showInput?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Hours"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(we,{label:"Start time",value:t.start_time,onChange:S=>Q(k,"start_time",S.target.value)})}),e.jsx("div",{className:"flex-1",children:e.jsx(we,{label:"End time",value:t.end_time,onChange:S=>Q(k,"end_time",S.target.value),minTime:t.start_time})})]})]}),e.jsx("div",{className:"flex w-full max-w-[300px]",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Rate per hour"}),e.jsx("div",{className:"relative",children:e.jsx(Oe,{value:t.rate,onChange:S=>Q(k,"rate",S.target.value)})})]})}),e.jsx("div",{className:"flex gap-4",children:e.jsx("button",{onClick:()=>w(k),className:"rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white",children:"Save"})})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Hours"}),e.jsxs("div",{className:"text-sm text-gray-900",children:[be(t.start_time)," -"," ",be(t.end_time)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Rate"}),e.jsxs("div",{className:"text-sm text-gray-900",children:["$",t.rate,"/hour"]})]})]}),e.jsx("button",{onClick:()=>h(k),className:"text-sm text-primaryBlue underline",children:"Edit"})]})})},k)),e.jsx("button",{onClick:A,className:"text-primaryBlue underline underline-offset-2",children:"+ Add pricing"})]})]}),e.jsxs("div",{className:"mb-3 flex gap-4 border-b pb-4",children:[e.jsx("div",{className:"rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500",children:"Cancel"}),e.jsx(se,{className:"rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white",onClick:U,loading:f,children:r?"Update":"Save"})]})]})})}function Es({onNext:G,setValue:x,defaultValues:_,onSubmit:j,sportsOffered:r}){const[l,f]=a.useState([]),[b,u]=a.useState(null),[N,p]=a.useState(!1),[T,R]=a.useState(null),[L,U]=je.useState(!1);je.useEffect(()=>{const h=c=>{b!==null&&!c.target.closest(".dropdown-container")&&u(null)};return document.addEventListener("mousedown",h),()=>{document.removeEventListener("mousedown",h)}},[b]);const $=(h,c)=>{c.stopPropagation(),u(b===h?null:h)},ee=()=>{p(!0)},q=h=>{p(!1)},Q=async()=>{x("pricing",l),U(!0),await j(),U(!1)},A=h=>{f(l.filter((c,C)=>C!==h)),u(null)},w=(h,c)=>{R({...h,index:c}),p(!0),u(null)};return console.log("pricing rows",l),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"w-full max-w-xl p-4",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_27001)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_27001)"}),e.jsxs("g",{filter:"url(#filter0_d_397_27001)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.5 37C24.5 30.0964 30.0964 24.5 37 24.5C43.9036 24.5 49.5 30.0964 49.5 37C49.5 43.9036 43.9036 49.5 37 49.5C30.0964 49.5 24.5 43.9036 24.5 37ZM37 28.9965C37.5178 28.9965 37.9375 29.4163 37.9375 29.934V31.0132C39.0083 31.2211 39.9523 31.7812 40.5418 32.5965C40.8452 33.0161 40.751 33.6021 40.3315 33.9055C39.9119 34.2089 39.3258 34.1147 39.0224 33.6952C38.6686 33.2059 37.9339 32.7986 37 32.7986H36.6431C35.3781 32.7986 34.7257 33.5858 34.7257 34.1451V34.2431C34.7257 34.6719 35.0377 35.2054 35.7823 35.5032L38.9141 36.7559C40.1869 37.2651 41.1493 38.3812 41.1493 39.7569C41.1493 41.5317 39.6337 42.7526 37.9375 43.0208V44.066C37.9375 44.5837 37.5178 45.0035 37 45.0035C36.4822 45.0035 36.0625 44.5837 36.0625 44.066V42.9868C34.9917 42.7789 34.0477 42.2188 33.4582 41.4035C33.1548 40.9839 33.249 40.3979 33.6685 40.0945C34.0881 39.7911 34.6742 39.8853 34.9776 40.3048C35.3314 40.7941 36.0661 41.2014 37 41.2014H37.2343C38.567 41.2014 39.2743 40.3703 39.2743 39.7569C39.2743 39.3281 38.9623 38.7946 38.2177 38.4968L35.0859 37.2441C33.8131 36.7349 32.8507 35.6188 32.8507 34.2431V34.1451C32.8507 32.3833 34.3834 31.1896 36.0625 30.9629V29.934C36.0625 29.4163 36.4822 28.9965 37 28.9965Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_27001",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_27001"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_27001",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_27001",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_27001",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("p",{className:"mb-6 text-2xl font-semibold",children:"Pricing"})]}),e.jsxs("div",{className:"space-y-4",children:[l==null?void 0:l.map((h,c)=>{var C;return e.jsxs("div",{className:"relative rounded-xl bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[h.sport_id?e.jsx("span",{className:"capitalize",children:((C=r==null?void 0:r.find(I=>I.id==h.sport_id&&I.status===1))==null?void 0:C.name)||""}):e.jsx("span",{className:"text-gray-500",children:"All sports"}),h.type&&h.type!==""&&e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"•"}),e.jsx("span",{className:"capitalize",children:h.type})]}),h.sub_type&&h.sub_type!==""&&e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"•"}),e.jsx("span",{className:"capitalize",children:h.sub_type})]})]}),e.jsxs("div",{className:"dropdown-container relative",children:[e.jsx("button",{className:"text-gray-400 hover:text-gray-600",onClick:I=>$(c,I),children:e.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 110-2 1 1 0 010 2zm7 0a1 1 0 110-2 1 1 0 010 2zm7 0a1 1 0 110-2 1 1 0 010 2z"})})}),b===c&&e.jsx("div",{className:"absolute right-0 top-8 z-10 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",onClick:I=>I.stopPropagation(),children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>w(h,c),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),"Edit"]}),e.jsxs("button",{onClick:()=>A(c),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})})]})]}),e.jsx("div",{className:"mt-2 space-y-2",children:h.is_general?e.jsxs("div",{className:"flex items-center gap-5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"font-medium text-gray-600",children:"General price"}),e.jsxs("div",{className:"group relative",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4 text-gray-500",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."})]})]}),e.jsxs("span",{className:"rounded-full bg-[#CBF5E5] px-3 py-1 font-medium text-primaryGreen",children:["$",h.general_rate,"/H"]})]}):h.price_by_hours.map((I,z)=>e.jsxs("div",{className:"flex items-center gap-5",children:[I.start_time&&I.end_time?e.jsxs("span",{className:"text-gray-600",children:[be(I.start_time)," -"," ",be(I.end_time)]}):e.jsx("span",{className:"text-gray-600",children:"All times"}),e.jsxs("span",{className:"rounded-full bg-[#CBF5E5] px-3 py-1 font-medium text-primaryGreen",children:["$",I.rate,"/H"]})]},z))})]},c)}),(l==null?void 0:l.length)===0&&e.jsxs("button",{onClick:ee,className:"flex w-fit items-center justify-center space-x-2 border-b border-black",children:[e.jsx("span",{className:"text-xl",children:"+"}),e.jsx("span",{children:"Add pricing"})]}),(l==null?void 0:l.length)>0&&e.jsxs("button",{onClick:ee,className:"flex w-fit items-center justify-center space-x-2 border-b border-black",children:[e.jsx("span",{className:"text-xl",children:"+"}),e.jsx("span",{children:"Add another"})]}),e.jsx(se,{onClick:Q,loading:L,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"})]})]}),e.jsx(qe,{isOpen:N,onClose:()=>{p(!1),R(null)},title:T?"Edit pricing":"Add pricing",showFooter:!1,children:e.jsx(ks,{handleSave:q,setValue:x,setPricingRows:f,pricingRows:l,editingPricing:T,defaultValues:_,sportsOffered:r})})]})}const ye=new he;function bt(){const G=Qe(),{dispatch:x}=a.useContext(Ye),[_,j]=a.useState(0),[r,l]=a.useState(!1),{dispatch:f}=a.useContext(Ne),[b,u]=a.useState(null),[N,p]=a.useState(!1),[T,R]=a.useState([]),[L]=a.useState([]),[U,$]=a.useState(null),ee=localStorage.getItem("role"),{triggerRefetch:q}=We(),Q=s=>{var n,H,v;if(!(s!=null&&s.club))return 0;const m=s.sports.some(Y=>Y.status===1);return s.club.club_location?s.club.splash_screen?m?(n=s.courts)!=null&&n.length?!s.club.account_details||s.club.account_details==='"[]"'?4:(H=s.club.days_off)!=null&&H.length?((v=s.pricing)!=null&&v.length,6):5:3:2:1:0},{register:A,handleSubmit:w,control:h,getValues:c,setValue:C,formState:{errors:I},watch:z}=Ve({defaultValues:{courts:[]}}),{fields:t,append:k,remove:S}=es({control:h,name:"courts"}),Z=async()=>{var s;try{const m=await ye.callRawAPI("/v3/api/custom/courtmatchup/club/profile",{},"GET");if(((s=m==null?void 0:m.model)==null?void 0:s.club.completed)==1){G("/club/dashboard");return}if(u(m==null?void 0:m.model),m!=null&&m.model){const{club:n,courts:H=[]}=m.model,v=n.club_location?JSON.parse(n.club_location):null,Y=n.account_details&&n.account_details!=='"[]"'?JSON.parse(n.account_details):[],D=n.club_location?JSON.stringify({lat:v.lat,lng:v.lng,address:v.address||""}):null;R(m.model.sports),C("club_location",D),C("splash_screen",JSON.parse(n.splash_screen)),C("sports",m.model.sports),C("times",n.times?JSON.parse(n.times):[]),C("max_players",n.max_players),C("account_details",Y),C("days_off",n.days_off?JSON.parse(n.days_off):[]),C("courts",H.length?H:[]),C("pricing",m.model.pricing||[]),C("club_logo",n.club_logo),C("name",n.name)}return m.model}catch(m){Te(x,m.code),X(f,m.message,3e3,"error")}},W=async()=>{try{const s=await ye.callRawAPI(`/v3/api/custom/courtmatchup/${ee}/stripe/account/verify`,{},"POST");$(s)}catch(s){return console.error("Error checking Stripe connection:",s),!1}},K=async()=>{var m;const s=c();p(!0);try{const n={};switch(_){case 0:n.club_location=s.club_location;break;case 1:n.splash_screen=s.splash_screen,n.club_logo=s.club_logo,n.name=s.name;break;case 2:break;case 3:n.courts=(m=s.courts)==null?void 0:m.map(v=>({type:v.type,sub_type:v.sub_type,sport_id:v.sport_id,club_sport_id:v==null?void 0:v.sport_id,name:v.name,...(v==null?void 0:v.id)&&{court_id:v.id}}));break;case 4:n.account_details=s.account_details;break;case 5:n.times=s.times,n.days_off=s.days_off,n.max_players=s.max_players;break;case 6:n.pricing=s.pricing,n.completed=1;break}await ye.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",n,"POST");const H=await Z();q(),u(H.model),j(v=>v+1)}catch(n){console.error(n),X(f,n.message,3e3,"error"),Te(x,n.code)}finally{p(!1)}},te=()=>{j(Math.max(_-1,0))},re=async s=>{p(!0);try{await ye.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",{pricing:s.pricing,completed:1},"POST"),l(!0)}catch(m){console.error(m),X(f,"Error saving data",3e3,"error")}finally{p(!1)}};a.useEffect(()=>{var s,m,n;(async()=>{const H=await Z();j(Q(H))})(),W(),Ke({title:(s=b==null?void 0:b.club)==null?void 0:s.name,path:"/club/profile-setup",clubName:(m=b==null?void 0:b.club)==null?void 0:m.name,favicon:(n=b==null?void 0:b.club)==null?void 0:n.club_logo,description:"Club Profile Setup"})},[]),a.useEffect(()=>{if(_===4){const s=setInterval(()=>{W()},5e3);return()=>clearInterval(s)}},[_]);const d=()=>{const s=z();switch(_){case 0:return e.jsx(gs,{onNext:K,register:A,setValue:C,errors:I,defaultValues:s,isSubmitting:N});case 1:return e.jsx(bs,{onNext:K,onBack:te,register:A,fields:t,append:k,remove:S,setValue:C,clubProfile:b,defaultValues:s,isSubmitting:N});case 2:return e.jsx(js,{onNext:K,register:A,errors:I,setValue:C,clubProfile:b,defaultValues:s,isSubmitting:N,defaultSports:L,fetchClubProfile:Z});case 3:return e.jsx(_s,{onNext:K,register:A,errors:I,values:s,setValue:C,watch:z,defaultValues:s,isSubmitting:N});case 4:return e.jsx(Ss,{onNext:K,isSubmitting:N,stripeConnectionData:U,setStripeConnectionData:$});case 5:return e.jsx(Ns,{onNext:K,register:A,errors:I,setValue:C,defaultValues:s,isSubmitting:N});case 6:return e.jsx(Es,{onNext:K,register:A,onSubmit:w(re),errors:I,setValue:C,defaultValues:s,isSubmitting:N,sportsOffered:T});default:return null}};return e.jsx(Xe,{children:e.jsxs("div",{className:"flex flex-col bg-white pb-7",children:[e.jsxs("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:[e.jsx("div",{className:"mb-10",children:_!==0&&e.jsxs("button",{className:"mt-5 flex items-center gap-2 text-[#525866]",onClick:te,children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 4.79199L3.125 10.0003L8.33333 15.2087M3.75 10.0003H16.875",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),e.jsx("span",{children:"Back"})]})}),e.jsx("div",{className:"flex flex-1 items-center justify-center",children:d()})]}),r&&e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-full max-w-xl rounded-2xl bg-white ",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-start gap-4 p-5",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Sign up complete!"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Congratulations! You have successfully signed up for Court Matchup. You can now access your club portal, manage your club, and start creating courts."})]})]}),e.jsx("div",{className:"flex w-full justify-end border-t border-gray-200 p-5",children:e.jsx(se,{onClick:()=>G("/club/dashboard"),className:"w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white",children:"Continue to Club portal!"})})]})})})]})})}export{bt as default};
