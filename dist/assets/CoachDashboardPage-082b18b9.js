import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as l,b as p,f as K}from"./vendor-851db8c1.js";import{M as T,T as z,G as I,A as F,e as W,ab as J,b as U,i as G,R as q}from"./index-a0784e19.js";import{h as y}from"./moment-a9aaa855.js";import{c as Q}from"./index.esm-b72032a7.js";import{G as X}from"./react-icons-51bc3cff.js";import{C as Z}from"./Calendar-9031b5fe.js";import E from"./Skeleton-1e8bf077.js";import{f as $}from"./date-fns-07266b7d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./react-loading-skeleton-3d87d1f5.js";function P(c){return X({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"}}]})(c)}new T;let ee=new z;function se({bookings:c}){var i,x,d,u,R,Y,A,V;const[h,g]=l.useState(0),[m,C]=l.useState(y()),[s,w]=l.useState([]),[n,k]=l.useState(null),[a,b]=l.useState(null),[N,j]=l.useState(!1),{dispatch:M}=l.useContext(I),{dispatch:L}=l.useContext(F),f=r=>{const o=r.clone().startOf("week"),_=[];for(let S=0;S<7;S++){const H=o.clone().add(S,"days");_.push({date:H.format("dddd • MMM D, YYYY"),events:[],count:0,isHighlighted:!1,dateStr:H.format("YYYY-MM-DD")})}return _};l.useEffect(()=>{const r=f(m);c&&c.length>0&&c.forEach(o=>{const _=y(o.date).format("YYYY-MM-DD"),S=r.findIndex(H=>H.dateStr===_);S!==-1&&(r[S].count+=1,r[S].events.push({time:`${y(o.start_time,"HH:mm:ss").format("hh:mm A")} - ${y(o.end_time,"HH:mm:ss").format("hh:mm A")}`,type:o.reservation_type===4?"LESSON":"CLINIC",players:o.player_ids?JSON.parse(o.player_ids).length:0,isHighlighted:!1,...o}))}),w(r)},[m,c]);const v=async()=>{if(h>0){const r=h-1;g(r),C(o=>o.clone().subtract(1,"week"))}},D=async()=>{const r=h+1;g(r),C(o=>o.clone().add(1,"week"))},O=()=>{const r=m.clone().startOf("week"),o=m.clone().endOf("week"),_=`${r.format("MMM D")} - ${o.format("MMM D")}`;return h===0?`This week (${_})`:h===1?`Next week (${_})`:`${h} weeks from now (${_})`},t=async r=>{j(!0);try{const o=JSON.parse(r.player_ids),_=await J(M,L,"user",o),S=await ee.getOne("coach",r.coach_id,{join:"user|user_id"});console.log({coachResponse:S}),b({...r,players_details:_.list,coach_details:S.model,coaches_details:[]})}catch(o){console.log({error:o}),U(M,o.message,5e3,"error")}finally{j(!1)}};return console.log({selectedEvent:a}),e.jsxs("div",{children:[N&&e.jsx(W,{}),e.jsxs("div",{className:"mx-auto max-w-7xl",children:[e.jsx("div",{children:e.jsx("div",{className:"mx-auto mb-5 mt-5 w-fit max-w-lg rounded-xl bg-white p-1",children:e.jsxs("div",{className:"flex items-center justify-between gap-4 rounded-xl bg-gray-50 p-2",children:[e.jsx("button",{onClick:v,className:`rounded-xl bg-white p-2 text-gray-600 ${h===0?"cursor-not-allowed opacity-50":"hover:text-gray-800"}`,children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:"text-lg font-medium",children:O()}),e.jsx("button",{onClick:D,className:"rounded-xl bg-white p-2 text-gray-600 hover:text-gray-800",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})})}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:[e.jsx("div",{className:"space-y-2",children:s.map((r,o)=>e.jsx("button",{onClick:()=>{k(r),b(null)},className:`w-full rounded-lg  border-[1.5px] ${(n==null?void 0:n.date)===r.date?" border-primaryBlue bg-blue-50":"border-gray-200 bg-white"} p-4`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-gray-900",children:r.date}),e.jsxs("div",{className:"flex items-center gap-1 text-gray-500",children:[e.jsx(Q,{className:"text-sm"}),e.jsx("span",{className:"text-sm",children:r.count})]})]})},r.date))}),((i=n==null?void 0:n.events)==null?void 0:i.length)>0&&e.jsx("div",{className:" space-y-2",children:n.events.map((r,o)=>e.jsxs("button",{onClick:()=>t(r),className:`flex w-full items-center justify-between rounded-lg border-[1.5px] border-gray-200 bg-white p-3 ${(a==null?void 0:a.id)===r.id?"border-primaryBlue":"border-gray-200"}`,children:[e.jsx("span",{className:"text-sm text-gray-900",children:r.time}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-primaryBlue",children:r.type}),e.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-sm text-primaryBlue",children:[e.jsx(P,{className:"text-sm"}),e.jsx("span",{className:"text-sm",children:r.players})]})]})]},o))}),a&&e.jsx("div",{className:"space-y-6 rounded-lg bg-white p-6 shadow-3",children:e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"mb-3 text-xs text-gray-600",children:[y(a.date).format("MMMM D, YYYY • hh:mm A")," ","-"," ",y(a.end_time,"HH:mm:ss").format("hh:mm A")]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"COURT"}),e.jsx("div",{className:"text-sm text-gray-900",children:a!=null&&a.court_id?`#${a==null?void 0:a.court_id}`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"HOURS OF COACHING"}),e.jsx("div",{className:"text-sm text-gray-900",children:a.duration})]}),e.jsx("div",{className:"border-t border-gray-200 pt-2"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"PLAYERS"}),e.jsx("div",{className:"mt-2 max-h-40 space-y-1 overflow-y-auto rounded-lg bg-gray-100 p-2",children:((x=a==null?void 0:a.players_details)==null?void 0:x.length)>0&&((d=a==null?void 0:a.players_details)==null?void 0:d.map((r,o)=>e.jsxs("div",{className:"text-sm text-gray-900",children:[r==null?void 0:r.first_name," ",r==null?void 0:r.last_name]},o)))})]}),e.jsx("div",{className:"border-t border-gray-200 pt-2"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"COACHES"}),e.jsxs("div",{className:"mt-2 max-h-40 space-y-1 overflow-y-auto rounded-lg bg-gray-100 p-2",children:[(a==null?void 0:a.coach_details)&&e.jsxs("div",{className:"text-sm text-gray-900",children:[(R=(u=a==null?void 0:a.coach_details)==null?void 0:u.user)==null?void 0:R.first_name," ",(A=(Y=a==null?void 0:a.coach_details)==null?void 0:Y.user)==null?void 0:A.last_name]}),(a==null?void 0:a.coaches_details.length)>0&&((V=a==null?void 0:a.coaches_details)==null?void 0:V.map((r,o)=>e.jsxs("div",{className:"text-sm text-gray-900",children:[r==null?void 0:r.first_name," ",r==null?void 0:r.last_name]},o)))]})]}),e.jsx("div",{className:"border-t border-gray-200 pt-2"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"NOTES"}),e.jsx("div",{className:"text-sm text-gray-900",children:a.notes||"N/A"})]})]})]})})]})]})]})}new T;let te=new z;function ae({coachProfile:c,clubProfile:h,bookings:g}){var t;l.useState(0),l.useState(y());const[m,C]=l.useState(null),[s,w]=l.useState(new Date),[n,k]=l.useState(null),[a,b]=l.useState(!1),{dispatch:N}=l.useContext(I),{dispatch:j}=l.useContext(F),M=()=>{w(new Date(s.setMonth(s.getMonth()-1)))},L=()=>{w(new Date(s.setMonth(s.getMonth()+1)))},f=i=>{C(i),k(null)},v=i=>{if(!i||!g)return[];const x=y(i).format("YYYY-MM-DD");return g.filter(d=>y(d.date).format("YYYY-MM-DD")===x)},D=async i=>{b(!0);try{const x=JSON.parse(i.player_ids),d=await J(N,j,"user",x),u=await te.getOne("coach",i.coach_id,{join:"user|user_id"});k({...i,players_details:d.list,coach_details:u.model,coaches_details:[]})}catch(x){console.log({error:x}),U(N,x.message,5e3,"error")}finally{b(!1)}},O=m?v(m):[];return e.jsxs("div",{children:[a&&e.jsx(W,{}),e.jsx("div",{className:"mx-auto mt-5 max-w-7xl",children:e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:[e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsx(Z,{currentMonth:s,selectedDate:m,onDateSelect:f,onPreviousMonth:M,onNextMonth:L,daysOff:h!=null&&h.days_off?JSON.parse(h.days_off):[],coachAvailability:Array.isArray(JSON.parse(c==null?void 0:c.availability))?JSON.parse(c==null?void 0:c.availability):[],bookings:g})}),m&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"text-lg font-medium text-gray-900",children:y(m).format("dddd, MMMM D, YYYY")}),O.length>0?e.jsx("div",{className:"space-y-2",children:O.map((i,x)=>e.jsxs("button",{onClick:()=>D(i),className:`flex w-full items-center justify-between rounded-lg border ${(n==null?void 0:n.id)===i.id?"border-primaryBlue bg-blue-50":"border-gray-200 bg-white"} p-3`,children:[e.jsx("span",{className:"text-sm text-gray-900",children:`${y(i.start_time,"HH:mm:ss").format("hh:mm A")} - ${y(i.end_time,"HH:mm:ss").format("hh:mm A")}`}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-primaryBlue",children:i.reservation_type===4?"LESSON":"CLINIC"}),e.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-sm text-primaryBlue",children:[e.jsx(P,{className:"text-sm"}),e.jsx("span",{className:"text-sm",children:i.player_ids?JSON.parse(i.player_ids).length:0})]})]})]},x))}):e.jsx("div",{className:"rounded-lg border border-gray-200 bg-white p-4 text-center text-gray-500",children:"No bookings for this date"})]}),n&&e.jsx("div",{className:"space-y-6 rounded-lg bg-white p-6 shadow-3",children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"mb-3 text-xs text-gray-600",children:y(n.date).format("dddd • MMM D, YYYY")}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"COURT"}),e.jsx("div",{className:"text-sm text-gray-900",children:n.court_id||"Not assigned"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"HOURS OF COACHING"}),e.jsx("div",{className:"text-sm text-gray-900",children:n.duration})]}),e.jsx("div",{className:"border-t border-gray-200 pt-2"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"PLAYERS"}),e.jsx("div",{className:"mt-2 max-h-40 space-y-1 overflow-y-auto rounded-lg bg-gray-100 p-2",children:(t=n==null?void 0:n.players_details)==null?void 0:t.map((i,x)=>e.jsxs("div",{className:"text-sm text-gray-900",children:[i==null?void 0:i.first_name," ",i==null?void 0:i.last_name]},x))})]}),e.jsx("div",{className:"border-t border-gray-200 pt-2"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-2 max-h-40 space-y-1 overflow-y-auto rounded-lg bg-gray-100 p-2",children:(n==null?void 0:n.coach_details)&&e.jsxs("div",{className:"text-sm text-gray-900",children:[n.coach_details.user.first_name," ",n.coach_details.user.last_name]})})]})]})]})})]})})]})}new T;let re=new z;const ie=[{header:"Date",accessor:"date"},{header:"Time",accessor:"time"},{header:"Type",accessor:"type"},{header:"Event",accessor:"event"},{header:"Duration",accessor:"duration"},{header:"Price",accessor:"price"},{header:"Status",accessor:"status"},{header:"",accessor:"actions"}],le=c=>{const g={0:{text:"Pending",class:"bg-yellow-100 text-yellow-800"},1:{text:"Confirmed",class:"bg-green-100 text-green-800"},2:{text:"Cancelled",class:"bg-red-100 text-red-800"}}[c]||{text:"Unknown",class:"bg-gray-100 text-gray-800"};return e.jsx("span",{className:`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${g.class}`,children:g.text})},ne=({bookings:c})=>{var b,N,j,M,L,f,v,D,O;const{dispatch:h}=p.useContext(F),{dispatch:g}=p.useContext(I);p.useState([]),p.useState(10),p.useState(0),p.useState(0),p.useState(0),p.useState(!1),p.useState(!1);const[m,C]=p.useState(!1),[s,w]=p.useState(null);K(),p.useRef(null);const[n,k]=p.useState(!1),a=async t=>{k(!0);try{const i=JSON.parse(t.player_ids||"[]"),x=await J(g,h,"user",i),d=await re.getOne("coach",t.coach_id,{join:"user|user_id"});w({...t,players_details:x.list||[],coach_details:d.model||null,coaches_details:[]})}catch(i){console.log({error:i}),U(g,i.message,5e3,"error")}finally{k(!1)}};return e.jsxs("div",{className:"h-screen ",children:[n&&e.jsx(W,{}),m?e.jsx(E,{}):e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsx("tr",{children:ie.map((t,i)=>e.jsx("th",{scope:"col",className:"px-6 py-4 text-left text-sm font-medium text-gray-500",children:t.header},i))})}),e.jsx("tbody",{children:c.map((t,i)=>{var x;return e.jsxs("tr",{className:"cursor-pointer hover:bg-gray-50",onClick:()=>a(t),children:[e.jsx("td",{className:"whitespace-nowrap bg-gray-100 px-4 py-3",children:$(new Date(t.date),"MMM dd, yyyy")}),e.jsx("td",{className:"whitespace-nowrap bg-gray-100 px-4 py-3",children:`${$(new Date(`2000-01-01T${t.start_time}`),"h:mm a")} - ${$(new Date(`2000-01-01T${t.end_time}`),"h:mm a")}`}),e.jsx("td",{className:"whitespace-nowrap bg-gray-100 px-4 py-3",children:e.jsx("span",{className:"capitalize",children:`${t.type} ${t.subtype?`- ${t.subtype}`:""}`})}),e.jsx("td",{className:"whitespace-nowrap bg-gray-100 px-4 py-3 capitalize",children:(x=G.find(d=>d.value==(t==null?void 0:t.reservation_type)))==null?void 0:x.label}),e.jsxs("td",{className:"whitespace-nowrap bg-gray-100 px-4 py-3",children:[t.duration," ",t.duration===1?"hour":"hours"]}),e.jsxs("td",{className:"whitespace-nowrap bg-gray-100 px-4 py-3",children:["$",t.price]}),e.jsx("td",{className:"whitespace-nowrap bg-gray-100 px-4 py-3",children:le(t.status)}),e.jsx("td",{className:"rounded-r-xl bg-gray-100 px-4 py-3",children:e.jsx("div",{className:"flex items-center justify-end gap-3",children:e.jsx("button",{onClick:()=>a(t),className:"rounded-full p-2 transition-colors hover:bg-gray-200",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})})})]},i)})})]}),!m&&c.length===0&&e.jsx("div",{className:"w-full px-6 py-4 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No bookings available"})})]}),e.jsx(q,{isOpen:!!s,onClose:()=>w(null),title:"Event details",showFooter:!1,children:s&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Date and time"}),e.jsxs("p",{className:"mt-1 text-lg font-medium",children:[$(new Date(s.date),"MMM dd, yyyy")," •"," ",$(new Date(`2000-01-01T${s.start_time}`),"hh:mm a")," ","-"," ",$(new Date(`2000-01-01T${s.end_time}`),"hh:mm a")]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"EVENT"}),e.jsx("p",{className:"mt-1 text-lg font-medium capitalize",children:((b=G.find(t=>t.value==s.reservation_type))==null?void 0:b.label)||"Lesson"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COURT"}),e.jsx("p",{className:"mt-1 text-lg font-medium",children:`${s.court_id?`#${s.court_id}`:"N/A"}`})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"HOURS OF COACHING"}),e.jsx("p",{className:"mt-1 text-lg font-medium",children:s.duration})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"}),e.jsx("div",{className:"mt-2 max-h-40 space-y-1 overflow-y-auto rounded-lg bg-gray-100 p-2",children:((N=s==null?void 0:s.players_details)==null?void 0:N.length)>0&&((j=s==null?void 0:s.players_details)==null?void 0:j.map((t,i)=>e.jsxs("div",{className:"text-sm capitalize text-gray-900",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]},i)))})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACHES"}),e.jsxs("div",{className:"mt-2 max-h-40 space-y-1 overflow-y-auto rounded-lg bg-gray-100 p-2",children:[(s==null?void 0:s.coach_details)&&e.jsxs("div",{className:"text-sm capitalize text-gray-900",children:[(L=(M=s==null?void 0:s.coach_details)==null?void 0:M.user)==null?void 0:L.first_name," ",(v=(f=s==null?void 0:s.coach_details)==null?void 0:f.user)==null?void 0:v.last_name]}),((D=s==null?void 0:s.coaches_details)==null?void 0:D.length)>0&&((O=s==null?void 0:s.coaches_details)==null?void 0:O.map((t,i)=>e.jsxs("div",{className:"text-sm text-gray-900",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]},i)))]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SECTION"}),e.jsx("p",{className:"mt-1 text-lg font-medium",children:s.details||"No details available"})]})]})})]})};let B=new T;const Ke=()=>{const{dispatch:c}=p.useContext(I),[h,g]=l.useState("All reservations"),[m,C]=l.useState("list"),[s,w]=l.useState({list:[]}),[n,k]=l.useState(null),[a,b]=l.useState(null),[N,j]=l.useState([]),[M,L]=l.useState(window.innerWidth<768),[f,v]=l.useState(!1);p.useEffect(()=>{c({type:"SETPATH",payload:{path:"coach"}})},[]);const D=async()=>{try{v(!0);const d=await B.callRawAPI("/v3/api/custom/courtmatchup/coach/bookings",{},"GET");w(d),j(d.list||[])}catch(d){console.error("Error fetching reservations:",d),w({list:[]}),j([])}finally{v(!1)}},O=d=>{var R;const u=d.target.value;if(g(u),u==="All reservations")j((s==null?void 0:s.list)||[]);else{const Y=((R=s==null?void 0:s.list)==null?void 0:R.filter(A=>A.type===u))||[];j(Y)}};async function t(){var d;try{v(!0);const u=await B.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),R=await B.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${((d=u==null?void 0:u.model)==null?void 0:d.club_id)||10}`,{},"GET");k(u),b(R.model)}catch(u){console.error("Error fetching coach profile:",u)}finally{v(!1)}}l.useEffect(()=>{t()},[]),l.useEffect(()=>{D()},[]),l.useEffect(()=>{const d=()=>{L(window.innerWidth<768)};return window.addEventListener("resize",d),()=>{window.removeEventListener("resize",d)}},[]);const i=()=>{D(),t()},x=()=>e.jsxs("div",{className:"mb-4 flex flex-col items-start justify-between gap-3 sm:flex-row sm:items-center sm:gap-0 md:mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h1",{className:"mb-3 text-xl font-semibold text-gray-900 sm:mb-0 sm:text-2xl",children:"My schedule"}),e.jsx("button",{onClick:i,className:"ml-2 rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700","aria-label":"Refresh data",title:"Refresh",disabled:f,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 ${f?"animate-spin":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]}),e.jsxs("div",{className:"flex w-full flex-col items-start gap-3 sm:w-auto sm:flex-row sm:items-center sm:gap-4",children:[e.jsx("div",{className:"relative w-full sm:w-auto",children:e.jsxs("select",{value:h,onChange:O,className:"w-full rounded-lg border border-gray-200 bg-white px-3 py-2 pr-8 text-sm capitalize focus:border-blue-500 focus:outline-none sm:w-auto sm:px-4","aria-label":"Filter reservations",disabled:f,children:[e.jsx("option",{value:"All reservations",children:"All reservations"}),G.map(d=>e.jsx("option",{value:d.value,children:d.label},d.value))]})}),e.jsxs("div",{className:"flex w-full rounded-lg border border-gray-200 sm:w-auto",children:[e.jsx("button",{onClick:()=>C("list"),className:`flex-1 px-2 py-2 text-xs sm:flex-auto sm:px-4 sm:text-sm ${m==="list"?"font-medium text-blue-600":"text-gray-700"} hover:bg-gray-50`,"aria-label":"List view",disabled:f,children:"List"}),e.jsx("button",{onClick:()=>C("calendar"),className:`flex-1 px-2 py-2 text-xs sm:flex-auto sm:px-4 sm:text-sm ${m==="calendar"?"font-medium text-blue-600":"text-gray-700"} hover:bg-gray-50`,"aria-label":"Calendar view",disabled:f,children:"Calendar"}),e.jsx("button",{onClick:()=>C("table"),className:`flex-1 px-2 py-2 text-xs sm:flex-auto sm:px-4 sm:text-sm ${m==="table"?"font-medium text-blue-600":"text-gray-700"} hover:bg-gray-50`,"aria-label":"Table view",disabled:f,children:"Table"})]})]})]});return e.jsxs("div",{className:"relative min-h-screen bg-gray-50 p-3 sm:p-4 md:p-6",children:[f&&e.jsx(W,{}),x(),e.jsxs(e.Fragment,{children:[m==="list"&&e.jsx("div",{className:"overflow-hidden rounded-lg",children:e.jsx("div",{className:`transition-all duration-300 ${M?"px-0":"px-2"}`,children:e.jsx(se,{bookings:N})})}),m==="calendar"&&e.jsx("div",{className:"overflow-hidden rounded-lg",children:e.jsx("div",{className:"transition-all duration-300",children:e.jsx(ae,{coachProfile:n,clubProfile:a,bookings:N})})}),m==="table"&&e.jsx("div",{className:"overflow-x-auto rounded-lg shadow-sm",children:e.jsx("div",{className:"min-w-full",children:e.jsx(ne,{bookings:N})})})]})]})};export{Ke as default};
