import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,f as S}from"./vendor-851db8c1.js";import{u as F}from"./react-hook-form-687afde5.js";import{o as T}from"./yup-2824f222.js";import{c as P,a as l}from"./yup-54691517.js";import{w as $,A as q,G as R,M as B,b as D,t as G}from"./index-a0784e19.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const I=({setSidebar:m,getData:E})=>{var h,b,g,f,j,y;const[n,d]=r.useState(!1),k=P({slug:l().required(),subject:l().required(),html:l().required(),tag:l().required()}).required(),{dispatch:A}=r.useContext(q),{dispatch:c}=r.useContext(R),C=S(),{register:a,handleSubmit:p,setError:u,formState:{errors:s}}=F({resolver:T(k)}),x=async o=>{let w=new B;d(!0);try{w.setTable("email");const t=await w.callRestAPI({slug:o.slug,subject:o.subject,html:o.html,tag:o.tag},"POST");if(!t.error)C("/admin/email"),D(c,"Added"),E(1,15);else if(t.validation){const N=Object.keys(t.validation);for(let i=0;i<N.length;i++){const v=N[i];u(v,{type:"manual",message:t.validation[v]})}}}catch(t){console.log("Error",t),u("subject",{type:"manual",message:t.message}),G(A,t.message)}d(!1)};return r.useEffect(()=>{c({type:"SETPATH",payload:{path:"email"}})},[]),e.jsxs("div",{className:"mx-auto  rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Add Email"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#F4F4F4]",onClick:()=>m(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(x)(),m(!1)},disabled:n,children:n?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full max-w-lg p-4 text-left",onSubmit:p(x),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",...a("slug"),className:`focus:shadow-outline } w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow
focus:outline-none`})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"subject",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...a("subject"),className:`focus: shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(h=s.subject)!=null&&h.message?"border-red-500":""} `}),e.jsx("p",{className:"text-xs italic text-red-500",children:(b=s.subject)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...a("tag"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(g=s.tag)!=null&&g.message?"border-red-500":""} `}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=s.tag)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(j=s.html)!=null&&j.message?"border-red-500":""}`,...a("html"),rows:15}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=s.html)==null?void 0:y.message})]})]})]})},we=$(I,"email","You don't have permission to add emails");export{we as default};
