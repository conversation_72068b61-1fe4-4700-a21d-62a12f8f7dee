import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as s,b as we}from"./vendor-851db8c1.js";import{d as et,M as ve,ak as ut,R as De,G as Pe,e as Be,al as ht,b as R,K as tt,a3 as Ze,H as xe,J as de,E as ye}from"./index-a0784e19.js";import{u as ft}from"./react-hook-form-687afde5.js";import{S as ze}from"./react-select-c8303602.js";import{B as Re}from"./BottomDrawer-4cdfc0e3.js";import{H as xt}from"./HistoryComponent-0c9f35b4.js";import{P as pt}from"./PencilIcon-35185602.js";import{T as gt}from"./TrashIcon-7d213648.js";import{T as yt}from"./TimeSlotGrid-3140c36d.js";import{b as Le}from"./@headlessui/react-a5400090.js";function wt({title:o,titleId:r,...l},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":r},l),o?s.createElement("title",{id:r},o):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))}const vt=s.forwardRef(wt),jt=vt;function He({isOpen:o,onClose:r,onConfirm:l,eventCount:n,affectedReservations:u=[],isSubmitting:w,type:_="hours"}){if(!o)return null;const T=C=>{if(!C)return"";const[H,G]=C.split(":"),k=parseInt(H),p=k>=12?"PM":"AM";return`${k%12||12}:${G} ${p}`},F=C=>C?new Date(C).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"}):"";return e.jsxs("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-2xl rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Confirm Changes"}),e.jsx("p",{className:"mb-4 text-gray-600",children:`Changing ${_==="court"?"this court":`these ${_}`} will delete ${n} existing reservation${n!==1?"s":""}.`}),u.length>0&&e.jsxs("div",{className:"mb-6 max-h-64 overflow-y-auto rounded-lg border border-gray-200",children:[e.jsx("div",{className:"bg-gray-50 px-4 py-2",children:e.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Affected Reservations:"})}),e.jsx("div",{className:"divide-y divide-gray-200",children:u.map((C,H)=>e.jsx("div",{className:"px-4 py-3 text-sm",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:C.sport_name}),e.jsx("p",{className:"text-gray-600",children:C.first_name&&C.last_name?`${C.first_name} ${C.last_name}`:C.email})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-gray-900",children:F(C.date)}),e.jsxs("p",{className:"text-gray-600",children:[T(C.start_time)," -"," ",T(C.end_time)]})]})]})},H))})]}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to continue?"}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:r,className:"rounded-lg border border-gray-200 px-6 py-2",disabled:w,children:"Cancel"}),e.jsx(et,{onClick:l,className:"rounded-lg bg-primaryBlue px-6 py-2 text-white",loading:w,children:"Confirm"})]})]})]})}const st=we.forwardRef(({onSubmit:o=()=>{},initialData:r={},club:l={},isOpen:n=!1,onClose:u=()=>{},title:w="Edit club settings",onPrimaryAction:_=()=>{},submitting:T=!1},F)=>{const C=new ve,{handleSubmit:H}=ft(),G=i=>{if(!i)return i;const[b,t]=i.split(":").map(Number),a=b*60+t,m=Math.round(a/30)*30,O=Math.floor(m/60)%24,M=m%60;return`${O.toString().padStart(2,"0")}:${M.toString().padStart(2,"0")}:00`},[k,p]=s.useState([{from:"",until:""}]),[V,W]=s.useState(!1),[Y,f]=s.useState(0),[x,h]=s.useState([]),[A,$]=s.useState(!1),[S,K]=s.useState(!1),[c,N]=s.useState(null);we.useEffect(()=>{if(console.log("Club data or modal state changed:",{clubTimes:l==null?void 0:l.times,isOpen:n,clubExists:!!l}),l!=null&&l.times)try{const i=JSON.parse(l.times);if(console.log("Parsed times from club:",i),i.length>0){console.log("Club data changed, updating time slots:",i);const b=i.map(t=>({from:G(t.from),until:G(t.until)}));console.log("Rounded times to 30-minute intervals:",b),p(b)}else console.log("Parsed times array is empty, setting default slot"),p([{from:"",until:""}])}catch(i){console.error("Error parsing club times:",i),p([{from:"",until:""}])}else console.log("No club times data, setting default slot"),p([{from:"",until:""}])},[l==null?void 0:l.times,n]),we.useEffect(()=>{console.log("Current time slots:",k)},[k]);const j=(i,b,t)=>{p(a=>{const m=[...a];return m[i]={...m[i],[b]:t?t.value:""},m})},g=async i=>{try{K(!0);const b=await C.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{times:i.times,days_off:i.days_off},"POST");return b&&!b.error&&b.total_affected>0?(f(b.total_affected),h(b.affected_reservations||[]),N(i),W(!0),!0):!1}catch(b){return console.error("Error checking for affected events:",b),!1}finally{K(!1)}},L=async()=>{$(!0);try{const i={...c,delete_affected_events:!0};await o(i),W(!1),N(null)}catch(i){console.error("Error saving changes:",i)}finally{$(!1)}},U=()=>{W(!1),N(null),f(0),h([])};we.useImperativeHandle(F,()=>({submit:async()=>new Promise(i=>{H(async b=>{const t=Object.entries(P.daysOff).filter(([M,Q])=>Q).map(([M])=>M),a=P.dailyBreaks?[{start:P.breakStartTime,end:P.breakEndTime}]:[],m={times:k,daily_breaks:a,days_off:t};await g(m)||await o(m),i(m)})()})}));const[P,J]=s.useState({times:k,daysOff:{Monday:!1,Tuesday:!1,Wednesday:!1,Thursday:!1,Friday:!1,Saturday:!1,Sunday:!1,...l!=null&&l.days_off?JSON.parse(l.days_off).reduce((i,b)=>({...i,[b]:!0}),{}):{}},dailyBreaks:!1,breakStartTime:"09:00:00",breakEndTime:"10:00:00",...r});we.useEffect(()=>{if(l&&(J(i=>({...i,daysOff:{Monday:!1,Tuesday:!1,Wednesday:!1,Thursday:!1,Friday:!1,Saturday:!1,Sunday:!1,...l!=null&&l.days_off?JSON.parse(l.days_off).reduce((b,t)=>({...b,[t]:!0}),{}):{}}})),l!=null&&l.daily_breaks))try{const i=JSON.parse(l.daily_breaks);i&&i.length>0&&J(b=>({...b,dailyBreaks:!0,breakStartTime:i[0].start||b.breakStartTime,breakEndTime:i[0].end||b.breakEndTime}))}catch(i){console.error("Error parsing daily breaks:",i)}},[l]);const Z=i=>{J(b=>({...b,daysOff:{...b.daysOff,[i]:!b.daysOff[i]}}))},I=ut().map(i=>({...i,value:i.value+":00"}));console.log("Time options generated:",I.length,"options"),console.log("First few time options:",I.slice(0,5));const X=()=>{p(i=>[...i,{from:"",until:""}])},te=i=>{p(b=>b.filter((t,a)=>a!==i))};return e.jsxs(e.Fragment,{children:[e.jsx(De,{isOpen:n,onClose:u,title:w,onPrimaryAction:_,submitting:T||S,children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"General opening hours"}),(k.length>0?k:[{from:"",until:""}]).map((i,b)=>e.jsxs("div",{className:"mb-5 flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(ze,{classNamePrefix:"select",options:I,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null},value:(()=>{const t=I.find(a=>a.value===i.from);return console.log(`Finding option for from=${i.from}:`,t),t||null})(),onChange:t=>j(b,"from",t),placeholder:"Select time",isClearable:!0})}),e.jsx("div",{className:"flex-1",children:e.jsx(ze,{classNamePrefix:"select",components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null},options:i.from?I.filter(t=>t.value>i.from):I,value:(()=>{const t=I.find(a=>a.value===i.until);return console.log(`Finding option for until=${i.until}:`,t),t||null})(),onChange:t=>j(b,"until",t),placeholder:"Select time",isDisabled:!i.from,isClearable:!0})})]}),k.length>1&&e.jsx("button",{onClick:()=>te(b),className:" text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]},b))]}),e.jsx("button",{onClick:X,className:"mb-5 text-primaryBlue underline hover:text-primaryBlue/80",children:"+Add another time slot"}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"Days off"}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:Object.keys(P.daysOff).map(i=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:P.daysOff[i],onChange:()=>Z(i),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-600",children:i})]},i))})]})]})}),e.jsx(He,{isOpen:V,onClose:U,onConfirm:L,eventCount:Y,affectedReservations:x,isSubmitting:A,type:"hours"})]})});st.displayName="ClubSettingsEditForm";const Nt=st;function bt({initialData:o={},onSubmit:r,setExceptionName:l,exceptionName:n}){return e.jsx("div",{className:"flex flex-col gap-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"Exception details"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("label",{className:"mb-2 block text-sm text-gray-600",children:"Name"}),e.jsx("input",{type:"text",value:n,onChange:u=>l(u.target.value),placeholder:"Enter exception name",className:"w-full rounded-xl border border-gray-200 px-3 py-2 text-sm shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"})]})]})})}const Ct=new ve;function Ge({onSubmit:o,initialData:r,onClose:l,isOpen:n,isEdit:u,club:w,sports:_=[],courts:T,title:F,primaryButtonText:C,submitting:H,showFooter:G=!1,onPrimaryAction:k}){var Q,ne,Se;const[p,V]=s.useState({name:(r==null?void 0:r.name)||"",sport_id:Number(r==null?void 0:r.sport_id)||null,type:(r==null?void 0:r.type)||"",sub_type:(r==null?void 0:r.sub_type)||null}),[W,Y]=s.useState(!1),[f,x]=s.useState(!1),[h,A]=s.useState(!1),[$,S]=s.useState(null),[K,c]=s.useState(null),[N,j]=s.useState(!1),[g,L]=s.useState(0),[U,P]=s.useState(null),{dispatch:J}=s.useContext(Pe),Z=()=>{V({name:"",sport_id:null,type:"",sub_type:null})};s.useEffect(()=>{r?V({name:r.name||"",sport_id:Number(r.sport_id)||null,type:r.type||"",sub_type:r.sub_type||null}):u||Z()},[r,u]);const I=s.useCallback(v=>{const{name:z,value:oe}=v.target;V(me=>{const he=z==="sport_id"?Number(oe):oe;if(me[z]===he)return me;const ae={...me};return ae[z]=he,z==="sport_id"&&(ae.type="",ae.sub_type=null),z==="type"&&(ae.sub_type=null),ae})},[]),X=async()=>{A(!1),x(!0);try{const v={court_id:r.id,...p,sport_id:$,type:"",sub_type:null,sport_change_option:K};if(await i(v)){x(!1);return}await o(v),u||Z()}catch(v){console.error(v)}finally{x(!1)}},te=()=>{S(null),c(null),A(!1)},i=async v=>{try{await Ct.callRawAPI("/v3/api/custom/courtmatchup/club/check-affected-events",{court_id:v.court_id,sport_id:v.sport_id,type:v.type,sub_type:v.sub_type},"POST");const z={affected_events_count:Math.floor(Math.random()*5)};return z.affected_events_count>0?(L(z.affected_events_count),P(v),j(!0),!0):!1}catch(z){return console.error("Error checking for affected events:",z),!1}},b=async()=>{x(!0);try{await o(U),j(!1),u||Z()}catch(v){console.error("Error saving changes:",v)}finally{x(!1)}},t=async v=>{if(v.preventDefault(),m.length>0&&!p.type){R(J,"Please select a court type",3e3,"error");return}if(O.length>0&&!p.sub_type){R(J,"Please select a surface type",3e3,"error");return}if(u&&(r!=null&&r.sport_id)&&p.sport_id!==r.sport_id){S(p.sport_id),A(!0);return}x(!0);try{const z=u?{court_id:r.id,...p}:p;if(u&&await i(z)){x(!1);return}await o(z),u||Z()}catch(z){console.error(z)}finally{x(!1)}},a=_.find(v=>v.id===p.sport_id),m=((Q=a==null?void 0:a.sport_types)==null?void 0:Q.filter(v=>v.type))||[],O=((Se=(ne=a==null?void 0:a.sport_types)==null?void 0:ne.find(v=>v.type===p.type))==null?void 0:Se.subtype)||[],M=()=>{var pe,_e;const[v,z]=s.useState(""),oe=ue=>{z(ue.target.value)},me=()=>{c(Number(v)),X()},he=((pe=_.find(ue=>ue.id===(r==null?void 0:r.sport_id)))==null?void 0:pe.name)||"previous sport",ae=((_e=_.find(ue=>ue.id===$))==null?void 0:_e.name)||"new sport";return e.jsxs("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-lg bg-white p-6",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Change Sport"}),e.jsxs("p",{className:"mb-4 text-gray-600",children:["You are changing the sport for this space from"," ",e.jsx("strong",{children:he})," to ",e.jsx("strong",{children:ae}),". However, any events for this space will still be listed under the previous sport, including in the daily scheduler."]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"Would you like to:"}),e.jsxs("div",{className:"mb-6 space-y-3",children:[e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"1",checked:v==="1",onChange:oe,className:"mt-1 h-4 w-4"}),e.jsxs("span",{children:["A: Keep these events (events will remain under ",he,")"]})]}),e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"2",checked:v==="2",onChange:oe,className:"mt-1 h-4 w-4"}),e.jsx("span",{children:"B: Delete these events"})]}),e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"3",checked:v==="3",onChange:oe,className:"mt-1 h-4 w-4"}),e.jsxs("span",{children:["C: Change the sport listed for these events to ",ae]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:te,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:me,disabled:!v,className:`rounded-lg px-4 py-2 text-white ${v?"bg-primaryBlue hover:bg-blue-700":"cursor-not-allowed bg-gray-400"}`,children:"Confirm"})]})]})]})};return e.jsxs("div",{children:[W&&e.jsx(Be,{}),e.jsx(De,{isOpen:n!==void 0?n:!0,onClose:l,title:F||(u?"Edit court":"Add new court"),showFooter:G,primaryButtonText:C||(u?"Save changes":"Add court"),onPrimaryAction:k,submitting:H||f,children:e.jsx(ht,{isLoading:W,children:e.jsxs("form",{onSubmit:t,className:"flex h-full flex-col",children:[e.jsxs("div",{className:"h-full flex-1 space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Court Name"}),e.jsx("input",{type:"text",name:"name",value:p.name,onChange:I,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Enter court name",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Sport"}),e.jsx("div",{className:"mt-2 space-x-4",children:_.filter(v=>v.status===1).map(v=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"sport_id",value:v.id,checked:p.sport_id===v.id,onChange:I,className:"form-radio"}),e.jsx("span",{className:"ml-2",children:v.name})]},v.id))})]}),m.length>0&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"mt-2 space-x-4",children:m.map(v=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"type",value:v.type,checked:p.type===v.type,onChange:I,className:"form-radio",required:!0}),e.jsx("span",{className:"ml-2",children:v.type})]},v.type))})]}),p.type&&O.length>0&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Sub Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"mt-2 space-x-4",children:O.map(v=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"sub_type",value:v,checked:p.sub_type===v,onChange:I,className:"form-radio",required:!0}),e.jsx("span",{className:"ml-2",children:v})]},v))})]})]}),e.jsxs("div",{className:"flex flex-shrink-0 justify-end gap-4 border-t border-gray-200 px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:l,children:"Cancel"}),e.jsx(et,{type:"submit",loading:f,className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:u?"Save changes":"Add court"})]})]})})}),h&&e.jsx(M,{}),e.jsx(He,{isOpen:N,onClose:()=>j(!1),onConfirm:b,eventCount:g,isSubmitting:f,type:"court"})]})}function Ye({showTimesAvailableModal:o,setShowTimesAvailableModal:r,selectedCourt:l,setSelectedTimes:n,isSubmitting:u,selectedTimes:w,onSave:_,minTime:T=0,maxTime:F=23,allowMultipleSlots:C=!0,title:H="Times available"}){const[G,k]=s.useState(null),[p,V]=s.useState(!1),[W,Y]=s.useState(null),[f,x]=s.useState(null),[h,A]=s.useState({start:null,current:null}),$=s.useRef(new Map),S=s.useRef(null),K=s.useRef(new Set),c=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],j=(()=>{const t=[];for(let a=0;a<=23;a++)t.push(`${a.toString().padStart(2,"0")}:00:00`),t.push(`${a.toString().padStart(2,"0")}:30:00`);return t})(),g=(t,a)=>{n(m=>m.some(M=>M.time===t&&M.day===a)?m.filter(M=>!(M.time===t&&M.day===a)):[...m,{time:t,day:a}])},L=(t,a)=>{k(null)},U=(t,a)=>{n(m=>m.filter(O=>!(O.time===t&&O.day===a))),k(null)},P=()=>{const t={};return w.forEach(a=>{const m=a.day.toLowerCase();t[m]||(t[m]=[]),t[m].push(a.time)}),Object.entries(t).map(([a,m])=>({day:a,timeslots:[...new Set(m)].sort()}))},J=t=>{const[a,m]=t.split(":"),O=parseInt(a),M=O>=12?"PM":"AM";let Q=O%12;return Q===0&&(Q=12),`${Q}:${m} ${M}`},Z=s.useCallback((t,a)=>{if(!t||!a.start||!a.current)return!1;const m=t.getBoundingClientRect(),O=Math.min(a.start.x,a.current.x),M=Math.max(a.start.x,a.current.x),Q=Math.min(a.start.y,a.current.y),ne=Math.max(a.start.y,a.current.y);return!(m.right<O||m.left>M||m.bottom<Q||m.top>ne)},[]),I=s.useCallback(t=>{S.current&&cancelAnimationFrame(S.current),S.current=requestAnimationFrame(()=>{$.current.forEach((a,m)=>{if(Z(a,t)){const[O,M]=m.split("|");w.some(ne=>ne.time===M&&ne.day===O)||g(M,O)}})})},[Z,w]),X=(t,a,m)=>{V(!0),Y(t),x(a);const O={start:{x:m.clientX,y:m.clientY},current:{x:m.clientX,y:m.clientY}};A(O),K.current.clear(),g(t,a)},te=s.useCallback(t=>{if(p){t.preventDefault();const a={...h,current:{x:t.clientX,y:t.clientY}};A(a),I(a)}},[p,h,I]),i=s.useCallback(()=>{V(!1),Y(null),x(null),A({start:null,current:null}),K.current.clear(),S.current&&cancelAnimationFrame(S.current)},[]),b=(t,a)=>{p&&(w.some(O=>O.time===t&&O.day===a)||g(t,a))};return s.useEffect(()=>{if(p)return window.addEventListener("mousemove",te),window.addEventListener("mouseup",i),()=>{window.removeEventListener("mousemove",te),window.removeEventListener("mouseup",i)}},[p,te,i]),e.jsx(Re,{isOpen:o,onClose:()=>r(!1),title:H,onDiscard:()=>{n([]),r(!1)},discardLabel:"Discard",showActions:!0,isSubmitting:u,saveLabel:"Save changes",leftElement:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]}),e.jsx("p",{className:"text-xl",children:l==null?void 0:l.name})]}),onSave:()=>{const t=P();_==null||_(t),r(!1)},children:e.jsxs("div",{className:"w-full select-none",children:[p&&h.start&&h.current&&e.jsx("div",{style:{position:"fixed",left:Math.min(h.start.x,h.current.x),top:Math.min(h.start.y,h.current.y),width:Math.abs(h.current.x-h.start.x),height:Math.abs(h.current.y-h.start.y),backgroundColor:"rgba(59, 130, 246, 0.2)",border:"2px solid rgb(59, 130, 246)",pointerEvents:"none",zIndex:1e3}}),e.jsx("div",{className:"grid grid-cols-7 gap-5",children:c.map(t=>e.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[e.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium capitalize",children:t}),e.jsx("div",{className:"space-y-2",children:j.map(a=>{const m=w.some(M=>M.time===a&&M.day===t),O=`${t}-${a}`;return e.jsxs("div",{className:"relative",children:[e.jsx("button",{ref:M=>{M&&$.current.set(`${t}|${a}`,M)},className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium text-gray-500 ${m?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":"border-gray-300 hover:border-gray-400"}`,onMouseDown:M=>X(a,t,M),onMouseEnter:()=>b(a,t),onMouseUp:i,onClick:()=>{m?U(a,t):g(a,t)},children:J(a)}),G===O&&e.jsx("div",{className:"absolute right-0 z-10 mt-1 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>L(),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),"Edit"]}),e.jsxs("button",{onClick:()=>U(a,t),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})})]},`${t}-${a}`)})})]},t))})]})})}function St({showExceptionTimeSelector:o,setShowExceptionTimeSelector:r,selectedException:l,setSelectedExceptionTimes:n,selectedExceptionTimes:u,exceptions:w,onSelectException:_,onSave:T,isSubmitting:F}){s.useContext(Pe);const C=new ve,[H,G]=s.useState(null),[k,p]=s.useState(!1),[V,W]=s.useState(null),[Y,f]=s.useState(null),x=s.useRef(null),[h,A]=s.useState(!1),[$,S]=s.useState((l==null?void 0:l.name)||""),K=s.useRef(null),[c,N]=s.useState(!1),[j,g]=s.useState(0),[L,U]=s.useState([]),[P,J]=s.useState(!1),[Z,I]=s.useState(null);s.useEffect(()=>{S((l==null?void 0:l.name)||"")},[l]),s.useEffect(()=>{h&&K.current&&K.current.focus()},[h]);const[X,te]=s.useState(()=>(w==null?void 0:w.findIndex(d=>d.name===(l==null?void 0:l.name)))||0),i=()=>{if(X>0){const d=X-1;te(d),_(w[d])}},b=()=>{if(X<w.length-1){const d=X+1;te(d),_(w[d])}},t=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],m=(()=>{const d=[];for(let y=8;y<=22;y++)d.push(`${y.toString().padStart(2,"0")}:00:00`),d.push(`${y.toString().padStart(2,"0")}:30:00`);return d})(),O=(d,y)=>{const q=u.some(D=>D.time===d&&D.day===y);n(q?D=>D.filter(re=>!(re.time===d&&re.day===y)):D=>[...D,{time:d,day:y}])},M=(d,y)=>{n(q=>q.filter(D=>!(D.time===d&&D.day===y))),G(null)},Q=()=>{const d=t.map(y=>{const q=u.filter(D=>D.day===y).map(D=>D.time).sort();return q.length>0?{day:y.toLowerCase(),timeslots:q}:null}).filter(Boolean);return{name:l==null?void 0:l.name,days:d}},ne=d=>{const[y,q]=d.split(":").map(Number),D=y===0?12:y>12?y-12:y,re=y<12?"AM":"PM",ie=q.toString().padStart(2,"0");return`${D}:${ie} ${re}`},Se=d=>{S(d.target.value)},v=()=>{$.trim()&&(l.name=$.trim(),A(!1))},z=d=>{d.key==="Enter"?v():d.key==="Escape"&&(S((l==null?void 0:l.name)||""),A(!1))},oe=async d=>{try{J(!0);const y=await C.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{exceptions:d},"POST");return y&&!y.error&&y.total_affected>0?(g(y.total_affected),U(y.affected_reservations||[]),I(d),N(!0),!0):!1}catch(y){return console.error("Error checking for affected events:",y),!1}finally{J(!1)}},me=async()=>{try{await T({exceptions:Z,delete_affected_events:!0}),N(!1),I(null),r(!1),n([])}catch(d){console.error("Error saving changes:",d)}},he=()=>{N(!1),I(null),g(0),U([])},ae=e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",onClick:i,disabled:X===0,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5",stroke:X===0?"#D1D5DB":"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",onClick:b,disabled:X===(w==null?void 0:w.length)-1,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5",stroke:X===(w==null?void 0:w.length)-1?"#D1D5DB":"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsx("div",{className:"flex items-center gap-2",children:h?e.jsx("input",{ref:K,type:"text",value:$,onChange:Se,onBlur:v,onKeyDown:z,className:"w-48 rounded-lg border border-primaryBlue bg-white px-3 py-1 text-xl outline-none",placeholder:"Enter exception name"}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-xl",children:$}),e.jsx("button",{onClick:()=>A(!0),className:"rounded-lg p-1 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.7167 7.51667L12.4833 8.28333L4.93333 15.8333H4.16667V15.0667L11.7167 7.51667ZM14.7167 2.5C14.5083 2.5 14.2917 2.58333 14.1333 2.74167L12.6083 4.26667L15.7333 7.39167L17.2583 5.86667C17.5833 5.54167 17.5833 5.01667 17.2583 4.69167L15.3083 2.74167C15.1417 2.575 14.9333 2.5 14.7167 2.5ZM11.7167 5.15833L2.5 14.375V17.5H5.625L14.8417 8.28333L11.7167 5.15833Z",fill:"#868C98"})})})]})})]});s.useEffect(()=>{var d;if(l){const y=((d=l.days)==null?void 0:d.flatMap(q=>q.timeslots.map(D=>({day:q.day.charAt(0).toUpperCase()+q.day.slice(1),time:D}))))||[];n(y)}},[l]);const pe=(d,y)=>{if(!V||!Y)return!1;const[q,D]=V.split("-"),[re,ie]=Y.split("-"),je=t.indexOf(y),ke=t.indexOf(q),fe=t.indexOf(re),Ne=Math.min(ke,fe),be=Math.max(ke,fe);if(je<Ne||je>be)return!1;const Me=m.indexOf(d),Ce=m.indexOf(D),ee=m.indexOf(ie),ce=Math.min(Ce,ee),le=Math.max(Ce,ee);return Me>=ce&&Me<=le},_e=(d,y)=>{x.current&&clearTimeout(x.current),W(`${y}-${d}`),f(`${y}-${d}`),x.current=setTimeout(()=>{p(!0)},150)},ue=(d,y)=>{k&&f(`${y}-${d}`)},Ie=()=>{if(x.current&&(clearTimeout(x.current),x.current=null),k){const[d,y]=V.split("-"),[q,D]=Y.split("-"),re=t.indexOf(d),ie=t.indexOf(q),je=Math.min(re,ie),ke=Math.max(re,ie),fe=m.indexOf(y),Ne=m.indexOf(D),be=Math.min(fe,Ne),Me=Math.max(fe,Ne),Ce=[];for(let ee=je;ee<=ke;ee++){const ce=t[ee];for(let le=be;le<=Me;le++){const Ee=m[le];u.some(Te=>Te.time===Ee&&Te.day===ce)||Ce.push({time:Ee,day:ce})}}n(ee=>[...ee,...Ce]),p(!1),W(null),f(null)}};return s.useEffect(()=>{const d=()=>{k&&Ie()};return window.addEventListener("mouseup",d),()=>window.removeEventListener("mouseup",d)},[k,V,Y]),e.jsx(Re,{isOpen:o,onClose:()=>r(!1),title:"Exception times",onDiscard:()=>{n([]),r(!1)},discardLabel:"Discard",showActions:!0,saveLabel:"Save changes",leftElement:ae,onSave:async()=>{const d=Q(),y=w.map(D=>D.name===l.name?d:D);await oe(y)||(await T(y),r(!1),n([]))},isSubmitting:F||P,children:e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"grid grid-cols-7 gap-5",children:t.map(d=>e.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[e.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium",children:d}),e.jsx("div",{className:"space-y-2",children:m.map(y=>{const q=u==null?void 0:u.some(ie=>ie.time===y&&ie.day===d),D=k&&pe(y,d),re=`${d}-${y}`;return e.jsxs("div",{className:"relative",children:[e.jsx("button",{className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium text-gray-500 ${q?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":D?"border-primaryBlue bg-[#EBF1FF] bg-opacity-50":"border-gray-300 hover:border-gray-400"}`,onMouseDown:()=>_e(y,d),onMouseEnter:()=>ue(y,d),onClick:()=>{x.current&&(clearTimeout(x.current),x.current=null),k||O(y,d),p(!1),W(null),f(null)},children:ne(y)}),H===re&&e.jsx("div",{className:"absolute right-0 z-10 mt-1 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsx("div",{className:"py-1",children:e.jsxs("button",{onClick:()=>M(y,d),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})})})]},`${d}-${y}`)})})]},d))}),e.jsx(He,{isOpen:c,onClose:he,onConfirm:me,eventCount:j,affectedReservations:L,isSubmitting:F,type:"exception"})]})})}const _t=({exceptions:o,setExceptions:r,selectedException:l,setSelectedException:n,showExceptionTimeSelector:u,setShowExceptionTimeSelector:w,selectedExceptionTimes:_,setSelectedExceptionTimes:T,showDeleteExceptionModal:F,setShowDeleteExceptionModal:C,selectedExceptionIndex:H,setSelectedExceptionIndex:G,deletingException:k,deleteException:p,setShowExceptionEditModal:V,showHowItWorksModal:W,setShowHowItWorksModal:Y,timesSelectorIsSubmitting:f,setTimesSelectorIsSubmitting:x,userRole:h,profileSettings:A,globalDispatch:$,handleSearchException:S})=>{const K=new ve;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-8 max-w-4xl",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Exceptions"}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsx("button",{className:"rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",onClick:()=>{n(null),V(!0)},children:"+ Add new"})})]}),e.jsxs("div",{className:"mb-6 flex justify-between",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"search",onChange:c=>S(c.target.value),className:"w-full rounded-md border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-600",onClick:()=>Y(!0),children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.16732 7.33398H8.00065L8.00065 10.834M14.1673 8.00065C14.1673 11.4064 11.4064 14.1673 8.00065 14.1673C4.5949 14.1673 1.83398 11.4064 1.83398 8.00065C1.83398 4.5949 4.5949 1.83398 8.00065 1.83398C11.4064 1.83398 14.1673 4.5949 14.1673 8.00065Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.54102 5.33333C7.54102 5.58646 7.74622 5.79167 7.99935 5.79167C8.25248 5.79167 8.45768 5.58646 8.45768 5.33333C8.45768 5.0802 8.25248 4.875 7.99935 4.875C7.74622 4.875 7.54102 5.0802 7.54102 5.33333Z",fill:"#868C98",stroke:"#868C98",strokeWidth:"0.25"})]}),"How it works"]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-2 shadow",children:[e.jsx("div",{className:"px-6 py-3",children:e.jsxs("div",{className:"grid grid-cols-2",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Exception name"}),e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Days applicable"})]})}),e.jsx("ul",{className:"pb-5",children:Array.isArray(o)&&o.length>0?o.map((c,N)=>{var j;return e.jsx("li",{className:"mx-2 my-3 rounded-xl bg-gray-100 px-6 py-3",children:e.jsxs("div",{className:"grid grid-cols-2 items-center",children:[e.jsx("span",{className:"text-sm text-gray-900",children:c.name}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:(j=c.days)==null?void 0:j.map(g=>g.day.slice(0,3).toUpperCase()).join(", ")}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"text-gray-400 hover:text-gray-500",onClick:()=>{var L;n(c);const g=((L=c.days)==null?void 0:L.flatMap(U=>U.timeslots.map(P=>({day:U.day.charAt(0).toUpperCase()+U.day.slice(1),time:P}))))||[];T(g),w(!0)},children:e.jsx(pt,{className:"h-5 w-5"})}),e.jsx("button",{onClick:()=>{G(N),C(!0)},className:"text-gray-400 hover:text-gray-500",children:e.jsx(gt,{className:"h-5 w-5"})})]})]})]})},N)}):e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No exceptions found"})})]})]}),e.jsx(St,{showExceptionTimeSelector:u,setShowExceptionTimeSelector:w,selectedException:l,setSelectedExceptionTimes:T,selectedExceptionTimes:_,exceptions:o,onSelectException:c=>{var j;n(c);const N=((j=c.days)==null?void 0:j.flatMap(g=>g.timeslots.map(L=>({day:g.day.charAt(0).toUpperCase()+g.day.slice(1),time:L}))))||[];T(N)},isSubmitting:f,onSave:async c=>{var N;x(!0);try{let j,g;Array.isArray(c)?(j={exceptions:c},g=c):(j=c,g=c.exceptions),h==="club"?await K.callRawAPI(`/v3/api/custom/courtmatchup/${h}/profile-edit`,j,"POST"):await K.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${(N=A==null?void 0:A.user)==null?void 0:N.id}`,j,"POST"),r(g),R($,"Times updated successfully",3e3,"success")}catch(j){console.error(j),R($,"Error updating times",3e3,"error")}finally{x(!1)}}}),e.jsx(tt,{onClose:()=>C(!1),isOpen:F,onDelete:()=>p(H),message:"Are you sure you want to delete this exception?",loading:k})]})},Ke=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Fe=(o,r)=>{if(!r||r.length===0)return!0;const[l,n]=o.split(":"),u=parseInt(l)*60+parseInt(n);return r.some(w=>{const[_,T]=w.from.split(":"),[F,C]=w.until.split(":"),H=parseInt(_)*60+parseInt(T),G=parseInt(F)*60+parseInt(C);return u>=H&&u<G})},$e=(o,r)=>!r||r.length===0?!1:r.includes(o),qe=(o,r)=>{if(!r||r.length===0)return[];const l=o.toLowerCase();return r.reduce((n,u)=>{const w=u.days.find(_=>_.day===l);return w&&n.push({name:u.name,timeslots:w.timeslots}),n},[])},Xe=(o,r)=>{if(!r||r.length===0)return!1;const l=o.includes(":00",5)?o:`${o}:00`;return r.some(n=>n.timeslots.includes(l))},Qe=(o,r)=>{if(!r||r.length===0)return null;const l=o.includes(":00",5)?o:`${o}:00`,n=r.find(u=>u.timeslots.includes(l));return n?n.name:null},kt=({isOpen:o,onClose:r,court:l,club:n,globalDispatch:u,onSave:w,edit_api:_})=>{const[T,F]=s.useState([]),[C,H]=s.useState(null),[G,k]=s.useState(!1),[p,V]=s.useState({times:[],daysOff:[],exceptions:[]}),W=new ve;s.useEffect(()=>{o&&l&&Y()},[o,l]),s.useEffect(()=>{if(n){const c=n.times?JSON.parse(n.times):[],N=n.days_off?JSON.parse(n.days_off):[],j=n.exceptions?JSON.parse(n.exceptions):[];V({times:c,daysOff:N,exceptions:j})}},[n]);const Y=()=>{try{let c=[];l.availability&&(c=JSON.parse(l.availability));const N=Ke.map(j=>({day:j.toLowerCase(),timeslots:[]}));c&&Array.isArray(c)&&c.length>0&&c.forEach(j=>{const g=N.findIndex(L=>L.day===j.day.toLowerCase());g!==-1&&(N[g].timeslots=j.timeslots)}),F(N),H(c||[])}catch(c){console.error("Error parsing court availability:",c),R(u,"Error loading court availability",3e3,"error")}},f=()=>{if(!T||!C)return!1;const c=x(),N=Array.isArray(C)?C.filter(g=>g.timeslots&&g.timeslots.length>0):[];if(c.length===0&&N.length>0||c.length>0&&N.length===0||c.length!==N.length)return!0;const j={};N.forEach(g=>{j[g.day.toLowerCase()]=[...g.timeslots].sort()});for(const g of c){const L=g.day.toLowerCase(),U=j[L];if(!U||g.timeslots.length!==U.length)return!0;const P=[...g.timeslots].sort();for(let J=0;J<P.length;J++){const Z=P[J].replace(/:00$/,""),I=U[J].replace(/:00$/,"");if(Z!==I)return!0}}return!1},x=()=>T?T.filter(c=>c.timeslots.length>0):[],h=(c,N)=>{if($e(N,p.daysOff)){R(u,`${N} is a club day off`,3e3,"error");return}if(!Fe(c,p.times)){R(u,"This time is outside club hours",3e3,"error");return}const j=qe(N,p.exceptions);if(Xe(c,j)){const g=Qe(c,j);R(u,`This time is marked as "${g||"Exception"}"`,3e3,"warning")}F(g=>{const L=g.map(P=>{if(P.day===N.toLowerCase()){const J=c.replace(":00","");if(!P.timeslots.some(I=>I===c||I===J))return{...P,timeslots:[...P.timeslots,c].sort()}}return P});return L.some(P=>P.day===N.toLowerCase())||L.push({day:N.toLowerCase(),timeslots:[c]}),L})},A=(c,N)=>{F(j=>j.map(g=>{if(g.day===N.toLowerCase()){const L=g.timeslots.filter(U=>U!==c&&U!==c.replace(":00",""));return L.length===0?null:{...g,timeslots:L}}return g}).filter(Boolean))},$=(c,N)=>{const j=T==null?void 0:T.find(L=>L.day===N.toLowerCase());if(!j)return!1;const g=c.replace(":00","");return j.timeslots.some(L=>L===c||L.replace(":00","")===g)},S=async()=>{try{k(!0);const c=x();await W.callRawAPI(_,{courts:[{court_id:l.id,availability:c}]},"POST"),R(u,"Court availability updated successfully",3e3,"success"),H(c),w&&w(),r()}catch(c){console.error("Error saving court availability:",c),R(u,"Failed to update court availability",3e3,"error")}finally{k(!1)}},K=()=>{Y(),r()};return l?e.jsx(Re,{isOpen:o,onClose:r,title:`Court Availability - ${l.name}`,onDiscard:K,discardLabel:"Discard",showActions:f(),saveLabel:"Save changes",onSave:S,isSubmitting:G,children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Set the available time slots for this court. Users will only be able to book during these times."})}),e.jsx("div",{className:"overflow-x-auto pb-4",children:e.jsx(yt,{days:Ke,isSelected:$,handleTimeSelect:h,handleDeleteTime:A,renderTimeSlotContent:(c,N)=>{if($e(N,p.daysOff))return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});if(!Fe(c.value,p.times))return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});const j=qe(N,p.exceptions);if(Xe(c.value,j)){const g=Qe(c.value,j);return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500",children:e.jsx("span",{className:"truncate",children:g||"Exception"})})}return null},disableTimeSlot:(c,N)=>!!($e(N,p.daysOff)||!Fe(c.value,p.times))})})]})}):null},Mt=({club:o,courts:r,exceptions:l,setShowEditModal:n,searchQuery:u,setSearchQuery:w,filteredCourts:_,activeDropdown:T,setActiveDropdown:F,dropdownPosition:C,setDropdownPosition:H,setSelectedCourtForEdit:G,setShowEditCourtModal:k,setSelectedCourtForDelete:p,setShowDeleteCourtModal:V,setShowAddCourtModal:W,sports:Y,globalDispatch:f,edit_api:x,fetchSettings:h})=>{var i,b;const[A,$]=s.useState((o==null?void 0:o.allow_user_court_selection)===1),[S,K]=s.useState(!1),[c,N]=s.useState([]),[j,g]=s.useState(!1),[L,U]=s.useState(!1),[P,J]=s.useState(null),Z=new ve,I=localStorage.getItem("user");s.useEffect(()=>{if(r&&r.length>0){const t=r.map(a=>({id:a.id,name:a.name,sport_id:a.sport_id,type:a.type||"",sub_type:a.sub_type||"",allow_reservation:a.allow_reservation!==!1,allow_lesson:a.allow_lesson!==!1,allow_clinic:a.allow_clinic!==!1,allow_buddy:a.allow_buddy!==!1}));$((o==null?void 0:o.allow_user_court_selection)===1),N(t)}},[r]);const X=async()=>{const t=!A;K(!0);try{await Z.callRawAPI(x,{allow_user_court_selection:t?1:0},"POST"),await xe(Z,{user_id:I,activity_type:de.court_management,action_type:de.UPDATE,data:{allow_user_court_selection:t?1:0},club_id:o==null?void 0:o.id,description:`${t?"Enabled":"Disabled"} user court selection`}),$(t),R(f,`User court selection ${t?"enabled":"disabled"} successfully`,3e3,"success"),h&&h()}catch(a){console.error("Error updating user court selection setting:",a),R(f,"Error updating setting",3e3,"error")}finally{K(!1)}},te=async()=>{g(!0);try{const t=c.map(a=>({id:a.id,allow_reservation:a.allow_reservation,allow_lesson:a.allow_lesson,allow_clinic:a.allow_clinic,allow_buddy:a.allow_buddy}));await Z.callRawAPI(x,{court_settings:t},"POST"),await xe(Z,{user_id:I,activity_type:de.court_management,action_type:de.UPDATE,data:t,club_id:o==null?void 0:o.id,description:"Updated court usage settings"}),R(f,"Court settings saved successfully",3e3,"success"),h&&h()}catch(t){console.error("Error saving court settings:",t),R(f,"Error saving court settings",3e3,"error")}finally{g(!1)}};return e.jsxs("div",{className:"mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between rounded-lg bg-[#F6F8FA] px-6 py-3",children:[e.jsx("h2",{className:"text-lg font-medium",children:"Club settings"}),e.jsx("button",{className:"text-sm font-medium text-gray-600",onClick:()=>n(!0),children:"Edit"})]}),e.jsxs("div",{className:"flex flex-col divide-y divide-gray-200 p-6",children:[e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"GENERAL OPENING HOURS"}),e.jsx("p",{className:"mt-1",children:o!=null&&o.times&&JSON.parse(o==null?void 0:o.times).length>0?(i=JSON.parse(o==null?void 0:o.times))==null?void 0:i.map(t=>e.jsxs("div",{children:[Ze(t.from)," -"," ",Ze(t.until)]},t.from)):"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DAYS OFF"}),e.jsx("p",{className:"mt-1",children:o!=null&&o.days_off?(b=JSON.parse(o==null?void 0:o.days_off))==null?void 0:b.join(", "):"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"TOTAL COURTS"}),e.jsx("p",{className:"mt-1",children:r!=null&&r.length?r==null?void 0:r.length:"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"SCHEDULED EXCEPTIONS"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("p",{className:"mt-1",children:l==null?void 0:l.length})})]}),e.jsxs("div",{className:"py-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"ALLOW USERS TO SELECT COURT"}),e.jsx("button",{onClick:X,disabled:S,className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${A?"bg-primaryBlue":"bg-gray-200"} ${S?"cursor-not-allowed opacity-50":""}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${A?"translate-x-6":"translate-x-1"}`})})]}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"When enabled, users can select their preferred court during reservation."})]})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-6 shadow",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-medium",children:"Courts"}),e.jsx("button",{className:"rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:bg-gray-400",onClick:te,disabled:j,children:j?"Saving...":"Save Court Settings"})]}),e.jsx("div",{className:"mb-4 flex gap-4",children:e.jsxs("div",{className:"relative w-96",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"Search by name, sport, type, sub-type...",className:"w-full rounded-lg border border-gray-300 px-4 py-2 pl-10 text-sm focus:border-blue-500 focus:outline-none",value:u,onChange:t=>w(t.target.value)})]})}),e.jsx("div",{className:"overflow-x-auto overflow-y-hidden",children:e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"px-4 pb-4",children:"Name"}),e.jsx("th",{className:"px-4 pb-4",children:"Sport"}),e.jsx("th",{className:"px-4 pb-4",children:"Type"}),e.jsx("th",{className:"px-4 pb-4",children:"Sub-type"}),e.jsx("th",{className:"px-4 pb-4"})]})}),(_==null?void 0:_.length)>0?_==null?void 0:_.map(t=>{var a;return e.jsx("tbody",{children:e.jsxs("tr",{className:"overflow-hidden",children:[e.jsx("td",{className:"rounded-l-xl bg-gray-100 px-4 py-3",children:t.name?t.name:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.sport_id?(a=Y.find(m=>m.id==t.sport_id))==null?void 0:a.name:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.type||"--"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.sub_type||"--"}),e.jsx("td",{className:"rounded-r-xl bg-gray-100 px-4 py-3",children:e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:m=>{const O=m.currentTarget.getBoundingClientRect(),M=m.currentTarget.closest("table").getBoundingClientRect(),Q=O.bottom>M.bottom-100;H(Q?"top":"bottom"),F(T===t.id?null:t.id)},className:"text-gray-400 hover:text-gray-500",children:e.jsx(jt,{className:"h-5 w-5"})}),T===t.id&&e.jsx("div",{className:`absolute right-0 z-10 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 ${C==="top"?"bottom-full mb-1":"top-full mt-1"}`,children:e.jsxs("div",{className:"py-1",role:"menu",children:[e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{G(t),k(!0),F(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.041 5.20756L13.5768 2.67181C13.9022 2.34638 14.4298 2.34637 14.7553 2.67181L17.3268 5.2433C17.6522 5.56874 17.6522 6.09638 17.3268 6.42181L14.791 8.95756M11.041 5.20756L2.53509 13.7135C2.37881 13.8698 2.29102 14.0817 2.29102 14.3027V17.7076H5.69584C5.91685 17.7076 6.12881 17.6198 6.28509 17.4635L14.791 8.95756M11.041 5.20756L14.791 8.95756",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Edit"]}),e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{J(t),U(!0),F(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.66667 1.66667V4.16667M13.3333 1.66667V4.16667M2.5 6.66667H17.5M4.16667 3.33333H15.8333C16.7538 3.33333 17.5 4.07953 17.5 5V16.6667C17.5 17.5871 16.7538 18.3333 15.8333 18.3333H4.16667C3.24619 18.3333 2.5 17.5871 2.5 16.6667V5C2.5 4.07953 3.24619 3.33333 4.16667 3.33333Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Availability"]}),e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{p(t.id),V(!0),F(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Delete"]})]})})]})})]})},t.id)}):e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No courts found"})]})}),e.jsx("button",{className:"mt-6 rounded-md px-4 py-2 text-sm font-medium text-black underline",onClick:()=>W(!0),children:"+ Add court"})]}),e.jsx(kt,{isOpen:L,onClose:()=>{U(!1),J(null)},court:P,club:o,globalDispatch:f,edit_api:x,onSave:()=>{h&&h()}})]})},Et=({courts:o=[],sports:r=[],edit_api:l,globalDispatch:n,fetchSettings:u,club:w})=>{const[_]=s.useState(!1),[T,F]=s.useState([]),[C,H]=s.useState(!1),[G,k]=s.useState(!1),p=new ve,V=localStorage.getItem("user");s.useEffect(()=>{if(o&&o.length>0){const f=o.map(x=>{const h=x.court_settings||{};return{id:x.id,name:x.name,sport_id:x.sport_id,type:x.type||"",sub_type:x.sub_type||"",min_booking_time:h.min_booking_time||30,allow_reservation:h.allow_reservation!==!1,allow_lesson:h.allow_lesson!==!1,allow_clinic:h.allow_clinic!==!1,allow_buddy:h.allow_buddy!==!1,court_settings:h}});F(f)}},[o]);const W=(f,x,h)=>{F(A=>A.map($=>$.id===f?{...$,[x]:h}:$)),k(!0)},Y=async()=>{H(!0);try{const f=T.map(x=>({court_id:x.id,court_settings:{min_booking_time:x.min_booking_time,allow_reservation:x.allow_reservation,allow_lesson:x.allow_lesson,allow_clinic:x.allow_clinic,allow_buddy:x.allow_buddy}}));await p.callRawAPI(l,{courts:f},"POST"),await xe(p,{user_id:V,activity_type:ye.court_management,action_type:de.UPDATE,data:f,club_id:w==null?void 0:w.id,description:"Updated court settings"}),R(n,"Court settings saved successfully",3e3,"success"),k(!1),u&&u()}catch(f){console.error("Error saving court settings:",f),R(n,"Error saving court settings",3e3,"error")}finally{H(!1)}};return e.jsxs("div",{className:"mt-8",children:[_&&e.jsx(Be,{}),e.jsx("div",{className:"mb-6 flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Court Settings"})}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow",children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:'Configure minimum booking time and allowed activities for each court. Make your changes and click "Save Changes" to apply them.'})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Court Name"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Sport"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Min. Booking Time"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Reservation"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Lesson"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Clinic"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Find-a-Buddy"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:T.length>0?T.map(f=>{var x;return e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:f.name})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"text-sm text-gray-500",children:((x=r.find(h=>h.id===f.sport_id))==null?void 0:x.name)||"N/A"})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"number",min:"30",max:"1440",step:"30",value:f.min_booking_time,onChange:h=>{const A=parseInt(h.target.value,10);isNaN(A)||A<30||A>1440||W(f.id,"min_booking_time",A)},className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"minutes"})]})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:f.allow_reservation,onChange:h=>{W(f.id,"allow_reservation",h)},className:`${f.allow_reservation?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${f.allow_reservation?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:f.allow_lesson,onChange:h=>{W(f.id,"allow_lesson",h)},className:`${f.allow_lesson?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${f.allow_lesson?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:f.allow_clinic,onChange:h=>{W(f.id,"allow_clinic",h)},className:`${f.allow_clinic?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${f.allow_clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:f.allow_buddy,onChange:h=>{W(f.id,"allow_buddy",h)},className:`${f.allow_buddy?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${f.allow_buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})})]},f.id)}):e.jsx("tr",{children:e.jsx("td",{colSpan:"7",className:"px-6 py-4 text-center text-sm text-gray-500",children:"No courts found. Please add courts in the General Settings tab."})})})]})}),T.length>0&&e.jsxs("div",{className:"mt-6 flex items-center justify-between",children:[e.jsx("p",{className:"text-xs text-gray-500",children:"* Minimum booking time can be set between 30 minutes and 24 hours (1440 minutes)."}),e.jsx("button",{onClick:Y,disabled:C,className:"inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:C?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"-ml-1 mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):"Save Changes"})]})]})]})},se=new ve;function Ht({profileSettings:o,fetchSettings:r,sports:l=[],club:n,courts:u,edit_api:w}){const[_,T]=s.useState("general-settings"),[F,C]=s.useState(null),[H,G]=s.useState("bottom"),[k,p]=s.useState(!1),[V,W]=s.useState(!1),[Y,f]=s.useState(!1),[x,h]=s.useState(null),[A,$]=s.useState(!1),{dispatch:S}=we.useContext(Pe),[K,c]=s.useState(!1),[N,j]=s.useState(!1),[g,L]=s.useState(!1),[U,P]=s.useState(null),[J,Z]=s.useState([]),[I,X]=s.useState(""),[te,i]=s.useState(!1),[b,t]=s.useState(!1),[a,m]=s.useState(null),[O,M]=s.useState(!1),[Q,ne]=s.useState(!1),[Se,v]=s.useState([]),[z,oe]=s.useState(!1),[me,he]=s.useState(""),[ae,pe]=s.useState(!1),[_e,ue]=s.useState(null),[Ie,d]=s.useState(!1),[y,q]=s.useState(!1),[D,re]=s.useState(null),[ie,je]=s.useState(!1),[ke,fe]=s.useState(!1),[Ne,be]=s.useState(null),[Me]=s.useState(!1),Ce=localStorage.getItem("role"),[ee,ce]=s.useState(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[]),le=localStorage.getItem("user");console.log("courts",u),s.useEffect(()=>{const E=B=>{F&&!B.target.closest(".relative")&&C(null)};return document.addEventListener("mousedown",E),()=>{document.removeEventListener("mousedown",E)}},[F]),s.useEffect(()=>{ce(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[])},[n==null?void 0:n.exceptions]),we.useEffect(()=>{S({type:"SETPATH",payload:{path:"court-management"}})},[]);const Ee=async E=>{M(!0);const B=ee.filter((ge,Ae)=>Ae!==E);try{await se.callRawAPI(w,{exceptions:B},"POST"),await xe(se,{user_id:le,activity_type:ye.court_management,action_type:de.DELETE,data:B,club_id:n==null?void 0:n.id,description:"Deleted court exception"}),ce(B),t(!1),M(!1),R(S,"Exception deleted successfully")}catch{M(!1)}},Te=E=>{if(E===""){ce(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[]);return}const B=ee.filter(ge=>ge.name.toLowerCase().includes(E.toLowerCase()));ce(B)},nt=async()=>{je(!0);try{se.setTable("club_court"),await se.callRestAPI({id:Ne},"DELETE"),await xe(se,{user_id:le,activity_type:ye.court_management,action_type:de.DELETE,data:Ne,club_id:n==null?void 0:n.id,description:"Deleted court"})}catch(E){console.log(E)}finally{je(!1),be(null),fe(!1),r()}};console.log(ee);const at=()=>{switch(_){case"exceptions":return e.jsx(_t,{exceptions:ee,setExceptions:ce,selectedException:x,setSelectedException:h,showExceptionTimeSelector:Q,setShowExceptionTimeSelector:ne,selectedExceptionTimes:Se,setSelectedExceptionTimes:v,showDeleteExceptionModal:b,setShowDeleteExceptionModal:t,selectedExceptionIndex:a,setSelectedExceptionIndex:m,deletingException:O,deleteException:Ee,setShowExceptionEditModal:f,showHowItWorksModal:k,setShowHowItWorksModal:p,timesSelectorIsSubmitting:z,setTimesSelectorIsSubmitting:oe,userRole:Ce,profileSettings:o,globalDispatch:S,handleSearchException:Te});case"general-settings":return e.jsx(Mt,{club:n,courts:u,exceptions:ee,setShowEditModal:W,searchQuery:me,setSearchQuery:he,filteredCourts:rt,activeDropdown:F,setActiveDropdown:C,dropdownPosition:H,setDropdownPosition:G,setSelectedCourtForEdit:ue,setShowEditCourtModal:pe,setSelectedCourtForDelete:be,setShowDeleteCourtModal:fe,setShowAddCourtModal:$,sports:l,globalDispatch:S,edit_api:w,fetchSettings:r});case"court-settings":return e.jsx(Et,{courts:u,sports:l,edit_api:w,globalDispatch:S,fetchSettings:r,club:n});default:return null}},rt=u==null?void 0:u.filter(E=>{var ge,Ae,Ue,Ve,Je;const B=me.toLowerCase();return((ge=E.name)==null?void 0:ge.toLowerCase().includes(B))||((Ue=(Ae=l.find(mt=>mt.id==E.sport_id))==null?void 0:Ae.name)==null?void 0:Ue.toLowerCase().includes(B))||((Ve=E.type)==null?void 0:Ve.toLowerCase().includes(B))||((Je=E.sub_type)==null?void 0:Je.toLowerCase().includes(B))}),lt=()=>k?e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-lg rounded-2xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-4",children:e.jsx("h2",{className:"text-lg font-semibold",children:"How it works"})}),e.jsx("div",{className:"mb-6 space-y-4 text-sm text-gray-600",children:e.jsxs("p",{children:[e.jsxs("ol",{className:"list-inside list-decimal space-y-2",children:[e.jsx("li",{children:"You can define which hours the exceptions take place here and the exception will automatically be added everywhere in the schedule where you defined the hours for it."}),e.jsx("li",{children:"When adding an event in the daily scheduler, the club can specify these schedule exception names and define the date/time/repeating details."})]}),e.jsx("p",{className:"mt-2",children:'Separately, you can also create custom event types in the daily scheduler by using the event type "other" and defining the name of the event.'})]})})]}),e.jsx("div",{className:"flex justify-end border-t border-gray-200 p-6",children:e.jsx("button",{onClick:()=>p(!1),className:"rounded-xl bg-[#2B5F2B] px-5 py-3 text-sm text-white",children:"Close"})})]})}):null,Oe=we.useRef(null),ot=async()=>{Oe.current&&await Oe.current.submit()},it=async E=>{try{c(!0),await se.callRawAPI(w,E,"POST"),await xe(se,{user_id:le,activity_type:ye.court_management,action_type:de.UPDATE,data:E,club_id:n==null?void 0:n.id,description:"Updated club settings"}),W(!1),R(S,"Settings saved successfully",3e3,"success"),r()}catch(B){console.error("Error saving settings:",B),R(S,"Error saving settings",3e3,"error")}finally{c(!1)}},We=async()=>{if(i(!0),!I.length){R(S,"Please enter a name",3e3,"error");return}const E={name:I,days:[]},B=[...ee,E];try{await se.callRawAPI(w,{exceptions:B},"POST"),await xe(se,{user_id:le,activity_type:ye.court_management,action_type:de.CREATE,data:E,club_id:n==null?void 0:n.id,description:"Added new court exception"}),ce(B),R(S,"Exception added successfully",3e3,"success"),f(!1),h(E),ne(!0),X("")}catch(ge){console.error(ge),R(S,"Error adding exception",3e3,"error")}finally{i(!1)}},ct=async E=>{j(!0);try{const B={courts:[E]};await se.callRawAPI(w,B,"POST"),await xe(se,{user_id:le,activity_type:ye.court_management,action_type:de.CREATE,data:E,club_id:n==null?void 0:n.id,description:"Added new court"}),$(!1),R(S,"Court added successfully",3e3,"success"),r()}catch(B){console.error(B)}finally{j(!1)}},dt=async E=>{d(!0);try{await se.callRawAPI(w,{courts:[E]},"POST"),await xe(se,{user_id:le,activity_type:ye.court_management,action_type:de.UPDATE,data:E,club_id:n==null?void 0:n.id,description:"Updated court"}),R(S,"Court updated successfully",3e3,"success"),pe(!1),r()}catch(B){console.error(B),R(S,"Error updating court",3e3,"error")}finally{d(!1)}};return e.jsxs("div",{className:"",children:[Me&&e.jsx(Be,{}),e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200",children:[e.jsx("nav",{className:"-mb-px flex space-x-8",children:["General settings","Court settings","Exceptions"].map(E=>e.jsx("button",{className:`
                whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium
                ${_===E.toLowerCase().replace(" ","-")?"border-primaryBlue text-primaryBlue":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}
              `,onClick:()=>T(E.toLowerCase().replace(" ","-")),children:E},E))}),e.jsx(xt,{title:"Court Management History",emptyMessage:"No court management history found",activityType:ye.court_management})]}),at(),e.jsx(lt,{}),e.jsx(Nt,{ref:Oe,club:n,onSubmit:it,isOpen:V,onClose:()=>W(!1),title:"Edit club settings",onPrimaryAction:ot,submitting:K}),e.jsx(De,{isOpen:Y,onClose:()=>{f(!1),h(null)},title:x?"Edit exception":"Add exception",onPrimaryAction:We,primaryButtonText:x?"Save changes":"Add exception",submitting:te,children:e.jsx(bt,{initialData:x,onSubmit:We,setExceptionName:X,exceptionName:I})}),e.jsx(Ge,{profileSettings:o,club:n,sports:l,courts:u,onSubmit:ct,onClose:()=>$(!1),isEdit:!1,isOpen:A,title:"Add new court",showFooter:!1}),e.jsx(Ye,{selectedCourt:U,selectedTimes:J,setSelectedTimes:Z,showTimesAvailableModal:g,setShowTimesAvailableModal:L}),e.jsx(Ge,{onSubmit:dt,isEdit:!0,initialData:_e,sports:l,courts:u,profileSettings:o,onClose:()=>{pe(!1),ue(null)},isOpen:ae,title:"Edit court",primaryButtonText:"Save changes",submitting:Ie,showFooter:!1}),e.jsx(Ye,{showTimesAvailableModal:y,setShowTimesAvailableModal:q,selectedCourt:D,selectedTimes:J,setSelectedTimes:Z,onSave:async E=>{try{const B={courts:[{court_id:D.id,availability:E}]};await se.callRawAPI(w,B,"POST"),R(S,"Times updated successfully",3e3,"success"),r()}catch(B){console.error(B),R(S,"Error updating times",3e3,"error")}}}),e.jsx(tt,{isOpen:ke,onClose:()=>{fe(!1),be(null)},title:"Delete court",loading:ie,onDelete:nt,message:"Are you sure you want to delete this court?"})]})}export{Ht as C};
