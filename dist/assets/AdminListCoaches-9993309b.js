import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as s}from"./vendor-851db8c1.js";import{w as b,M as S,e as y}from"./index-a0784e19.js";import{S as j}from"./react-select-c8303602.js";import{L as v}from"./ListCoaches-275741e1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./index.esm-9c6194ba.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./TimeslotPicker-4ff6da8c.js";import"./BottomDrawer-4cdfc0e3.js";import"./TimeSlotGrid-3140c36d.js";import"./InvitationCard-0b073c4d.js";import"./HistoryComponent-0c9f35b4.js";import"./FormattedPhoneNumber-40dd7178.js";import"./DataTable-a2248415.js";let m=new S;function w(){const[L,l]=s.useState(null),[c,r]=s.useState(!1),[n,p]=s.useState([]),[i,d]=s.useState(null),[u,f]=s.useState([]);async function h(){r(!0);try{m.setTable("clubs");const t=await m.callRestAPI({},"GETALL");p(t.list)}catch(t){console.error("Error fetching data:",t)}finally{r(!1)}}const x=async t=>{r(!0);try{d({id:t.value,name:t.label}),g(t.value)}catch(a){console.error("Error fetching data:",a)}finally{r(!1)}},g=async t=>{var a;r(!0);try{const o=await m.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${t}`,{},"GET");l(o==null?void 0:o.model),f((a=o==null?void 0:o.model)==null?void 0:a.sports)}catch(o){console.log(o)}finally{r(!1)}};return s.useEffect(()=>{h()},[]),e.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[c&&e.jsx(y,{}),e.jsxs("div",{className:"mb-4 max-w-xl",children:[e.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),e.jsx(j,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:n.map(t=>({value:t.user_id,label:t.name})),isMulti:!1,onChange:x})]}),i!=null&&i.id?e.jsx(v,{club:i,sports:u}):e.jsx("div",{className:"flex h-[calc(100vh-200px)] items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"mb-2 text-xl font-medium text-gray-900",children:"No Club Selected"}),e.jsx("p",{className:"text-gray-600",children:"Please select a club from the dropdown above to view and manage its details."})]})})]})}const xt=b(w,"coach","You don't have permission to access coach management");export{xt as default};
