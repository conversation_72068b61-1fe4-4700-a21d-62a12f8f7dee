import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as i,f as D}from"./vendor-851db8c1.js";import{u as F}from"./react-hook-form-687afde5.js";import{o as P}from"./yup-2824f222.js";import{c as R,a as o}from"./yup-54691517.js";import{G as T,A as $,d as B,M as H,b as M,t as O}from"./index-9f98cff7.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as d}from"./MkdInput-ebcf18ce.js";import"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Ce=({setSidebar:S})=>{var f,g,y,j,N,w,_,v;const{dispatch:n}=i.useContext(T),A=R({user_id:o(),club_id:o(),hourly_rate:o(),status:o(),bio:o(),sport_id:o(),availability:o(),account_details:o(),photo:o(),is_public:o()}).required(),{dispatch:I}=i.useContext($),[b,G]=i.useState({}),[x,p]=i.useState(!1),k=D(),{register:s,handleSubmit:C,setError:h,setValue:L,formState:{errors:t}}=F({resolver:P(A)});i.useState([]);const E=async a=>{let u=new H;p(!0);try{for(let c in b){let r=new FormData;r.append("file",b[c].file);let m=await u.uploadImage(r);a[c]=m.url}u.setTable("coach");const l=await u.callRestAPI({user_id:a.user_id,club_id:a.club_id,hourly_rate:a.hourly_rate,status:a.status,bio:a.bio,sport_id:a.sport_id,availability:a.availability,account_details:a.account_details,photo:a.photo,is_public:a.is_public},"POST");if(!l.error)M(n,"Added"),k("/club/coach"),S(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(l.validation){const c=Object.keys(l.validation);for(let r=0;r<c.length;r++){const m=c[r];h(m,{type:"manual",message:l.validation[m]})}}p(!1)}catch(l){p(!1),console.log("Error",l),h("user_id",{type:"manual",message:l.message}),O(I,l.message)}};return i.useEffect(()=>{n({type:"SETPATH",payload:{path:"coach"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Coach"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:C(E),children:[e.jsx(d,{type:"number",page:"add",name:"user_id",errors:t,label:"User Id",placeholder:"User Id",register:s,className:""}),e.jsx(d,{type:"number",page:"add",name:"club_id",errors:t,label:"Club Id",placeholder:"Club Id",register:s,className:""}),e.jsx(d,{page:"add",name:"hourly_rate",errors:t,label:"Hourly Rate",placeholder:"Hourly Rate",register:s,className:""}),e.jsx(d,{type:"number",page:"add",name:"status",errors:t,label:"Status",placeholder:"Status",register:s,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"bio",children:"Bio"}),e.jsx("textarea",{placeholder:"Bio",...s("bio"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(f=t.bio)!=null&&f.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=t.bio)==null?void 0:g.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"sport_id",children:"Sport Id"}),e.jsx("textarea",{placeholder:"Sport Id",...s("sport_id"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=t.sport_id)!=null&&y.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(j=t.sport_id)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"availability",children:"Availability"}),e.jsx("textarea",{placeholder:"Availability",...s("availability"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(N=t.availability)!=null&&N.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(w=t.availability)==null?void 0:w.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"account_details",children:"Account Details"}),e.jsx("textarea",{placeholder:"Account Details",...s("account_details"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(_=t.account_details)!=null&&_.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(v=t.account_details)==null?void 0:v.message})]}),e.jsx(d,{type:"text",page:"add",name:"photo",errors:t,label:"Photo",placeholder:"Photo",register:s,className:""}),e.jsx(d,{type:"number",page:"add",name:"is_public",errors:t,label:"Is Public",placeholder:"Is Public",register:s,className:""}),e.jsx(B,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{Ce as default};
