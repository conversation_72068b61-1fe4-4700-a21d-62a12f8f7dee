import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{d as N,z as M,C as O,M as I,A as Z,G as W,m as H,e as K,t as $,b as L}from"./index-9f98cff7.js";import{r as a,b as J,f as Q}from"./vendor-851db8c1.js";import{A as U}from"./AuthLayout-dbf1f583.js";import{u as R}from"./react-hook-form-687afde5.js";import{o as z}from"./yup-2824f222.js";import{c as T,a as _,d as V}from"./yup-54691517.js";import{P as X}from"./react-phone-input-2-57d1f0dd.js";/* empty css              */import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";const D=({onNext:x,setValue:u,defaultValues:t,isSubmitting:y})=>{const[S,m]=a.useState((t==null?void 0:t.phone)||""),d=T({first_name:_().required("First name is required"),last_name:_().required("Last name is required"),email:_().email("Invalid email").required("Email is required"),phone:_().required("Phone number is required")}).required(),{register:o,handleSubmit:h,formState:{errors:n},setValue:p,reset:f}=R({resolver:z(d),defaultValues:{first_name:"",last_name:"",email:"",phone:""}});a.useEffect(()=>{t&&(t.email||t.first_name||t.last_name||t.phone)&&(f({first_name:t.first_name||"",last_name:t.last_name||"",email:t.email||"",phone:t.phone||""}),m(t.phone||""))},[t,f]);const g=i=>{m(i),p("phone",i),u("phone",i)},j=async i=>{await x(i)};return e.jsxs("div",{className:"mx-auto max-w-2xl px-6",children:[e.jsxs("div",{className:"mb-8 text-center",children:[e.jsx("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Complete Your Profile"}),e.jsx("p",{className:"text-gray-600",children:"Let's start by setting up your basic profile information"})]}),e.jsxs("form",{onSubmit:h(j),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"first_name",className:"block text-sm font-medium text-gray-700",children:"First Name *"}),e.jsx("input",{...o("first_name"),type:"text",id:"first_name",className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Enter your first name"}),n.first_name&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.first_name.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"last_name",className:"block text-sm font-medium text-gray-700",children:"Last Name *"}),e.jsx("input",{...o("last_name"),type:"text",id:"last_name",className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Enter your last name"}),n.last_name&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.last_name.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address *"}),e.jsx("input",{...o("email"),type:"email",id:"email",disabled:!0,className:"mt-1 block w-full rounded-xl border border-gray-300 bg-gray-50 px-3 py-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Enter your email address"}),n.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.email.message}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Email cannot be changed during onboarding"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number *"}),e.jsx("div",{className:"mt-1",children:e.jsx(X,{country:"us",value:S,onChange:g,inputStyle:{width:"100%",height:"48px",borderRadius:"12px",border:"1px solid #d1d5db",fontSize:"16px",paddingLeft:"48px"},containerStyle:{width:"100%"},buttonStyle:{borderRadius:"12px 0 0 12px",border:"1px solid #d1d5db",borderRight:"none"}})}),n.phone&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.phone.message})]}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[e.jsx("span",{children:"Step 1 of 3"}),e.jsx("span",{children:"Profile Setup"})]}),e.jsx("div",{className:"mt-2 h-2 w-full rounded-full bg-gray-200",children:e.jsx("div",{className:"h-2 w-1/3 rounded-full bg-primaryGreen"})})]}),e.jsx("div",{className:"pt-6",children:e.jsx(N,{type:"submit",loading:y,className:"w-full rounded-xl bg-primaryGreen px-4 py-3 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:"Continue"})})]})]})},ee=({onNext:x,onBack:u,register:t,setValue:y,errors:S,defaultValues:m,isSubmitting:d})=>{const[o,h]=a.useState(!1),[n,p]=a.useState(!1),f=T({password:_().min(8,"Password must be at least 8 characters").matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number").required("Password is required"),confirm_password:_().oneOf([V("password"),null],"Passwords must match").required("Please confirm your password")}).required(),{register:g,handleSubmit:j,formState:{errors:i},watch:P}=R({resolver:z(f),defaultValues:{password:"",confirm_password:""}}),r=P("password"),w=()=>{h(!o)},B=()=>{p(!n)},E=async c=>{await x(c)},b=(c=>{if(!c)return{strength:0,label:"",color:""};let l=0;return c.length>=8&&l++,/[a-z]/.test(c)&&l++,/[A-Z]/.test(c)&&l++,/\d/.test(c)&&l++,/[^a-zA-Z\d]/.test(c)&&l++,l<=2?{strength:l,label:"Weak",color:"bg-red-500"}:l<=3?{strength:l,label:"Fair",color:"bg-yellow-500"}:l<=4?{strength:l,label:"Good",color:"bg-blue-500"}:{strength:l,label:"Strong",color:"bg-green-500"}})(r);return e.jsxs("div",{className:"mx-auto max-w-2xl px-6",children:[e.jsxs("div",{className:"mb-8 text-center",children:[e.jsx("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Set Your Password"}),e.jsx("p",{className:"text-gray-600",children:"Create a secure password for your admin staff account"})]}),e.jsxs("form",{onSubmit:j(E),className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"New Password *"}),e.jsxs("div",{className:"relative mt-1",children:[e.jsx("input",{...g("password"),type:o?"text":"password",id:"password",className:"block w-full rounded-xl border border-gray-300 px-3 py-3 pr-10 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Enter your new password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3",onClick:w,children:o?e.jsx(M,{className:"h-5 w-5 text-gray-400"}):e.jsx(O,{className:"h-5 w-5 text-gray-400"})})]}),i.password&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.password.message}),r&&e.jsxs("div",{className:"mt-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Password strength:"}),e.jsx("span",{className:`font-medium ${b.strength<=2?"text-red-600":b.strength<=3?"text-yellow-600":b.strength<=4?"text-blue-600":"text-green-600"}`,children:b.label})]}),e.jsx("div",{className:"mt-1 h-2 w-full rounded-full bg-gray-200",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${b.color}`,style:{width:`${b.strength/5*100}%`}})})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirm_password",className:"block text-sm font-medium text-gray-700",children:"Confirm Password *"}),e.jsxs("div",{className:"relative mt-1",children:[e.jsx("input",{...g("confirm_password"),type:n?"text":"password",id:"confirm_password",className:"block w-full rounded-xl border border-gray-300 px-3 py-3 pr-10 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Confirm your new password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3",onClick:B,children:n?e.jsx(M,{className:"h-5 w-5 text-gray-400"}):e.jsx(O,{className:"h-5 w-5 text-gray-400"})})]}),i.confirm_password&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.confirm_password.message})]}),e.jsxs("div",{className:"rounded-lg bg-blue-50 p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Password Requirements:"}),e.jsxs("ul",{className:"text-sm text-blue-800 space-y-1",children:[e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:`mr-2 ${r&&r.length>=8?"text-green-600":"text-gray-400"}`,children:r&&r.length>=8?"✓":"○"}),"At least 8 characters long"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:`mr-2 ${r&&/[a-z]/.test(r)?"text-green-600":"text-gray-400"}`,children:r&&/[a-z]/.test(r)?"✓":"○"}),"One lowercase letter"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:`mr-2 ${r&&/[A-Z]/.test(r)?"text-green-600":"text-gray-400"}`,children:r&&/[A-Z]/.test(r)?"✓":"○"}),"One uppercase letter"]}),e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:`mr-2 ${r&&/\d/.test(r)?"text-green-600":"text-gray-400"}`,children:r&&/\d/.test(r)?"✓":"○"}),"One number"]})]})]}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[e.jsx("span",{children:"Step 2 of 3"}),e.jsx("span",{children:"Password Setup"})]}),e.jsx("div",{className:"mt-2 h-2 w-full rounded-full bg-gray-200",children:e.jsx("div",{className:"h-2 w-2/3 rounded-full bg-primaryGreen"})})]}),e.jsxs("div",{className:"flex gap-4 pt-6",children:[e.jsx(N,{type:"button",onClick:u,className:"flex-1 rounded-xl border border-gray-300 bg-white px-4 py-3 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Back"}),e.jsx(N,{type:"submit",loading:d,className:"flex-1 rounded-xl bg-primaryGreen px-4 py-3 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:"Continue"})]})]})]})},q=new I;function se({onNext:x,onBack:u,stripeConnectionData:t,isSubmitting:y,setStripeConnectionData:S}){J.useState(y||!1);const[m,d]=a.useState(!1),[o,h]=a.useState(!1),[n,p]=a.useState(!1),f=localStorage.getItem("role"),g=async()=>{try{const r=await q.callRawAPI(`/v3/api/custom/courtmatchup/${f}/stripe/account/verify`,{},"POST");return S(r),r}catch(r){return console.error("Error checking Stripe connection:",r),!1}},j=async()=>{d(!0);try{const r=await q.callRawAPI(`/v3/api/custom/courtmatchup/${f}/stripe/onboarding`,{},"POST");r&&r.url&&window.open(r.url,"_blank")}catch(r){console.error("Error connecting to Stripe:",r)}d(!1)};a.useEffect(()=>{if(m===!1){const r=setTimeout(()=>{g()},2e3);return()=>clearTimeout(r)}},[m]);const i=async()=>{h(!0),await x(),h(!1)},P=async()=>{p(!0);try{await x({skip_payment:!0})}finally{p(!1)}};return e.jsxs("div",{className:"mx-auto max-w-2xl px-6",children:[e.jsxs("div",{className:"mb-8 text-center",children:[e.jsx("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Connect Your Bank Account"}),e.jsx("p",{className:"text-gray-600",children:"Set up your payment method to receive payments through the platform"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border border-gray-200 bg-white p-6",children:t!=null&&t.complete||t!=null&&t.details_submitted?e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100",children:e.jsx("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Stripe Account Connected"}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Your Stripe account has been successfully connected and verified."}),e.jsxs("div",{className:"mt-4 grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${t.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${t.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]}):e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Connect Stripe Account"}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Connect your Stripe account to receive payments from the platform. This is secure and handled by Stripe."}),e.jsx("button",{onClick:j,disabled:m,className:"mt-4 w-full rounded-xl bg-primaryBlue px-4 py-3 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50",children:m?"Connecting...":"Connect Stripe Account"})]})}),e.jsx("div",{className:"rounded-lg bg-blue-50 p-4",children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-800",children:"About Stripe Integration"}),e.jsx("div",{className:"mt-2 text-sm text-blue-700",children:e.jsxs("ul",{className:"list-disc space-y-1 pl-5",children:[e.jsx("li",{children:"Stripe handles all payment processing securely"}),e.jsx("li",{children:"Your banking information is never stored on our servers"}),e.jsx("li",{children:"You can manage your account directly through Stripe"}),e.jsx("li",{children:"Payments are processed according to your Stripe settings"})]})})]})]})}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[e.jsx("span",{children:"Step 3 of 3"}),e.jsx("span",{children:"Bank Setup"})]}),e.jsx("div",{className:"mt-2 h-2 w-full rounded-full bg-gray-200",children:e.jsx("div",{className:"h-2 w-full rounded-full bg-primaryGreen"})})]}),e.jsx("div",{className:"space-y-4 pt-6",children:t!=null&&t.complete||t!=null&&t.details_submitted?e.jsxs("div",{className:"flex gap-4",children:[e.jsx(N,{type:"button",onClick:u,className:"flex-1 rounded-xl border border-gray-300 bg-white px-4 py-3 text-gray-700 hover:bg-gray-50",children:"Back"}),e.jsx(N,{onClick:i,loading:o,className:"flex-1 rounded-xl bg-primaryGreen px-4 py-3 text-white hover:bg-green-700",children:"Complete Setup"})]}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex gap-4",children:[e.jsx(N,{type:"button",onClick:u,className:"flex-1 rounded-xl border border-gray-300 bg-white px-4 py-3 text-gray-700 hover:bg-gray-50",children:"Back"}),e.jsx(N,{onClick:P,loading:n,className:"flex-1 rounded-xl border border-gray-300 bg-white px-4 py-3 text-gray-700 hover:bg-gray-50",children:"Skip for Now"})]}),e.jsx("p",{className:"text-center text-xs text-gray-500",children:"You can set up payments later in your profile settings"})]})})]})]})}const k=new I;function Te(){const x=Q(),{dispatch:u}=a.useContext(Z),[t,y]=a.useState(0),[S,m]=a.useState(!1),{dispatch:d}=a.useContext(W),[o,h]=a.useState(!1),[n,p]=a.useState(!1),[f,g]=a.useState(null),[j,i]=a.useState(null),P=s=>!s||!s.first_name||!s.last_name||!s.phone?0:s.password_changed?s.stripe_connected?3:2:1,{register:r,setValue:w,formState:{errors:B},watch:E}=R({defaultValues:{first_name:"",last_name:"",email:"",phone:"",password:"",confirm_password:""}}),F=async()=>{try{const s=await k.callRawAPI("/v3/api/custom/courtmatchup/admin_staff/stripe/account/verify",{},"POST");g(s)}catch(s){return console.error("Error checking Stripe connection:",s),!1}},b=async()=>{p(!0);try{const s=await k.callRawAPI("/v3/api/custom/courtmatchup/admin-staff/profile",{},"GET");if(await F(),(s==null?void 0:s.completed)==1){x("/admin_staff/dashboard");return}return i(s),w("first_name",(s==null?void 0:s.first_name)||""),w("last_name",(s==null?void 0:s.last_name)||""),w("email",(s==null?void 0:s.email)||""),w("phone",(s==null?void 0:s.phone)||""),s}catch(s){$(u,s.code),L(d,s.message,3e3,"error")}finally{p(!1)}},c=async s=>{h(!0);try{switch(t){case 0:const v={first_name:s.first_name,last_name:s.last_name,phone:s.phone};await k.callRawAPI("/v3/api/custom/courtmatchup/admin-staff/profile-edit",v,"POST"),L(d,"Profile updated successfully",2e3,"success");break;case 1:const A=await k.updatePassword(s.password);if(A.error){if(A.validation){const Y=Object.values(A.validation).join(", ");throw new Error(Y)}throw new Error(A.message||"Failed to update password")}await k.callRawAPI("/v3/api/custom/courtmatchup/admin-staff/profile-edit",{password_changed:1},"POST"),L(d,"Password updated successfully",2e3,"success");break;case 2:const C={};s&&s.skip_payment?C.not_paid_through_platform=1:C.not_paid_through_platform=0,C.completed=1,await k.callRawAPI("/v3/api/custom/courtmatchup/admin-staff/profile-edit",C,"POST");break}await b(),t===2?m(!0):y(v=>v+1)}catch(v){console.error(v),L(d,v.message,3e3,"error"),$(u,v.code)}finally{h(!1)}},l=()=>{y(Math.max(t-1,0))};a.useEffect(()=>{(async()=>{const s=await b();s&&y(P(s))})()},[]),a.useEffect(()=>{H({title:"Admin Staff Onboarding",path:"/admin_staff/onboarding",description:"Admin Staff Onboarding"})},[]);const G=()=>{const s=E();switch(t){case 0:return e.jsx(D,{onNext:c,setValue:w,defaultValues:j||s,isSubmitting:o});case 1:return e.jsx(ee,{onNext:c,onBack:l,register:r,setValue:w,errors:B,defaultValues:j||s,isSubmitting:o});case 2:return e.jsx(se,{onNext:c,onBack:l,isSubmitting:o,stripeConnectionData:f,setStripeConnectionData:g});default:return null}};return e.jsxs(U,{children:[n&&e.jsx(K,{}),e.jsxs("div",{className:"flex flex-col bg-white pb-7",children:[e.jsx("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("div",{className:"mb-10",children:t!==0&&e.jsxs("button",{className:"mt-5 flex items-center gap-2 text-[#525866]",onClick:l,children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 4.79199L3.125 10.0003L8.33333 15.2087M3.75 10.0003H16.875",stroke:"#525866",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{children:"Back"})]})})}),e.jsx("div",{className:"",children:G()}),S&&e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-full max-w-xl rounded-2xl bg-white ",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-start gap-4 p-5",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Onboarding complete!"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Congratulations! You have successfully completed your admin staff onboarding. You can now access your admin staff portal."})]})]}),e.jsx("div",{className:"flex w-full justify-end border-t border-gray-200 p-5",children:e.jsx(N,{onClick:()=>{x("/admin_staff/dashboard")},className:"w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white",children:"Continue to Admin Staff portal!"})})]})})})]})]})}export{Te as default};
