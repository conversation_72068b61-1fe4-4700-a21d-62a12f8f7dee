import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as o}from"./vendor-851db8c1.js";import{w as g,M as S,e as y}from"./index-9f98cff7.js";import{S as j}from"./react-select-c8303602.js";import{L as v}from"./ListUsers-a90f7694.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./HistoryComponent-af37bc67.js";import"./DataTable-a2248415.js";import"./FormattedPhoneNumber-40dd7178.js";let l=new S;function w(){const[i,n]=o.useState(null),[c,r]=o.useState(!1),[p,d]=o.useState([]),[L,u]=o.useState(null),[N,f]=o.useState([]);async function x(){r(!0);try{l.setTable("clubs");const t=await l.callRestAPI({},"GETALL");d(t.list)}catch(t){console.error("Error fetching data:",t)}finally{r(!1)}}const h=async t=>{r(!0);try{u({id:t.value,name:t.label}),await b(t.value)}catch(a){console.error("Error fetching data:",a)}finally{r(!1)}},b=async t=>{var a,m;r(!0);try{const s=await l.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${t}`,{},"GET");n((a=s==null?void 0:s.model)==null?void 0:a.club),f((m=s==null?void 0:s.model)==null?void 0:m.sports)}catch(s){console.log(s)}finally{r(!1)}};return o.useEffect(()=>{x()},[]),e.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[c&&e.jsx(y,{}),e.jsxs("div",{className:"mb-4 max-w-xl",children:[e.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),e.jsx(j,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:p.map(t=>({value:t.user_id,label:t.name,club_id:t==null?void 0:t.id})),isMulti:!1,onChange:h})]}),i!=null&&i.id?e.jsx(v,{club:i,clubId:i.id}):e.jsx("div",{className:"flex h-[calc(100vh-200px)] items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"mb-2 text-xl font-medium text-gray-900",children:"No Club Selected"}),e.jsx("p",{className:"text-gray-600",children:"Please select a club from the dropdown above to view and manage its details."})]})})]})}const dt=g(w,"user","You don't have permission to access user management");export{dt as default};
