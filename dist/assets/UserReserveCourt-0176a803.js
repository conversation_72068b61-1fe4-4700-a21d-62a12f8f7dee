import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as a,f as Bt,b as ae,L as Ae}from"./vendor-851db8c1.js";import{M as $t,T as Pt,G as It,A as Rt,u as Et,m as At,b as w,$ as H,aA as Lt,aB as Q,aC as Yt,am as Ft,e as qt,v as O,aD as Ht,d as Ot,ar as Vt,H as Dt,E as Gt,J as Ut}from"./index-9f98cff7.js";import{B as Jt}from"./BackButton-11ba52b2.js";import{T as Wt}from"./TimeSlots-9a2fb5c0.js";import{A as Zt}from"./AddPlayers-864477e3.js";import{C as Kt}from"./Calendar-9031b5fe.js";import{S as Qt}from"./SportTypeSelection-ee0cc3da.js";import{C as Le}from"./ReservationSummary-ad645e94.js";import{h as V}from"./moment-a9aaa855.js";import{S as zt}from"./react-select-c8303602.js";import{g as Xt}from"./customThresholdUtils-f40b07d5.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-tooltip-7a26650a.js";import"./index.esm-09a3a6b8.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";let U=new $t,Ye=new Pt;const Zs=({})=>{var Ne,Se,ke,Ce,Te,Me,Be,$e,Pe,Ie,Re;const[g,Fe]=a.useState(null),[j,qe]=a.useState(null),[D,oe]=a.useState(new Date),[He,Oe]=a.useState([]),[Ve,De]=a.useState([]),[G,Ge]=a.useState(0),[J,Ue]=a.useState(0),[es,re]=a.useState(!1),[P,R]=a.useState("main"),[f,W]=a.useState([]),[le,Je]=a.useState(!1),[z,X]=a.useState(1),[ce,We]=a.useState(!1),[de,Ze]=a.useState(3.5),[me,Ke]=a.useState(3.5),[ue,Qe]=a.useState(""),[ze,Xe]=a.useState([]),[v,ge]=a.useState(null),[et,pe]=a.useState(!1),[p,tt]=a.useState(null),[y,st]=a.useState(null),[h,he]=a.useState([]),[ee,fe]=a.useState([]),[k,ye]=a.useState([]),[x,te]=a.useState(null),[C,nt]=a.useState(30),[it,at]=a.useState(null),[ot,rt]=a.useState(null),[lt,xe]=a.useState(!1),[ct,dt]=a.useState(null),[ve,mt]=a.useState(null),[ut,Y]=a.useState(!1),[E,B]=a.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),{state:ts,dispatch:_}=a.useContext(It);a.useContext(Rt);const{club:s,pricing:_e,sports:A,loading:ss,user_subscription:$,user_permissions:N,user_profile:b,club_membership:F,courts:Z}=Et(),gt=Bt();console.log("club",s);const we=localStorage.getItem("user"),r=ae.useMemo(()=>!($!=null&&$.planId)||!(F!=null&&F.length)?null:F.find(t=>t.plan_id===$.planId),[$,F]);console.log("userMembershipPlan",r),console.log("user_subscription",$),console.log("club_membership",F);const K=ae.useMemo(()=>{var o,i;if(((o=r==null?void 0:r.advance_booking_enabled)==null?void 0:o.court)===!1){const d=new Date;return d.setFullYear(d.getFullYear()+10),d}const t=((i=r==null?void 0:r.advance_booking_days)==null?void 0:i.court)||10,n=new Date,l=new Date;return l.setDate(n.getDate()+t),l},[r]),pt=async()=>{try{const t=await Ye.getList("user",{filter:["role,cs,user",`club_id,cs,${s==null?void 0:s.id}`]});Oe(t.list)}catch(t){console.error(t)}},ht=async()=>{try{const t=await Ye.getList("user",{filter:[`guardian,eq,${we}`,"role,cs,user"]});Xe(t.list)}catch(t){console.error("Error fetching family members:",t)}},ft=async()=>{try{const t=await U.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");De(t.groups)}catch(t){console.error(t)}};a.useEffect(()=>{(async()=>(pe(!0),await ft(),await ht(),pe(!1)))()},[we]),a.useEffect(()=>{s!=null&&s.id&&pt()},[s==null?void 0:s.id]),a.useEffect(()=>{b&&!v&&ge(b)},[b,v]),ae.useEffect(()=>{At({path:"/user/reserve-court",clubName:s==null?void 0:s.name,favicon:s==null?void 0:s.club_logo,description:"Reserve a court"})},[s==null?void 0:s.club_logo]),a.useEffect(()=>{},[P]);const yt=()=>{oe(new Date(D.setMonth(D.getMonth()-1)))},xt=()=>{oe(new Date(D.setMonth(D.getMonth()+1)))},c=A==null?void 0:A.find(t=>t.id===g),S=Xt(s==null?void 0:s.custom_request_threshold,g,p,y,4,A);a.useEffect(()=>{g&&(console.log("Selected Sport:",g),console.log("Selected Type:",p),console.log("Selected SubType:",y),console.log("Max Players Allowed:",S))},[g,p,y,S]),a.useEffect(()=>{f.length>S&&(console.log(`Clearing selected players: current ${f.length} exceeds new threshold ${S}`),W([]),X(1),w(_,`Player selection cleared. New maximum is ${S} players. Please select players again.`,4e3,"warning"))},[S]),a.useEffect(()=>{z>S-f.length&&X(Math.max(0,S-f.length))},[S,f.length]);const{start_time:se,end_time:ne,duration:ie}=H(h),vt=t=>{try{return t.court_settings?JSON.parse(t.court_settings):{min_booking_time:30,allow_reservation:!0,allow_lesson:!0,allow_clinic:!0,allow_buddy:!0}}catch(n){return console.warn(`Failed to parse court_settings for court ${t.id}:`,n),{min_booking_time:30,allow_reservation:!0,allow_lesson:!0,allow_clinic:!0,allow_buddy:!0}}},je=async(t,n,l)=>{try{const o=V(t).format("YYYY-MM-DD"),i=new URLSearchParams;return i.append("sport_id",g||""),i.append("type",p||""),i.append("subtype",y||""),i.append("date",o),i.append("court_id",""),i.append("start_time",n),i.append("end_time",l),await U.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/availability/${s==null?void 0:s.id}?${i.toString()}`,{},"GET")}catch(o){return console.error("Error getting qualifying courts:",o),{qualifying_courts:[],availability:[],min_booking_time:30}}},_t=async(t,n,l)=>{if(!t||!n||!l)return[];const o=await je(t,n,l);if(o.error)return console.error("API Error:",o.message),[];const i=o.qualifying_courts||[];return ye(i),i},wt=()=>!(!j||!g||ee.length===0),jt=a.useCallback(({sport:t,type:n,subType:l})=>{Fe(t),tt(n),st(l)},[]);a.useEffect(()=>{if(Z&&Z.length>0){let t=[...Z];g&&(t=t.filter(n=>n.sport_id&&n.sport_id.toString()===g.toString())),p&&(t=t.filter(n=>n.type===p)),y&&(t=t.filter(n=>n.sub_type===y)),t=t.filter(n=>vt(n).allow_reservation!==!1),fe(t)}else fe([])},[Z,g,p,y]);const bt=async()=>{var o,i,d;const t=(o=c==null?void 0:c.sport_types)==null?void 0:o.some(m=>m.type&&m.type.trim()!==""),n=(i=c==null?void 0:c.sport_types)==null?void 0:i.find(m=>m.type===p),l=(d=n==null?void 0:n.subtype)==null?void 0:d.some(m=>m&&m.trim()!=="");if(!g||t&&!p||t&&l&&!y||!j||!se||!ne){w(_,"Please select all required fields",3e3,"error");return}re(!0);try{const m=await U.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:J},"POST");at(m.client_secret),rt(m.payment_intent);const T=V(j).format("YYYY-MM-DD"),u={sport_id:g,type:p,sub_type:y,date:T,start_time:se,end_time:ne,duration:ie,reservation_type:Vt.court,price:J,player_ids:f.map(I=>I.id),primary_player_id:(v==null?void 0:v.id)||(b==null?void 0:b.id),buddy_details:null,payment_status:0,payment_intent:m.payment_intent,service_fee:Q(s==null?void 0:s.fee_settings,G),club_fee:s==null?void 0:s.club_fee,players_needed:z,min_ntrp:de,max_ntrp:me,note:ue};let M=null;if((s==null?void 0:s.allow_user_court_selection)===1&&x)M=x;else{const I=await _t(j,se,ne);I.length>0&&(M=I.sort((Tt,Mt)=>(Tt.min_booking_time||30)-(Mt.min_booking_time||30))[0].id)}M&&(u.court_id=M);const L=await U.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",u,"POST");return await Dt(U,{user_id:localStorage.getItem("user"),activity_type:Gt.court_reservation,action_type:Ut.CREATE,data:u,club_id:s==null?void 0:s.id,description:`${b==null?void 0:b.first_name} ${b==null?void 0:b.last_name} created a court reservation`}),L.error||w(_,"Reservation created successfully",3e3,"success"),mt(L.reservation_id),L.booking_id}catch(m){console.error(m),w(_,m.message||"Error creating reservation",3e3,"error")}finally{re(!1)}},be=t=>{W(n=>n.some(o=>o.id===t.id)?n.filter(o=>o.id!==t.id):[...n,t])},Nt=t=>{const n=t.value||t;(n==null?void 0:n.id)!==(v==null?void 0:v.id)&&(ge(n),W(l=>{const o=l.filter(d=>d.id!==(v==null?void 0:v.id));if(o.some(d=>d.id===n.id)){const d=o.filter(m=>m.id!==n.id);return[n,...d]}else return[n,...o]}))},St=async()=>{Y(!0);try{const t=V(h[0].from,"h:mm A").format("HH:mm"),n=V(h[0].until,"h:mm A").format("HH:mm"),l=await je(j,t,n);if(l.error){w(_,l.message||"No courts available that match the selected criteria",4e3,"warning"),Y(!1);return}const o=l.qualifying_courts||[],i=l.min_booking_time||30;if(o.length===0){w(_,"No courts are available for the selected time slot. Please choose a different time.",4e3,"warning"),Y(!1);return}if(ye(o),nt(i),o.length>0){const{duration:T}=H(h),u=T*60,M=o.filter(L=>{const I=L.min_booking_time||i;return u>=I});if(M.length>0){const L=M.sort((I,Ee)=>(I.min_booking_time||30)-(Ee.min_booking_time||30));te(L[0].id)}}const{duration:d}=H(h);if(d*60>=i){Y(!1),R("players");return}w(_,`Minimum booking time requirement is ${Math.floor(i/60)} hour${Math.floor(i/60)!==1?"s":""}${i%60!==0?` ${i%60} minutes`:""}. Please select a longer time slot.`,5e3,"warning"),Y(!1)}catch(t){console.error("Error checking court availability:",t),w(_,"Error checking court availability. Please try again.",3e3,"error"),Y(!1)}};a.useEffect(()=>{if(f!=null&&f.length&&g&&p&&y&&(h!=null&&h.length)&&ie){const t=Lt({pricing:_e,sportId:g,type:p,subType:y,duration:ie,selectedTime:h[0]}),n=Q(s==null?void 0:s.fee_settings,t);Ge(t),Ue(t+n)}},[f,g,p,y,h,_e,s==null?void 0:s.fee_settings]);const kt=async()=>{var o,i,d,m,T;if(!($!=null&&$.planId)){B({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to reserve courts",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(N!=null&&N.allowCourt)){B({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${N==null?void 0:N.planName}) does not include court reservations. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(j>K&&((o=r==null?void 0:r.advance_booking_enabled)!=null&&o.court)){const u=`Your membership plan only allows booking ${((i=r==null?void 0:r.advance_booking_days)==null?void 0:i.court)||10} days in advance. Please select a valid date.`;B({isOpen:!0,title:"Date Selection Error",message:u,type:"warning"}),R("main");return}const t=(d=c==null?void 0:c.sport_types)==null?void 0:d.some(u=>u.type&&u.type.trim()!==""),n=(m=c==null?void 0:c.sport_types)==null?void 0:m.find(u=>u.type===p),l=(T=n==null?void 0:n.subtype)==null?void 0:T.some(u=>u&&u.trim()!=="");if(!g||t&&!p||t&&l&&!y||!j||!h.length){B({isOpen:!0,title:"Incomplete Details",message:"Please complete all required Reservation detail",type:"warning"}),R("main");return}if((s==null?void 0:s.allow_user_court_selection)===1&&!x){B({isOpen:!0,title:"Court Selection Required",message:"Please select a court for your reservation",type:"warning"});return}if(h.length>0){const{duration:u}=H(h),M=u*60;if(M<C){B({isOpen:!0,title:"Minimum Booking Time Not Met",message:`The minimum booking time requirement is ${C} minutes. Your current selection is ${M} minutes. Please select a longer time slot.`,type:"warning"}),R("main");return}}if(!f.length){B({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}try{xe(!0);const u=await bt();if(!u)throw new Error("Failed to create reservation");dt(u),R("payment")}catch(u){console.error("Reservation error:",u),B({isOpen:!0,title:"Reservation Error",message:u.message||"Error creating reservation",type:"error"})}finally{xe(!1)}},Ct=t=>{const n=V(t.from,"h:mm A"),o=V(t.until,"h:mm A").diff(n,"minutes");if(x&&k.length>0){const i=k.find(m=>m.id===x),d=(i==null?void 0:i.min_booking_time)||C;if(o<d){w(_,`Minimum booking time for this court is ${d} minutes. Please select a longer time slot.`,4e3,"warning");return}}te(null),he([{from:t.from,until:t.until}])},q=s!=null&&s.court_description?JSON.parse(s==null?void 0:s.court_description):{reservation_description:"",payment_description:""};return N&&!N.allowCourt?e.jsx(Yt,{message:`Your current plan (${N==null?void 0:N.planName}) does not include court reservations. Please upgrade your plan to access this feature.`}):e.jsxs("div",{className:"",children:[e.jsx(Ft,{isOpen:E.isOpen,onClose:()=>B({...E,isOpen:!1}),title:E.title,message:E.message,actionButtonText:E.actionButtonText,actionButtonLink:E.actionButtonLink,type:E.type}),et&&e.jsx(qt,{}),e.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[P==="main"&&e.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),P==="players"&&e.jsx("div",{className:" ",children:"Step 2 • Reservation detail"}),P==="payment"&&e.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),e.jsxs("div",{className:"p-4",children:[e.jsx(Jt,{onBack:()=>{P==="main"?gt(-1):R(P==="payment"?"players":"main")}}),P==="main"?e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(Qt,{sports:A,userPermissions:N,initialSport:g,initialType:p,initialSubType:y,onSelectionChange:jt},`${g}-${p}-${y}`),g&&(!((Ne=c==null?void 0:c.sport_types)!=null&&Ne.length)||p!==null&&(y!==null||!((Ce=(ke=(Se=c==null?void 0:c.sport_types)==null?void 0:Se.find(t=>t.type===p))==null?void 0:ke.subtype)!=null&&Ce.length)))?e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[(Te=r==null?void 0:r.advance_booking_enabled)!=null&&Te.court?e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can reserve a court up to"," ",(Me=r==null?void 0:r.advance_booking_days)==null?void 0:Me.court," ",((Be=r==null?void 0:r.advance_booking_days)==null?void 0:Be.court)===1?"day":"days"," ","in advance."]}):e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a court for any future date."}),e.jsx(Kt,{currentMonth:D,selectedDate:j,onDateSelect:t=>{var n,l;if(t>K){const o=(n=r==null?void 0:r.advance_booking_enabled)!=null&&n.court?`Your membership plan only allows booking ${((l=r==null?void 0:r.advance_booking_days)==null?void 0:l.court)||10} days in advance`:"";if(o){w(_,o,3e3,"warning");return}}qe(t)},onPreviousMonth:yt,onNextMonth:xt,daysOff:s!=null&&s.days_off?JSON.parse(s.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:K,disabledDateMessage:($e=r==null?void 0:r.advance_booking_enabled)!=null&&$e.court?`Your membership plan only allows booking ${((Pe=r==null?void 0:r.advance_booking_days)==null?void 0:Pe.court)||10} days in advance`:"You can book for any future date"})]})}),j&&e.jsxs("div",{children:[g&&ee.length>0&&e.jsxs("div",{className:"mb-4 rounded-lg bg-blue-50 p-3",children:[e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("span",{className:"font-medium",children:"Minimum booking time:"})," ",e.jsxs("span",{className:"font-semibold text-blue-900",children:[Math.floor(C/60)," hour",Math.floor(C/60)!==1?"s":"",C%60!==0&&` ${C%60} minutes`]})]}),e.jsx("p",{className:"mt-1 text-xs text-blue-600",children:"Based on available courts for your selected sport, type, and subtype"})]}),e.jsx(Wt,{selectedDate:j,timeRange:h,onTimeClick:Ct,isLoading:ut,onNext:()=>{var t,n;if(!h.length){w(_,"Please select a time slot",3e3,"error");return}if(j>K){const l=(t=r==null?void 0:r.advance_booking_enabled)!=null&&t.court?`Your membership plan only allows booking ${((n=r==null?void 0:r.advance_booking_days)==null?void 0:n.court)||10} days in advance`:"";if(l){w(_,l,3e3,"warning");return}}St()},nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,className:"h-fit",isTimeSlotAvailable:wt,clubTimes:s!=null&&s.times?JSON.parse(s.times):[],minBookingTime:x&&k.length>0&&((Ie=k.find(t=>t.id===x))==null?void 0:Ie.min_booking_time)||C,enforceMinBookingTime:!!x})]})]}):e.jsx("div",{className:"flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5 md:col-span-2",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available time slots"})]})})]})})})}):P==="payment"?e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(Le,{selectedSport:g,sports:A,selectedType:p,selectedSubType:y,selectedDate:j,selectedTimes:h,selectedCourt:x?ee.find(t=>t.id===x):null})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:O(G)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:O(Q(s==null?void 0:s.fee_settings,G))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:O(J)})]}),e.jsxs("div",{children:[e.jsx(Ht,{user:b,bookingId:ct,reservationId:ve,clientSecret:it,paymentIntent:ot,navigateRoute:`/user/payment-success/${ve}?type=court`}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"text-sm text-gray-500",children:q==null?void 0:q.payment_description})})]}),e.jsx("div",{className:"space-y-4 text-sm text-gray-500",children:e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(Ae,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(Ae,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]})})]})]})})]})]}):e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-4",children:[" ",e.jsx(Le,{selectedSport:g,sports:A,selectedType:p,selectedSubType:y,selectedDate:j,selectedTimes:h,selectedCourt:x?k.find(t=>t.id===x):null})]}),e.jsxs("div",{className:"space-y-4",children:[(s==null?void 0:s.allow_user_court_selection)===1&&e.jsxs("div",{className:"mb-2 h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Select Court"}),x&&k.length>0&&e.jsxs("div",{className:"mb-3 rounded-lg bg-green-50 p-2 text-xs text-green-700",children:[e.jsx("span",{className:"font-medium",children:(Re=k.find(t=>t.id===x))==null?void 0:Re.name})," ","has been automatically selected as the best match for your time slot. You can change this selection below if needed."]}),e.jsx(zt,{className:"w-full text-sm",options:k.map(t=>{const n=t.min_booking_time||30,{duration:l}=h.length>0?H(h):{duration:0},o=l*60,i=o>0&&n>o;let d=`${t.name} (Min: ${n}min)`;if(i){const m=Math.floor(n/60),T=n%60,u=m>0?`${m}h${T>0?` ${T}m`:""}`:`${T}m`;d+=` - Requires ${u} minimum`}return{value:t.id,label:d,isDisabled:i}}),onChange:t=>{if(te(t?t.value:null),t&&h.length>0){const n=k.find(d=>d.id===t.value),l=(n==null?void 0:n.min_booking_time)||C,{duration:o}=H(h);o*60<l&&(he([]),w(_,`Selected court requires minimum ${l} minutes. Please select a new time slot.`,4e3,"info"))}},value:x?(()=>{const t=k.find(n=>n.id===x);if(t){const n=t.min_booking_time||30;return{value:x,label:`${t.name} (Min: ${n}min)`}}return null})():null,isClearable:!0,placeholder:"Select a court (available courts will appear after time selection)",noOptionsMessage:()=>{var n,l,o;if(!g)return"Please select a sport first";if((n=c==null?void 0:c.sport_types)!=null&&n.some(i=>i.type&&i.type.trim()!=="")&&!p)return"Please select a type";const t=(l=c==null?void 0:c.sport_types)==null?void 0:l.find(i=>i.type===p);return(o=t==null?void 0:t.subtype)!=null&&o.some(i=>i&&i.trim()!=="")&&!y?"Please select a sub-type":!j||!h.length?"Please select date and time first to see available courts":"No courts available for the selected time slot"}}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"The best available court has been automatically selected. You can change this selection if needed. Courts with higher minimum booking requirements are shown but disabled if your current time selection is too short."})]}),e.jsx(Zt,{players:He,groups:Ve,selectedPlayers:f,familyMembers:ze,currentUser:v,onCurrentUserChange:Nt,onPlayerToggle:t=>{if(f.some(l=>l.id===t.id)){if(t.id===(v==null?void 0:v.id)){w(_,"You cannot remove the primary player from the reservation",3e3,"warning");return}be(t);return}if(f.length>=S){const l=(c==null?void 0:c.name)||"this sport";w(_,`Maximum ${S} players allowed for ${l} (including yourself)`,3e3,"warning");return}be(t)},isFindBuddyEnabled:le,setSelectedPlayers:W,onFindBuddyToggle:()=>{Je(!le),We(!ce)},playersNeeded:z,onPlayersNeededChange:X,maximumPlayers:S,userProfile:b,showPlayersNeeded:ce,onNtrpMinChange:Ze,onNtrpMaxChange:Ke,onShortBioChange:Qe,initialNtrpMin:de,initialNtrpMax:me,initialShortBio:ue})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reservation detail"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",f==null?void 0:f.length,")"]}),e.jsx("div",{className:"mt-1",children:f.length>0&&f.map(t=>e.jsxs("div",{className:"text-sm",children:[t.first_name," ",t.last_name]},t.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Club Fee"}),e.jsx("span",{children:O(G)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:O(Q(s==null?void 0:s.fee_settings,G))})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:O(J)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Ot,{loading:lt,onClick:kt,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("div",{className:"text-center text-sm text-gray-500",children:q==null?void 0:q.reservation_description}),e.jsx("div",{className:"space-y-2 text-center text-sm text-gray-500",children:e.jsx("p",{children:"(You will not be charged yet)"})})]})})]})]})]})]})};export{Zs as default};
