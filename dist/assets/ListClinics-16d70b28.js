import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as l,b as u,f as wt}from"./vendor-851db8c1.js";import{an as kt,d as Ge,ak as Et,M as dt,G as ut,A as mt,u as Tt,R as Mt,v as ot,a3 as ve,a1 as lt,e as pt,ao as Dt,b as S,ab as At,T as It,c as Pt,E as Bt,K as $t,t as it,X as ct}from"./index-a0784e19.js";import{c as Lt,a as Pe}from"./yup-54691517.js";import{u as ht}from"./react-hook-form-687afde5.js";import{o as Ot}from"./yup-2824f222.js";import{P as Ht}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Rt from"./Skeleton-1e8bf077.js";import{S as be}from"./react-select-c8303602.js";import{H as Ft}from"./HistoryComponent-0c9f35b4.js";import{D as zt}from"./DataTable-a2248415.js";const Wt=({onClose:i,onConfirm:L,eventCounts:U})=>{const[v,j]=l.useState(""),a=()=>{v&&L(v)};return e.jsxs("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center ",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-2xl rounded-lg bg-white p-6",children:[e.jsxs("div",{className:"flex items-center justify-between border-b pb-4",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Confirm Updates"}),e.jsx("button",{onClick:i,className:"text-gray-500 hover:text-gray-700",children:e.jsx(kt,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"mb-4 text-base",children:"You're changing the Sport / Type / Subtype for this clinic. This affects how the clinic connects to scheduled events; time slots, availability, and visibility in the scheduler."}),e.jsx("p",{className:"mb-2 font-medium",children:"Below are the scheduled events currently tied to this clinic:"}),e.jsxs("div",{className:"mb-6 ml-6",children:[e.jsxs("p",{children:["Total Scheduled Events: ",U.total]}),e.jsxs("p",{children:["Completed Events: ",U.completed]}),e.jsxs("p",{children:["Upcoming Events: ",U.upcoming]}),e.jsxs("p",{children:["Last Event Date: ",U.lastEvent]})]}),e.jsx("p",{className:"mb-4 font-medium",children:"Choose how to handle scheduled events:"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option1",name:"sportChangeOption",value:"1",checked:v==="1",onChange:r=>j(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option1",className:"font-medium",children:"Cancel All Future Events"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Delete all upcoming events for this clinic. Past events will remain unchanged."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option2",name:"sportChangeOption",value:"2",checked:v==="2",onChange:r=>j(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option2",className:"font-medium",children:"Apply Changes Only to Future Events"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Keep past events intact: Update sport/type/subtype on upcoming events only."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option3",name:"sportChangeOption",value:"3",checked:v==="3",onChange:r=>j(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option3",className:"font-medium",children:"Apply Changes to All Events (Past and Future)"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Retroactively apply sport/type/subtype changes to all events connected to this clinic, including completed ones."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option4",name:"sportChangeOption",value:"4",checked:v==="4",onChange:r=>j(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option4",className:"font-medium",children:"Clone This Clinic and Keep Existing Schedule"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Keep the current events tied to this clinic, and create a new clinic with your changes that you can schedule separately."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option0",name:"sportChangeOption",value:"0",checked:v==="0",onChange:r=>j(r.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option0",className:"font-medium",children:"Don't Apply Sport/Type/Subtype Changes"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Save all other updates to this clinic, but leave the original sport, type, and subtype unchanged."})]})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t pt-4",children:[e.jsx("button",{onClick:i,className:"rounded-lg border border-gray-300 bg-white px-6 py-2 text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(Ge,{onClick:a,className:"rounded-lg bg-blue-500 px-6 py-2 text-white hover:bg-blue-600",disabled:!v,children:"Confirm and Save Changes"})]})]})]})},Z=Et();let w=new dt;const Vt=({clinic:i,onClose:L,getData:U,isOpen:v,sports:j})=>{var tt,st,at;const a=i||{sport_id:"",type:"",sub_type:"",date:"",end_date:"",start_time:"",end_time:"",name:"",cost_per_head:"",description:"",recurring:0,id:null},{setValue:r,watch:o}=ht({defaultValues:{sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,date:a.date,end_date:a.end_date,start_time:a.start_time,end_time:a.end_time,name:a.name,cost_per_head:a.cost_per_head,description:a.description,recurring:a.recurring}}),[ce,ee]=l.useState(!1),[h,je]=l.useState(!1),[Be,K]=l.useState(!1),[de,$e]=l.useState([]),[Ne,Le]=l.useState([]),[Xe,Qe]=l.useState([]),[_e,ue]=l.useState([]),[te,se]=l.useState(!1),[Se,Y]=l.useState(!1),[p,Ce]=l.useState(null),[me,Oe]=l.useState(!1),[ae,re]=l.useState([]),[I,we]=l.useState(!1),[He,ke]=l.useState(!1),[N,pe]=l.useState([]),[M,ne]=l.useState(0),[Re,he]=l.useState(!1),[O,Ee]=l.useState({sport_id:"",type:"",sub_type:""}),[x,ge]=l.useState(null),[Fe,Te]=l.useState({total:0,completed:0,upcoming:"No upcoming events",lastEvent:"No events scheduled"}),[Me,xe]=l.useState(null),[oe,s]=l.useState([]),[f,De]=l.useState([]),[ze,H]=l.useState(Z),J=o("sport_id"),q=o("type"),G=o("start_time"),{dispatch:y}=l.useContext(ut),{dispatch:fe}=l.useContext(mt),{club:C}=Tt();l.useEffect(()=>{i&&(r("sport_id",i.sport_id||""),r("type",i.type||""),r("sub_type",i.sub_type||""),r("date",i.date||""),r("end_date",i.end_date||""),r("start_time",i.start_time||""),r("end_time",i.end_time||""),r("name",i.name||""),r("cost_per_head",i.cost_per_head||""),r("description",i.description||""),r("recurring",i.recurring||0),Ee({sport_id:i.sport_id||"",type:i.type||"",sub_type:i.sub_type||""}))},[i,r]);const Ze=async()=>{if(!a.id)return[];K(!0);try{w.setTable("clinic_coaches");const t=await w.callRestAPI({filter:[`clinic_id,eq,${a.id}`]},"GETALL"),c=(await At(y,fe,"coach",(t==null?void 0:t.list.map(m=>m.coach_id))||[],"user|user_id")).list.map(m=>{var X,F;const D=t.list.find(_=>_.coach_id===m.id),R=D?JSON.parse(D.data||"{}"):{},k=R.working_hours||[];console.log("Coach working hours data:",{coachId:m.id,coachName:`${(X=m.user)==null?void 0:X.first_name} ${(F=m.user)==null?void 0:F.last_name}`,workingHours:k,coachData:R});const le=k.map(_=>{let P=_;typeof _!="string"&&(P=String(_)),P=P.split(":").slice(0,2).join(":");const B=P.split(":");if(B.length!==2)return console.warn("Invalid time format:",_),_;const $=parseInt(B[0])||0,z=parseInt(B[1])||0,d=$>=12?"PM":"AM";return`${$%12||12}:${z.toString().padStart(2,"0")} ${d}`});return{...m,hours:le,clinicCoachId:D==null?void 0:D.id,clinicCoachData:R}});$e(c),Qe(t.list||[]),ue(c)}catch(t){return console.log(t),[]}finally{K(!1)}},We=async()=>{if(console.log("getAllCoaches called, club:",C),!(C!=null&&C.id)){console.log("No club ID available");return}we(!0);try{console.log("Fetching coaches for club ID:",C.id),w.setTable("coach");const t=await w.callRestAPI({filter:[`courtmatchup_coach.club_id,eq,${C.id}`],join:["user|user_id"]},"GETALL");console.log("Coaches API response:",t),Le(t.list||[])}catch(t){console.log("Error fetching all coaches:",t)}finally{we(!1)}};l.useEffect(()=>{i!=null&&i.id&&Ze()},[i==null?void 0:i.id]),l.useEffect(()=>{C!=null&&C.id&&We()},[C==null?void 0:C.id]),l.useEffect(()=>{N.length>0&&!Se&&M<N.length&&(console.log("Starting hours selection for coaches:",N,"Current index:",M),ft())},[N.length,M,Se]),l.useEffect(()=>{v||Ie()},[v]),l.useEffect(()=>{var t;if(J){const n=j.find(c=>c.id.toString()===J.toString());if(n){const c=((t=n.sport_types)==null?void 0:t.filter(m=>m.type!==""))||[];s(c)}else s([])}else s([])},[J,j]),l.useEffect(()=>{if(q){const t=oe.find(n=>n.type===q);if(t){const n=(t.subtype||[]).filter(c=>c!=="");De(n)}else De([])}else De([])},[q,oe]);const gt=t=>{if(!t)return Z;const n=Z.findIndex(c=>c.value===t);return n===-1?Z:Z.filter((c,m)=>m>n)};l.useEffect(()=>{if(G){const t=gt(G);H(t)}else H(Z)},[G]);const Ae=async()=>{await Ze()},Ie=()=>{pe([]),ne(0),re([]),se(!1),Y(!1)},xt=async(t,n)=>{if(console.log("handleAddCoachWithHours called with:",t,n),!t||!t.id){console.error("Invalid coach object:",t),S(y,"Invalid coach data",3e3,"error");return}ke(!0);try{w.setTable("clinic_coaches");const c={clinic_id:a.id,coach_id:t.id,data:JSON.stringify({working_hours:n,fees:a.cost_per_head||0,sport_id:a.sport_id,type:a.type||"",sub_type:a.sub_type||""})};return(await w.callRestAPI(c,"POST")).error?!1:(S(y,"Coach added successfully",3e3,"success"),await Ae(),!0)}catch(c){return S(y,c==null?void 0:c.message,3e3,"error"),console.log(c),!1}finally{ke(!1)}},ft=async()=>{if(N.length===0)return;const t=N[M];t&&(Ce({...t,hours:[]}),Y(!0))},yt=async t=>{const n=N[M];console.log("Completing hours for coach:",n,"Hours:",t),await xt(n,t)&&(M<N.length-1?(console.log("Moving to next coach, index:",M+1),ne(M+1)):(console.log("All coaches processed successfully"),pe([]),ne(0),S(y,"All coaches added successfully",3e3,"success")))},bt=async t=>{try{w.setTable("clinic_coaches"),await w.callRestAPI({id:t.clinicCoachId},"DELETE"),S(y,"Coach removed successfully",3e3,"success"),await Ae()}catch(n){S(y,n==null?void 0:n.message,3e3,"error"),console.log(n)}},vt=async t=>{try{return console.log(`Fetching event counts for clinic ID: ${t}`),{total:12,completed:4,upcoming:"April 3, 2025",lastEvent:"June 19, 2025"}}catch(n){return console.log("Error fetching event counts:",n),{total:0,completed:0,upcoming:"No upcoming events",lastEvent:"No events scheduled"}}},jt=async()=>{if(!a.id){S(y,"Cannot save: no clinic selected",3e3,"error");return}const t={id:a.id,name:o("name"),cost_per_head:parseFloat(o("cost_per_head")),description:o("description"),sport_id:o("sport_id"),type:o("type"),sub_type:o("sub_type"),date:o("date"),end_date:o("end_date"),start_time:o("start_time"),end_time:o("end_time"),recurring:o("recurring")===1||o("recurring")===!0?1:0};if(t.sport_id!==O.sport_id||t.type!==O.type||t.sub_type!==O.sub_type){ee(!0);try{const c=await vt(a.id);Te(c),he(!0),xe(t)}catch(c){S(y,"Error fetching event information",3e3,"error"),console.log(c)}finally{ee(!1)}return}await et(t)},Nt={0:"no_changes",1:"cancel_future",2:"update_future_only",3:"update_all_events",4:"clone_clinic"},et=async t=>{ee(!0);try{const n={...t};x!==null&&(n.sport_change_option=x,n.sport_change_action=Nt[x]),console.log("API Payload being sent:",n),w.setTable("clinics");const c=await w.callRestAPI(n,"PUT");c!=null&&c.error||(S(y,"Clinic updated successfully",3e3,"success"),i&&Object.keys(n).forEach(m=>{m!=="id"&&(i[m]=n[m])}),Ee({sport_id:t.sport_id,type:t.type,sub_type:t.sub_type}),je(!1),ge(null),await Ae(),U())}catch(n){S(y,(n==null?void 0:n.message)||"An error occurred",3e3,"error"),console.log(n)}finally{ee(!1)}};l.useEffect(()=>{h&&(r("name",a.name||""),r("cost_per_head",a.cost_per_head||""),r("description",a.description||""),r("sport_id",a.sport_id||""),r("type",a.type||""),r("sub_type",a.sub_type||""),r("date",a.date||""),r("end_date",a.end_date||""),r("start_time",a.start_time||""),r("end_time",a.end_time||""),r("recurring",a.recurring))},[h,a,r]);const _t=()=>{var _,P,B,$,z;const[t,n]=l.useState((p==null?void 0:p.hours)||[]),c=d=>{if(!d)return 0;const[g,b]=d.split(":").map(Number);return g*60+b},m=d=>{if(!d)return"";const[g,b]=d.split(":").map(Number),E=g>=12?"PM":"AM";return`${g%12||12}:${b.toString().padStart(2,"0")} ${E}`},D=m(a.start_time),R=m(a.end_time),le=((d,g)=>{const b=[],E=c(d),T=c(g);for(let A=E;A<T;A+=30){const W=Math.floor(A/60),Q=A%60,V=`${W.toString().padStart(2,"0")}:${Q.toString().padStart(2,"0")}:00`;b.push({time24:V,time12:m(V)})}return b})(a.start_time,a.end_time),X=d=>{n(g=>g.includes(d.time12)?g.filter(b=>b!==d.time12):[...g,d.time12].sort((b,E)=>{const T=A=>{const[W,Q]=A.split(" "),[V,Ve]=W.split(":").map(Number);let ie=V;return Q==="PM"&&V!==12&&(ie+=12),Q==="AM"&&V===12&&(ie=0),ie*60+Ve};return T(b)-T(E)}))},F=async()=>{Oe(!0);try{const d=t.map(g=>{const[b,E]=g.split(" "),[T,A]=b.split(":").map(Number);let W=T;return E==="PM"&&T!==12&&(W+=12),E==="AM"&&T===12&&(W=0),`${W.toString().padStart(2,"0")}:${A.toString().padStart(2,"0")}`});if(p.clinicCoachId){const g=p.clinicCoachData||{};g.working_hours=d,w.setTable("clinic_coaches"),(await w.callRestAPI({id:p.clinicCoachId,data:JSON.stringify(g)},"PUT")).error||(S(y,"Coach hours updated successfully",3e3,"success"),await Ae(),Y(!1))}else await yt(d),Y(!1)}catch(d){S(y,d==null?void 0:d.message,3e3,"error"),console.log(d)}finally{Oe(!1)}};return Se?e.jsx("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-2xl rounded-lg bg-white shadow-xl",children:[e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200 p-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Set Coach Hours"}),N.length>1&&e.jsxs("p",{className:"text-sm text-gray-500",children:["Coach ",M+1," of"," ",N.length]})]}),e.jsx("button",{onClick:Ie,className:"text-gray-400 hover:text-gray-600",children:e.jsx(lt,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex flex-col space-y-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 p-4",children:[e.jsx("div",{className:"h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((_=p==null?void 0:p.user)==null?void 0:_.photo)||"/default-avatar.png",alt:`${(P=p==null?void 0:p.user)==null?void 0:P.first_name} ${(B=p==null?void 0:p.user)==null?void 0:B.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-semibold text-gray-900",children:[($=p==null?void 0:p.user)==null?void 0:$.first_name," ",(z=p==null?void 0:p.user)==null?void 0:z.last_name]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Clinic: ",D," - ",R]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Available Time Slots"}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:le.map(d=>e.jsx("button",{onClick:()=>X(d),className:`w-full rounded-lg border-2 p-3 text-center font-medium transition-all duration-200 hover:scale-105
                        ${t.includes(d.time12)?"border-primaryBlue bg-primaryBlue text-white shadow-md":"border-gray-200 text-gray-700 hover:border-primaryBlue hover:bg-blue-50 hover:text-primaryBlue"}
                      `,children:d.time12},d.time12))})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-3 border-t border-gray-200 p-6",children:[e.jsx("button",{onClick:Ie,className:"rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50",children:"Cancel"}),e.jsx(Ge,{loading:me,onClick:F,className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:me?"Saving...":"Save Hours"})]})]})}):null};if(!v)return null;const ye={control:t=>({...t,borderRadius:"0.5rem",border:"none",backgroundColor:"#f9fafb","&:hover":{border:"none",backgroundColor:"#f3f4f6"},"&:focus-within":{border:"none",boxShadow:"none",backgroundColor:"#f3f4f6"}}),option:(t,n)=>({...t,backgroundColor:n.isSelected?"#3b82f6":n.isFocused?"#f3f4f6":"white",color:n.isSelected?"white":"#374151","&:hover":{backgroundColor:n.isSelected?"#3b82f6":"#f3f4f6"}}),menu:t=>({...t,borderRadius:"0.5rem",boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"}),multiValue:t=>({...t,backgroundColor:"#e5e7eb",borderRadius:"0.375rem"}),multiValueLabel:t=>({...t,color:"#374151",padding:"0.25rem 0.5rem"}),multiValueRemove:t=>({...t,color:"#6b7280",borderRadius:"0 0.375rem 0.375rem 0","&:hover":{backgroundColor:"#d1d5db",color:"#374151"}})};return e.jsxs(e.Fragment,{children:[e.jsxs(Mt,{isOpen:v,onClose:L,title:a.name||"Clinic details",showFooter:!1,children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:a.name||"Clinic Details"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[((tt=j.find(t=>t.id.toString()==a.sport_id))==null?void 0:tt.name)||"No sport selected",a.type&&` • ${a.type}`,a.sub_type&&` • ${a.sub_type}`]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold text-primaryBlue",children:ot(a.cost_per_head)}),e.jsx("div",{className:"text-sm text-gray-500",children:"per person"})]})]})}),e.jsx("div",{className:"flex justify-end border-b border-gray-200 pb-4",children:h?e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>je(!1),className:"rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50",children:"Cancel"}),e.jsx(Ge,{loading:ce,onClick:jt,className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:ce?"Saving...":"Save All Changes"})]}):e.jsx("button",{onClick:()=>je(!0),className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),e.jsx("span",{children:"Edit Details"})]})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Basic Information"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Clinic Name"}),h?e.jsx("input",{type:"text",value:o("name")||"",onChange:t=>r("name",t.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",placeholder:"Enter clinic name"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.name||"No name provided"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Cost per Person"}),h?e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("span",{className:"font-medium text-gray-500",children:"$"})}),e.jsx("input",{type:"number",value:o("cost_per_head")||"",onChange:t=>r("cost_per_head",t.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-8 pr-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",placeholder:"0.00",min:"0",step:"0.01"})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:ot(a.cost_per_head)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Description"}),h?e.jsx("textarea",{value:o("description")||"",onChange:t=>r("description",t.target.value),className:"w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",rows:3,placeholder:"Enter clinic description"}):e.jsx("div",{className:"min-h-[80px] rounded-lg bg-gray-50 px-3 py-2 text-gray-900",children:a.description||"No description provided"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Sport Configuration"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Sport"}),h?e.jsx(be,{className:"w-full text-sm",options:j.filter(t=>t.status===1).map(t=>({value:t.id.toString(),label:t.name})),value:{value:J,label:((st=j.find(t=>t.id.toString()==J))==null?void 0:st.name)||"Select sport"},onChange:t=>{r("sport_id",t.value),r("type",""),r("sub_type","")},styles:ye}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:((at=j.find(t=>t.id.toString()==a.sport_id))==null?void 0:at.name)||"No sport selected"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Type"}),h?e.jsx(e.Fragment,{children:oe.length>0?e.jsx(be,{className:"w-full text-sm",options:oe.map(t=>({value:t.type,label:t.type})),value:{value:q,label:q||"Select type"},onChange:t=>{r("type",t.value),r("sub_type","")},styles:ye}):e.jsx("div",{className:"rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500",children:"This sport has no types"})}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.type||"No type selected"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Sub-type"}),h?e.jsx(e.Fragment,{children:f.length>0?e.jsx(be,{className:"w-full text-sm",options:f.map(t=>({value:t,label:t})),value:{value:o("sub_type"),label:o("sub_type")||"Select sub-type"},onChange:t=>{r("sub_type",t.value)},styles:ye}):e.jsx("div",{className:"rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500",children:"This type has no sub-types"})}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.sub_type||"No sub-type selected"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Scheduling"})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Date"}),h?e.jsx("input",{type:"date",value:o("date")||"",onChange:t=>r("date",t.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.date?new Date(a.date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"No start date set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Date"}),h?e.jsx("input",{type:"date",value:o("end_date")||"",onChange:t=>r("end_date",t.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",min:o("date")||void 0}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.end_date?new Date(a.end_date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"No end date set"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Time"}),h?e.jsx(be,{className:"w-full text-sm",options:Z,value:{value:o("start_time"),label:ve(o("start_time"))||"Select time"},onChange:t=>{r("start_time",t.value)},placeholder:"Select start time",styles:ye}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:ve(a.start_time)||"Not set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Time"}),h?e.jsx(be,{className:"w-full text-sm",options:ze,value:{value:o("end_time"),label:ve(o("end_time"))||"Select time"},onChange:t=>{r("end_time",t.value)},placeholder:G?"Select end time":"Select start time first",isDisabled:!G,styles:ye}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:ve(a.end_time)||"Not set"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Recurring Event"}),h?e.jsxs("select",{value:o("recurring")===1||o("recurring")===!0?"Yes":"No",onChange:t=>r("recurring",t.target.value==="Yes"?1:0),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",children:[e.jsx("option",{value:"No",children:"No"}),e.jsx("option",{value:"Yes",children:"Yes"})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:`h-2 w-2 rounded-full ${a.recurring===1?"bg-green-400":"bg-gray-400"}`}),e.jsx("span",{children:a.recurring===1?"Yes":"No"})]})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Coaches"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("span",{className:"rounded-full bg-primaryBlue/10 px-2 py-1 text-xs font-semibold text-primaryBlue",children:[de.length," assigned"]}),h&&e.jsxs("button",{onClick:async()=>{await We(),se(!0)},disabled:I,className:"inline-flex items-center rounded-lg bg-primaryBlue px-3 py-1 text-xs text-white transition-colors hover:bg-primaryBlue/90 disabled:cursor-not-allowed disabled:opacity-50",children:[I?e.jsxs("svg",{className:"mr-1 h-3 w-3 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e.jsx("svg",{className:"mr-1 h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),I?"Loading...":"Add Coach"]})]})]})}),de.length>0?e.jsx("div",{className:"space-y-3",children:de.map(t=>{var n,c,m,D,R;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4 transition-all hover:shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((n=t.user)==null?void 0:n.photo)||t.photo||"/default-avatar.png",alt:`${((c=t.user)==null?void 0:c.first_name)||""} ${((m=t.user)==null?void 0:m.last_name)||""}`,className:"h-full w-full object-cover",onError:k=>{k.target.src="/default-avatar.png"}})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"font-semibold text-gray-900",children:[(D=t.user)==null?void 0:D.first_name," ",(R=t.user)==null?void 0:R.last_name]}),e.jsx("div",{className:"text-sm text-gray-600",children:t.hours.length>0?(()=>{const k=[...t.hours].sort((V,Ve)=>{const ie=Ue=>{if(!Ue||!Ue.includes(" "))return 0;const rt=Ue.split(" ");if(rt.length!==2)return 0;const[St,nt]=rt,Ke=St.split(":");if(Ke.length!==2)return 0;const Ye=parseInt(Ke[0])||0,Ct=parseInt(Ke[1])||0;let Je=Ye;return nt==="PM"&&Ye!==12&&(Je+=12),nt==="AM"&&Ye===12&&(Je=0),Je*60+Ct};return ie(V)-ie(Ve)}),le=k.length*.5,X=k[0],F=k[k.length-1];if(!F||!F.includes(" "))return"Invalid time format";const _=F.split(" ");if(_.length!==2)return"Invalid time format";const[P,B]=_,$=P.split(":");if($.length!==2)return"Invalid time format";const z=parseInt($[0])||0,d=parseInt($[1])||0;let g=z;B==="PM"&&z!==12&&(g+=12),B==="AM"&&z===12&&(g=0);const b=g*60+d+30,E=Math.floor(b/60),T=b%60,A=E>=12?"PM":"AM",Q=`${E%12||12}:${T.toString().padStart(2,"0")} ${A}`;return k.length===1?`${X} - ${Q} (0.5 hours)`:`${X} - ${Q} (${le} ${le===1?"hour":"hours"})`})():"No hours set"})]})]}),h&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{className:"rounded-lg border border-primaryBlue bg-white px-3 py-2 text-sm font-medium text-primaryBlue transition-colors hover:bg-blue-50",onClick:()=>{Ce(t),Y(!0)},children:t.hours.length>0?"Edit hours":"Set hours"}),e.jsx("button",{onClick:()=>bt(t),className:"rounded-lg border border-red-200 p-2 text-red-400 transition-colors hover:bg-red-50 hover:text-red-600",title:"Remove coach",children:e.jsx(lt,{className:"h-4 w-4"})})]})]},t.id)})}):e.jsxs("div",{className:"rounded-lg border-2 border-dashed border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100",children:e.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"})})}),e.jsx("p",{className:"font-medium text-gray-500",children:"No coaches assigned"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:h?"Click 'Add Coach' to assign coaches to this clinic":"Coaches will appear here when assigned to this clinic"}),h&&e.jsxs("button",{onClick:async()=>{await We(),se(!0)},disabled:I,className:"mt-4 inline-flex items-center rounded-lg bg-primaryBlue px-4 py-2 text-white transition-colors hover:bg-primaryBlue/90 disabled:cursor-not-allowed disabled:opacity-50",children:[I?e.jsxs("svg",{className:"mr-2 h-4 w-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e.jsx("svg",{className:"mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),I?"Loading coaches...":"Add coaches"]})]})]})]}),Be&&e.jsx(pt,{})]})," ",Re&&e.jsx(Wt,{onClose:()=>{r("sport_id",O.sport_id),r("type",O.type),r("sub_type",O.sub_type),ge(null),xe(null),he(!1)},onConfirm:async t=>{const n=parseInt(t);ge(n),console.log("Sport change option selected:",n),he(!1),Me&&await et(Me),xe(null)},eventCounts:Fe}),te&&(()=>{const t=Ne.filter(n=>!_e.some(c=>c.id===n.id));return console.log("CoachSelectionModal props:",{allCoaches:Ne,selectedCoaches:_e,availableCoaches:t}),e.jsx(Dt,{coaches:t,selectedCoaches:ae,setSelectedCoaches:re,isOpen:te,loading:He,onClose:()=>{Ie()},onSave:async()=>{if(ae.length===0){S(y,"Please select at least one coach",3e3,"warning");return}console.log("Setting pending coaches for hours:",ae),pe(ae),ne(0),re([]),se(!1)}})})(),e.jsx(_t,{})]})};let qe=new dt,Ut=new It;const Kt=[{header:"Clinic ID",accessor:"id"},{header:"Name",accessor:"name"},{header:"Max participants",accessor:"max_participants"},{header:"Start Date",accessor:"date"},{header:"End date",accessor:"end_date"},{header:"Time",accessor:"start_time"},{header:"Sport",accessor:"sport"},{header:"Fee",accessor:"cost_per_head"}],Yt=({club:i,sports:L,courts:U})=>{const{dispatch:v}=u.useContext(ut),{dispatch:j}=u.useContext(mt),[a,r]=u.useState([]),[o,ce]=u.useState(10),[ee,h]=u.useState(0),[je,Be]=u.useState(0),[K,de]=u.useState(0),[$e,Ne]=u.useState(!1),[Le,Xe]=u.useState(!1),[Qe,_e]=u.useState(!1);u.useState([]),u.useState([]);const[ue,te]=u.useState(!0),[se,Se]=u.useState(!1),[Y,p]=u.useState(!1),Ce=wt(),me=u.useRef(null),[Oe,ae]=u.useState(!1),[re,I]=u.useState(null),[we,He]=u.useState(!1),[ke,N]=u.useState(!1),[pe,M]=u.useState(!1);u.useState([]);const ne=Lt({id:Pe(),email:Pe(),role:Pe(),status:Pe()});ht({resolver:Ot(ne)});const Re=s=>{s===""?x(1,o):x(1,o,{},[`courtmatchup_clinics.name,cs,${s}`])},he=s=>{console.log("date search",s),s===""?x(1,o):x(1,o,{},[`date,cs,${s}`])};function O(){x(K-1,o)}function Ee(){x(K+1,o)}async function x(s,f,De={},ze=[]){te(!(Y||se));try{qe.setTable("clinics");const H=await Ut.getPaginate("clinics",{page:s,limit:f,filter:[...ze,`courtmatchup_clinics.club_id,eq,${i==null?void 0:i.id}`],join:["sports|sport_id"]});H&&te(!1);const{list:J,total:q,limit:G,num_pages:y,page:fe}=H;r(J),ce(G),h(y),de(fe),Be(q),Ne(fe>1),Xe(fe+1<=y)}catch(H){te(!1),console.log("ERROR",H),it(j,H.message)}}const ge=s=>{s.target.value===""?x(1,o):x(1,o,{},[`status,cs,${s.target.value}`])},Fe=s=>{const f=s.target.value;f===""?x(1,o):x(1,o,{},[`sport_id,eq,${f}`])};u.useEffect(()=>{if(v({type:"SETPATH",payload:{path:"program-clinics"}}),!(i!=null&&i.id))return;const f=setTimeout(async()=>{await x(1,o)},700);return()=>{clearTimeout(f)}},[i]);const Te=s=>{me.current&&!me.current.contains(s.target)&&_e(!1)};u.useEffect(()=>(document.addEventListener("mousedown",Te),()=>{document.removeEventListener("mousedown",Te)}),[]);const Me=async s=>{N(!0);try{qe.setTable("clinics"),await qe.callRestAPI({id:s},"DELETE"),x(K,o)}catch(f){console.error("Error deleting clinic:",f),it(j,f.message)}finally{N(!1)}},xe=s=>{const f={...s,id:s==null?void 0:s.id,date:s==null?void 0:s.date,startTime:s==null?void 0:s.start_time,endTime:s==null?void 0:s.end_time,sport_id:s==null?void 0:s.sport_id,type:s==null?void 0:s.type,sub_type:s==null?void 0:s.sub_type,reservation_type:3,price:s==null?void 0:s.price,status:s==null?void 0:s.status,player_ids:s==null?void 0:s.player_ids,coach_ids:s==null?void 0:s.coach_ids};I(f),ae(!0)},oe={status:s=>e.jsx("span",{className:`rounded-lg px-3 py-1 text-sm ${s.status===1?"bg-[#D1FAE5] text-[#065F46]":"bg-[#F4F4F4] text-[#393939]"}`,children:s.status===1?"Active":"Inactive"}),start_time:s=>ve(s==null?void 0:s.start_time),date:s=>ct(s==null?void 0:s.date),end_date:s=>ct(s==null?void 0:s.end_date),players:s=>s!=null&&s.player_ids?`${JSON.parse(s==null?void 0:s.player_ids).length} players`:"0 players",sport:s=>{var f;return(f=s==null?void 0:s.sports)==null?void 0:f.name}};return e.jsxs("div",{className:"h-screen px-2 md:px-8",children:[ke||ue&&e.jsx(pt,{}),e.jsx("div",{className:"flex flex-col gap-4 py-3",children:e.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"relative flex w-full max-w-xs flex-1 items-center",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(Pt,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search clinics",onChange:s=>Re(s.target.value)})]}),e.jsx("input",{type:"date",className:"w-full rounded-md border border-gray-200 text-sm text-gray-500 sm:w-auto",onChange:s=>he(s.target.value)})]}),e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:flex-wrap sm:items-center",children:[e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 sm:w-auto",defaultValue:"All",onChange:Fe,children:[e.jsx("option",{value:"",children:"Sport: All"}),L==null?void 0:L.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 sm:w-auto",defaultValue:"All",onChange:ge,children:[e.jsx("option",{value:"",children:"Status: All"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{onClick:()=>Ce("/club/program-clinics/add"),className:"inline-flex items-center gap-2 rounded-md bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[e.jsx("span",{children:"+"}),"Add new"]}),e.jsx(Ft,{title:"Clinic History",emptyMessage:"No clinic history found",activityType:Bt.clinic})]})]})]})}),ue?e.jsx(Rt,{}):e.jsx(zt,{columns:Kt,data:a,loading:ue,renderCustomCell:oe,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",emptyMessage:"No clinics available",loadingMessage:"Loading clinics...",onClick:s=>xe(s)}),e.jsx(Ht,{currentPage:K,pageCount:ee,pageSize:o,canPreviousPage:$e,canNextPage:Le,updatePageSize:s=>{ce(s),x(1,s)},previousPage:O,nextPage:Ee,gotoPage:s=>x(s,o)}),e.jsx($t,{isOpen:we,onClose:()=>He(!1),onDelete:Me,loading:pe,title:"Delete",message:"Are you sure you want to delete this clinic?"}),e.jsx(Vt,{getData:x,onClose:()=>I(null),clinic:re,isOpen:re!==null,sports:L})]})},os=Yt;export{os as L};
