import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as u,f as V,r as s,j as z}from"./vendor-851db8c1.js";import{u as J}from"./react-hook-form-687afde5.js";import{o as Q}from"./yup-2824f222.js";import{c as W,a as i}from"./yup-54691517.js";import{M as X,A as Y,G as Z,t as ee,d as te,b as ae}from"./index-a0784e19.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as m}from"./MkdInput-e4d2bb0b.js";import{S as se}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let b=new X;const tt=n=>{var v,S,N,w,I,E,A,P;const{dispatch:k}=u.useContext(Y),C=W({user_id:i(),club_id:i(),hourly_rate:i(),status:i(),bio:i(),sport_id:i(),availability:i(),account_details:i(),photo:i(),is_public:i()}).required(),{dispatch:x}=u.useContext(Z),[g,oe]=u.useState({}),[_,f]=u.useState(!1),[R,y]=u.useState(!1),D=V(),[le,F]=s.useState(0),[ie,T]=s.useState(0),[re,$]=s.useState(""),[ce,B]=s.useState(0),[de,H]=s.useState(""),[ue,U]=s.useState(""),[me,L]=s.useState(""),[ne,M]=s.useState(""),[pe,G]=s.useState(""),[be,O]=s.useState(""),{register:o,handleSubmit:q,setError:j,setValue:l,formState:{errors:a}}=J({resolver:Q(C)}),c=z();s.useEffect(function(){(async function(){try{y(!0),b.setTable("coach");const e=await b.callRestAPI({id:n.activeId?n.activeId:Number(c==null?void 0:c.id)},"GET");e.error||(l("user_id",e.model.user_id),l("club_id",e.model.club_id),l("hourly_rate",e.model.hourly_rate),l("status",e.model.status),l("bio",e.model.bio),l("sport_id",e.model.sport_id),l("availability",e.model.availability),l("account_details",e.model.account_details),l("photo",e.model.photo),l("is_public",e.model.is_public),F(e.model.user_id),T(e.model.club_id),$(e.model.hourly_rate),B(e.model.status),H(e.model.bio),U(e.model.sport_id),L(e.model.availability),M(e.model.account_details),G(e.model.photo),O(e.model.is_public),y(!1))}catch(e){y(!1),console.log("error",e),ee(k,e.message)}})()},[]);const K=async e=>{f(!0);try{b.setTable("coach");for(let p in g){let d=new FormData;d.append("file",g[p].file);let h=await b.uploadImage(d);e[p]=h.url}const r=await b.callRestAPI({id:n.activeId?n.activeId:Number(c==null?void 0:c.id),user_id:e.user_id,club_id:e.club_id,hourly_rate:e.hourly_rate,status:e.status,bio:e.bio,sport_id:e.sport_id,availability:e.availability,account_details:e.account_details,photo:e.photo,is_public:e.is_public},"PUT");if(!r.error)ae(x,"Updated"),D("/club/coach"),x({type:"REFRESH_DATA",payload:{refreshData:!0}}),n.setSidebar(!1);else if(r.validation){const p=Object.keys(r.validation);for(let d=0;d<p.length;d++){const h=p[d];j(h,{type:"manual",message:r.validation[h]})}}f(!1)}catch(r){f(!1),console.log("Error",r),j("user_id",{type:"manual",message:r.message})}};return u.useEffect(()=>{x({type:"SETPATH",payload:{path:"coach"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Coach"}),R?t.jsx(se,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:q(K),children:[t.jsx(m,{type:"number",page:"edit",name:"user_id",errors:a,label:"User Id",placeholder:"User Id",register:o,className:""}),t.jsx(m,{type:"number",page:"edit",name:"club_id",errors:a,label:"Club Id",placeholder:"Club Id",register:o,className:""}),t.jsx(m,{page:"edit",name:"hourly_rate",errors:a,label:"Hourly Rate",placeholder:"Hourly Rate",register:o,className:""}),t.jsx(m,{type:"number",page:"edit",name:"status",errors:a,label:"Status",placeholder:"Status",register:o,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"bio",children:"Bio"}),t.jsx("textarea",{placeholder:"Bio",...o("bio"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(v=a.bio)!=null&&v.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(S=a.bio)==null?void 0:S.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"sport_id",children:"Sport Id"}),t.jsx("textarea",{placeholder:"Sport Id",...o("sport_id"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(N=a.sport_id)!=null&&N.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(w=a.sport_id)==null?void 0:w.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"availability",children:"Availability"}),t.jsx("textarea",{placeholder:"Availability",...o("availability"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(I=a.availability)!=null&&I.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(E=a.availability)==null?void 0:E.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"account_details",children:"Account Details"}),t.jsx("textarea",{placeholder:"Account Details",...o("account_details"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(A=a.account_details)!=null&&A.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(P=a.account_details)==null?void 0:P.message})]}),t.jsx(m,{type:"text",page:"edit",name:"photo",errors:a,label:"Photo",placeholder:"Photo",register:o,className:""}),t.jsx(m,{type:"number",page:"edit",name:"is_public",errors:a,label:"Is Public",placeholder:"Is Public",register:o,className:""}),t.jsx(te,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:_,disable:_,children:"Submit"})]})]})};export{tt as default};
