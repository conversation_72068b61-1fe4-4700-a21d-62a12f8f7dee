import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{C as L}from"./ClubUI-ecd97073.js";import{r as a}from"./vendor-851db8c1.js";import{w as A,M as C,e as E}from"./index-a0784e19.js";import{S as I}from"./react-select-c8303602.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./SportList-f554881c.js";import"./PlusIcon-7e8d14d7.js";import"./PencilIcon-35185602.js";import"./TrashIcon-aaaccaf2.js";import"./InformationCircleIcon-d35f3488.js";import"./SplashScreenPagePreview-46f2da97.js";import"./BottomDrawer-4cdfc0e3.js";import"./ImageCropModal-bf06efce.js";import"./react-image-crop-1f5038af.js";import"./index.esm-09a3a6b8.js";import"./react-icons-51bc3cff.js";import"./index.esm-b72032a7.js";import"./AuthLayout-63e138af.js";import"./MembershipCard-2e1504ef.js";import"./SportTypeSelection-ee0cc3da.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./HistoryComponent-0c9f35b4.js";import"./date-fns-07266b7d.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";let i=new C;function N(){const[t,d]=a.useState(null),[f,m]=a.useState(!1),[b,h]=a.useState([]),[l,x]=a.useState(null),[P,y]=a.useState([]),[T,g]=a.useState([]);async function w(){m(!0);try{i.setTable("clubs");const e=await i.callRestAPI({},"GETALL");h(e.list)}catch(e){console.error("Error fetching data:",e)}finally{m(!1)}}const j=async e=>{m(!0);try{x({id:e.value,name:e.label}),await c(e.value,e.club_id)}catch(n){console.error("Error fetching data:",n)}finally{m(!1)}},c=async(e,n)=>{m(!0);try{const r=await i.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${e}`,{},"GET"),S=async()=>{i.setTable("surface");const o=await i.callRestAPI({},"GETALL");y(o==null?void 0:o.list)},v=async()=>{var p,u;i.setTable("sports");const o=await i.callRestAPI({filter:[`club_id,cs,${(u=(p=r==null?void 0:r.model)==null?void 0:p.club)==null?void 0:u.id}`]},"GETALL");g(o==null?void 0:o.list)};S(),v(),d(r==null?void 0:r.model)}catch(r){console.log(r)}finally{m(!1)}};return a.useEffect(()=>{w()},[]),console.log("profileSettings",t),s.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[f&&s.jsx(E,{}),s.jsxs("div",{className:"mb-4 max-w-xl",children:[s.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),s.jsx(I,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:b.map(e=>({value:e.user_id,label:e.name,club_id:e==null?void 0:e.id})),isMulti:!1,onChange:j})]}),l!=null&&l.id?s.jsx(L,{selectedClub:l,profileSettings:t,club:t==null?void 0:t.club,fetchSettings:c,courts:t==null?void 0:t.courts,sports:t==null?void 0:t.sports,pricing:t==null?void 0:t.pricing,clubUser:t==null?void 0:t.user}):s.jsx("div",{className:"flex h-[calc(100vh-200px)] items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("h3",{className:"mb-2 text-xl font-medium text-gray-900",children:"No Club Selected"}),s.jsx("p",{className:"text-gray-600",children:"Please select a club from the dropdown above to view and manage its details."})]})})]})}const Nt=A(N,"club_ui","You don't have permission to access club UI management");export{Nt as default};
