import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,f as ne}from"./vendor-851db8c1.js";import{w as ce,M as de,T as me,G as ue,A as pe,c as xe,K as he,R as fe,t as ge,b as p}from"./index-9f98cff7.js";import"./index-be4468eb.js";import{c as be,a as g}from"./yup-54691517.js";import{u as we}from"./react-hook-form-687afde5.js";import{o as je}from"./yup-2824f222.js";import"./AddButton.module-98aac587.js";import{P as ye}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import ve from"./Skeleton-1e8bf077.js";import{E as Se}from"./EmailTemplateDrawer-60de7853.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";import"./SuccessModal-e9ef416e.js";let l=new de;new me;const Ce=[{header:"Subject",accessor:"subject",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],ke=()=>{const{dispatch:d}=s.useContext(ue),{dispatch:D}=s.useContext(pe),[m,x]=s.useState([]),[i,k]=s.useState(10),[R,I]=s.useState(0),[Ne,O]=s.useState(0),[b,H]=s.useState(0),[B,V]=s.useState(!1),[F,G]=s.useState(!1),[Ee,q]=s.useState(!1);s.useState(!1),s.useState([]),s.useState([]),s.useState("eq");const[w,j]=s.useState(!0),[K,Me]=s.useState(!1),[$,Te]=s.useState(!1);s.useState();const z=ne(),N=s.useRef(null),[U,E]=s.useState(!1),[Pe,_]=s.useState(!1),[Y,y]=s.useState(null),[Z,J]=s.useState([]),[Q,h]=s.useState(!1),[a,u]=s.useState(null),[W,M]=s.useState(!1),[X,v]=s.useState(!1),[ee,T]=s.useState(!1),te=be({id:g(),email:g(),role:g(),status:g()});we({resolver:je(te)});function se(){n(b-1,i)}function ae(){n(b+1,i)}async function n(t,r,o={},f=[]){j(!($||K));try{l.setTable("email");const c=await l.callRestAPI({payload:{...o},page:t,limit:r,filter:f},"PAGINATE");c&&j(!1);const{list:S,total:oe,limit:ie,num_pages:L,page:C}=c;x(S),k(ie),I(L),H(C),O(oe),V(C>1),G(C+1<=L)}catch(c){j(!1),console.log("ERROR",c),ge(D,c.message)}}const re=async()=>{try{l.setTable("user");const t=await l.callRestAPI({filter:["role,cs,user"]},"GETALL");l.setTable("profile");const r=await l.callRestAPI({},"GETALL"),o=t.list.map(f=>{const c=r.list.find(S=>S.user_id===f.id);return{...f,...c}});J(o)}catch(t){return console.error("Error fetching users:",t),[]}finally{}},P=async t=>{t.preventDefault(),M(!0);try{if(a!=null&&a.id)l.setTable("email"),(await l.callRestAPI({subject:a.subject,html:a.html,id:a.id},"PUT")).error||(p(d,"Email template updated successfully!",3e3,"success"),x(m.map(o=>o.id===a.id?{...o,...a}:o)));else{l.setTable("email");const r=await l.callRestAPI({subject:a.subject,html:a.html},"POST");r.error||(p(d,"Email template created successfully!",3e3,"success"),x([{...a,id:r.data},...m]))}h(!1),u(null)}catch(r){console.error(r),p(d,"Error creating email template",3e3,"error")}finally{M(!1)}},le=async t=>{T(!0);try{l.setTable("email"),(await l.callRestAPI({id:t},"DELETE")).error||(p(d,"Email template deleted successfully!",3e3,"success"),x(m.filter(o=>o.id!==t)))}catch(r){console.error(r),p(d,"An error occurred while deleting the template.",3e3,"error")}finally{T(!1),_(!1),y(null),v(!1)}};s.useEffect(()=>{d({type:"SETPATH",payload:{path:"email"}}),re();const r=setTimeout(async()=>{await n(1,i)},700);return()=>{clearTimeout(r)}},[]);const A=t=>{N.current&&!N.current.contains(t.target)&&q(!1)};return s.useEffect(()=>(document.addEventListener("mousedown",A),()=>{document.removeEventListener("mousedown",A)}),[]),e.jsxs("div",{className:"h-screen px-8",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[e.jsxs("div",{className:"relative flex max-w-[300px] flex-1 items-center justify-between",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(xe,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full rounded-xl border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search email subject",onChange:t=>{t.target.value?n(0,i,{},[`subject,cs,${t.target.value}`]):n(0,i)}})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{onClick:()=>E(!0),className:"inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.99919 10.0017H7.70752M4.99919 10.0017L2.81729 3.45596C2.6999 3.1038 3.06689 2.78551 3.39891 2.95152L16.7538 9.62898C17.0609 9.78253 17.0609 10.2208 16.7538 10.3743L3.39891 17.0518C3.06689 17.2178 2.6999 16.8995 2.81729 16.5474L4.99919 10.0017Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),"Send custom email"]}),e.jsxs("button",{onClick:()=>z("/club/program-clinics/add"),className:"inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100",children:[e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10 6.45703V9.9987L12.9167 12.9154",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M2.29102 3.95703V7.29036H5.62435",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M2.70898 12.5707C3.76873 15.5646 6.62818 17.7096 9.98935 17.7096C14.2528 17.7096 17.709 14.2585 17.709 10.0013C17.709 5.74411 14.2528 2.29297 9.98935 2.29297C6.79072 2.29297 4.04646 4.23552 2.87521 7.00362",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]}),"History"]}),e.jsxs("button",{onClick:()=>{u(null),h(!0)},className:"inline-flex items-center gap-2 rounded-xl bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[e.jsx("span",{children:"+"}),"Add new"]})]})]}),w?e.jsx(ve,{}):e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs("table",{className:"w-full min-w-full table-auto border-separate border-spacing-y-2",children:[e.jsx("thead",{className:"!border-none",children:e.jsx("tr",{children:Ce.map((t,r)=>t.accessor===""?e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500"},r):e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:t.header},r))})}),e.jsx("tbody",{className:"",children:m.map((t,r)=>e.jsxs("tr",{className:" bg-gray-100 px-4 py-3 hover:bg-gray-50",children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.subject})})}),e.jsx("td",{className:"flex justify-end whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{onClick:()=>{u(t),h(!0)},className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.041 7.20951L15.5768 4.67377C15.9022 4.34833 16.4298 4.34833 16.7553 4.67376L19.3268 7.24525C19.6522 7.57069 19.6522 8.09833 19.3268 8.42377L16.791 10.9595M13.041 7.20951L4.53509 15.7154C4.37881 15.8717 4.29102 16.0837 4.29102 16.3047V19.7095H7.69584C7.91685 19.7095 8.12881 19.6217 8.28509 19.4654L16.791 10.9595M13.041 7.20951L16.791 10.9595",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{onClick:()=>{y(t.id),v(!0)},className:"text-sm font-medium text-red-600 hover:text-red-800",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.79167 6.79297V18.8763C6.79167 19.3365 7.16476 19.7096 7.625 19.7096H16.375C16.8352 19.7096 17.2083 19.3365 17.2083 18.8763V6.79297M6.79167 6.79297H17.2083M6.79167 6.79297H5.125M17.2083 6.79297H18.875M13.6667 10.9596V15.543M10.3333 10.9596V15.543M9.5 6.79297C9.5 5.41226 10.6193 4.29297 12 4.29297C13.3807 4.29297 14.5 5.41226 14.5 6.79297",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]})})]},r))})]}),w&&e.jsx("div",{className:"px-6 py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading..."})}),!w&&m.length===0&&e.jsx("div",{className:"w-full px-6 py-4 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),e.jsx(ye,{currentPage:b,pageCount:R,pageSize:i,canPreviousPage:B,canNextPage:F,updatePageSize:t=>{k(t),n(1,t)},previousPage:se,nextPage:ae,gotoPage:t=>n(t,i)}),e.jsx(Se,{isOpen:U,onClose:()=>E(!1),members:Z}),e.jsx(he,{isOpen:X,onClose:()=>{v(!1),y(null)},onDelete:()=>le(Y),loading:ee}),e.jsx(fe,{isOpen:Q,onClose:()=>h(!1),title:a?"Edit Email Template":"Add New Email Template",primaryButtonText:a?"Update Template":"Create Template",onPrimaryAction:P,submitting:W,children:e.jsxs("form",{onSubmit:P,className:"space-y-6 py-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Subject"}),e.jsx("input",{type:"text",name:"subject",value:(a==null?void 0:a.subject)||"",onChange:t=>u({...a,subject:t.target.value}),className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Message"}),e.jsx("textarea",{name:"message",value:(a==null?void 0:a.html)||"",onChange:t=>u({...a,html:t.target.value}),rows:6,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]})})]})},jt=ce(ke,"email","You don't have permission to access email");export{jt as default};
