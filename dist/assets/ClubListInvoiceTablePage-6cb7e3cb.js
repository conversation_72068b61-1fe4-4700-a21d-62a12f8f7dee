import{j as n}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,r as R}from"./vendor-851db8c1.js";import{M as A,G as F,u as G,e as N,b as m}from"./index-a0784e19.js";import"./lodash-91d5d207.js";import{I as $}from"./InvoiceList-97d2e2bf.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index-be4468eb.js";import"./yup-54691517.js";import"./index.esm-3a36c7d6.js";import"./AddButton.module-98aac587.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./StripeConnectionStatus-2b96cb55.js";import"./HistoryComponent-0c9f35b4.js";let M=new A;const Pt=()=>{const{dispatch:T}=s.useContext(F),[p,a]=s.useState([]),[w,f]=s.useState(!1),{club:S,sports:L,fetchClubData:P}=G();s.useEffect(()=>{T({type:"SETPATH",payload:{path:"invoicing"}})},[]);async function l(j=1,C=10,D={}){f(!0);try{const{invoice_id:e="",firstName:u="",lastName:d="",sportId:b="",receiptId:h="",bookingType:v="",from:g="",until:y="",timeFrom:_="",timeUntil:k="",month:x="",year:I="",sort:E="desc",tabType:c="users"}=D,t=new URLSearchParams;t.append("page",j),t.append("limit",C),e&&t.append("receipt_id",e),u&&t.append("first_name",u),d&&t.append("last_name",d),b&&t.append("sport_id",b),h&&t.append("receipt_id",h),v&&t.append("booking_type",v),g&&t.append("from",g),y&&t.append("until",y),_&&t.append("time_from",_),k&&t.append("time_until",k),x&&t.append("month",x),I&&t.append("year",I),E&&t.append("sort",E);let o;switch(c){case"users":o="/v3/api/custom/courtmatchup/club/billing/invoices";break;case"coaches":o="/v3/api/custom/courtmatchup/reservations/billing/coach-invoices";break;case"staff":o="/v3/api/custom/courtmatchup/reservations/billing/staff-invoices";break;default:o="/v3/api/custom/courtmatchup/club/billing/invoices"}try{const i=await M.callRawAPI(`${o}?${t.toString()}`,{},"GET");if(i.error){m(i.error,"error"),a([]);return}let r=[];switch(c){case"users":r=i.invoices||[];break;case"coaches":r=i.coach_invoices||[];break;case"staff":r=i.staff_invoices||[];break;default:r=i.invoices||[]}a(r)}catch(i){console.error(`API Error for ${c} tab:`,i),a([]),i.status!==404&&m("Failed to fetch invoices","error")}}catch(e){console.error(e),m(e.message||"Failed to fetch invoices","error"),a([])}finally{f(!1)}}return R.useEffect(()=>{l(1,10,{tabType:"users"})},[]),n.jsxs("div",{className:"h-screen px-8",children:[n.jsx($,{club:S,getData:P,sports:L,fetchInvoices:l,invoices:p&&p.length>0?p:[]}),w&&n.jsx(N,{})]})};export{Pt as default};
