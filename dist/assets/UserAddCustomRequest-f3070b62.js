import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,f as we,L as pe,b as Ne}from"./vendor-851db8c1.js";import{$ as oe,G as ge,aB as xe,c as Se,v as k,d as be,aD as _e,M as je,b as H,ar as Re,u as Ce,m as ke,e as Pe,T as Be}from"./index-a0784e19.js";import{T as Me}from"./TimeSlots-c6ae4591.js";import{S as Te}from"./SportTypeSelection-ee0cc3da.js";import{C as Ae}from"./Calendar-9031b5fe.js";import{b as Ee}from"./index.esm-b72032a7.js";import{A as $e}from"./AddPlayers-53901d01.js";import{B as Le}from"./BottomDrawer-4cdfc0e3.js";import{B as Ie}from"./BackButton-11ba52b2.js";import{L as he}from"./ReservationSummary-78992f47.js";import{f as De}from"./date-fns-07266b7d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./index.esm-09a3a6b8.js";function Fe({title:d,titleId:c,...m},u){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:u,"aria-labelledby":c},m),d?r.createElement("title",{id:c},d):null,r.createElement("path",{fillRule:"evenodd",d:"M4.25 12a.75.75 0 0 1 .75-.75h14a.75.75 0 0 1 0 1.5H5a.75.75 0 0 1-.75-.75Z",clipRule:"evenodd"}))}const Oe=r.forwardRef(Fe),qe=Oe;function He({title:d,titleId:c,...m},u){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:u,"aria-labelledby":c},m),d?r.createElement("title",{id:c},d):null,r.createElement("path",{fillRule:"evenodd",d:"M12 3.75a.75.75 0 0 1 .75.75v6.75h6.75a.75.75 0 0 1 0 1.5h-6.75v6.75a.75.75 0 0 1-1.5 0v-6.75H4.5a.75.75 0 0 1 0-1.5h6.75V4.5a.75.75 0 0 1 .75-.75Z",clipRule:"evenodd"}))}const Ge=r.forwardRef(He),Ve=Ge,Ze=new je;function Ye({coaches:d,onClose:c,selectedDate:m,selectedSport:u,timeRange:y,sports:P,players:x,groups:B,club:a,isOpen:le,selectedType:w,selectedSubType:N,selectedTimes:h,userProfile:M,notes:f,numberOfPlayers:S}){const[g,G]=r.useState(!0),[t,V]=r.useState(null),[T,b]=r.useState(""),[j,_]=r.useState(1),[p,A]=r.useState([]),[E,$]=r.useState(1),[L,I]=r.useState(!1),[Z,Y]=r.useState(null),[J,D]=r.useState(!1),[i,U]=r.useState(0),[F,z]=r.useState(0),K=we(),{duration:v,end_time:Q,start_time:W}=oe(h),[de,me]=r.useState(null),[X,ve]=r.useState(null),[ee,ce]=r.useState(null),{dispatch:R}=r.useContext(ge),[O,se]=r.useState(null),ue=()=>{$(s=>Math.min(s+1,10))},te=()=>{$(s=>Math.max(s-1,1))},re=async()=>{if(!u||!m||!h||!p){H(R,"Please select all required fields",3e3,"error");return}D(!0);try{const s={sport_id:u,type:w,sub_type:N,date:m,player_ids:p.map(n=>n.id),start_time:W,end_time:Q,duration:v,coach_id:t?t.id:null,payment_intent:null,custom_request:1,notes:f,num_needed:S,reservation_type:Re.coach},o=await Ze.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",s,"POST");o.error||(H(R,"Request created successfully",3e3,"success"),se(o.reservation_id),ce(o.booking_id),K("/user/lessons"))}catch(s){console.error(s),H(R,s.message||"Error creating reservation",3e3,"error")}finally{D(!1)}},ae=s=>{A(o=>o.some(l=>l.id===s.id)?o.filter(l=>l.id!==s.id):[...o,s])},ne=()=>{t&&_(2)};r.useEffect(()=>{if(v&&(t!=null&&t.hourly_rate)){const s=v*(t==null?void 0:t.hourly_rate),o=xe(a==null?void 0:a.fee_settings,s),n=(a==null?void 0:a.club_fee)||0;U(s+o+n),z(s)}},[v,t,a==null?void 0:a.fee_settings,a==null?void 0:a.club_fee]);const ie=a!=null&&a.lesson_description?JSON.parse(a==null?void 0:a.lesson_description):{reservation_description:"",payment_description:""};return e.jsx(Le,{onClose:c,isOpen:le,title:j===1?"Select Coach":"Reservation detail",children:e.jsxs("div",{className:"relative mx-auto h-[90vh] w-full max-w-6xl overflow-y-auto rounded-lg bg-white p-6",children:[e.jsx(Ie,{onBack:()=>{}}),j===1&&e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:T,onChange:s=>b(s.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(Se,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>G(!g),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(Ee,{className:`text-xs transition-transform ${g?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto",children:[d.length>0&&d.filter(s=>`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`.toLowerCase().includes(T.toLowerCase())).sort((s,o)=>{const n=`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`.toLowerCase(),l=`${o==null?void 0:o.first_name} ${o==null?void 0:o.last_name}`.toLowerCase();return g?n.localeCompare(l):l.localeCompare(n)}).map(s=>e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(t==null?void 0:t.id)===s.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>V(s),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:(s==null?void 0:s.photo)||(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[s==null?void 0:s.first_name," ",s==null?void 0:s.last_name]})})]}),e.jsxs("span",{className:"text-gray-600",children:[k(s.hourly_rate),"/h"]})]},s.id)),e.jsx("div",{className:"flex h-full flex-col items-center justify-center gap-4",children:e.jsx("button",{onClick:()=>{V(null),_(2)},className:"b rounded-lg border border-primaryBlue px-4 py-2 text-sm text-primaryBlue ",children:"Continue without coach"})})]})]}),t&&e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Coach Profile"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-16 w-16 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsxs("p",{className:"text-lg text-gray-600",children:[k(t==null?void 0:t.hourly_rate),"/h"]})]})]}),(t==null?void 0:t.bio)&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:t==null?void 0:t.bio})})]}),e.jsx("div",{className:"border-t p-3",children:e.jsxs("button",{onClick:ne,className:"rounded-lg bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:["Continue with ",t==null?void 0:t.first_name]})})]})]}),j===2&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(he,{selectedSport:u,sports:P,selectedType:w,selectedSubType:N,selectedDate:m,selectedTimes:h,playersNeeded:E,selectedCoach:t,timeRange:oe(h)}),e.jsx($e,{searchQuery:T,setSearchQuery:b,selectedPlayers:p,setSelectedPlayers:A,onPlayerToggle:ae,players:x,groups:B,selectedGroup:Z,isFindBuddyEnabled:L,setIsFindBuddyEnabled:I,playersNeeded:E,handleIncrement:ue,handleDecrement:te,showAddReservationToFindBuddy:!1,showPlayersNeeded:!1}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving Details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",p.length,")"]}),e.jsx("div",{className:"mt-1",children:p.map(s=>s.id===M.id?e.jsx("div",{className:"text-sm",children:"Me"},s.id):e.jsxs("div",{className:"text-sm",children:[s.first_name," ",s.last_name]},s.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsx("div",{className:"text-sm",children:t?`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`:"No coach selected"})})]})]}),e.jsx(be,{loading:J,onClick:re,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Send request"}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:ie.reservation_description}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]}),j===3&&e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(he,{selectedSport:u,sports:P,selectedType:w,selectedSubType:N,selectedDate:m})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:k((a==null?void 0:a.club_fee)||0)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:k(xe(a==null?void 0:a.fee_settings,F))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:k(i)})]}),e.jsx("div",{children:e.jsx(_e,{user:M,bookingId:ee,clientSecret:de,paymentIntent:X,reservationId:O,navigateRoute:`/user/payment-success/${O}?type=lesson`})}),e.jsxs("div",{className:"space-y-4 text-sm text-gray-500",children:[e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(pe,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(pe,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})]})})]})]})]})})}let q=new je,fe=new Be;function Os(){const[d,c]=r.useState(null),[m,u]=r.useState(null),[y,P]=r.useState(null),[x,B]=r.useState(new Date),[a,le]=r.useState([]),[w,N]=r.useState(null),[h,M]=r.useState(null),[f,S]=r.useState(5),[g,G]=r.useState(""),[t,V]=r.useState(0),[T,b]=r.useState(0),[j,_]=r.useState(!1),[p,A]=r.useState([]),[E,$]=r.useState([]),[L,I]=r.useState(!1),[Z,Y]=r.useState(!1),[J,D]=r.useState(null),[i,U]=r.useState(null),[F,z]=r.useState([]),[K,v]=r.useState([]),[Q,W]=r.useState([]),{state:de,dispatch:me}=r.useContext(ge),[X,ve]=r.useState({from:null,until:null}),[ee,ce]=r.useState("main"),{user_permissions:R}=Ce(),{start_time:O,end_time:se,duration:ue}=oe(p),te=localStorage.getItem("user"),re=async()=>{try{const n=await q.callRawAPI("/v2/api/lambda/preference",{},"GET"),l=await fe.getOne("user",te,{}),C=await q.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${l.model.club_id}`,{},"GET"),ye=await fe.getList("user",{filter:["role,cs,user",`club_id,cs,${C.model.id}`]});v(ye.list),z(C.sports),D(n),U(C.model)}catch(n){console.error(n)}},ae=async()=>{try{const n=await q.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");W(n.groups)}catch(n){console.error(n)}};r.useEffect(()=>{(async()=>(Y(!0),await re(),await ae(),Y(!1)))()},[]),r.useEffect(()=>{},[ee]);const ne=()=>{B(new Date(x.setMonth(x.getMonth()-1)))},ie=()=>{B(new Date(x.setMonth(x.getMonth()+1)))},s=n=>{A([{from:n.from,until:n.until}])};r.useEffect(()=>{t&&(a!=null&&a.length)?b(t*(a==null?void 0:a.length)):b(t)},[t,a]);const o=async()=>{_(!0);const n={start_time:O,sport_id:d,end_time:se,date:De(new Date(y),"yyyy-MM-dd")};try{const l=await q.callRawAPI("/v3/api/custom/courtmatchup/user/coach/search-time-slots",n,"POST");l.error||($(l.list),I(!0))}catch(l){console.error("ERROR",l),H(me,l.message,5e3)}finally{_(!1)}};return Ne.useEffect(()=>{ke({path:"/user/create-custom-request",clubName:i==null?void 0:i.name,favicon:i==null?void 0:i.club_logo,title:"Custom request",description:"Custom Request"})},[i==null?void 0:i.club_logo]),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"flex items-center justify-center bg-white p-4 shadow-sm",children:e.jsx("p",{children:"Custom Request"})}),Z&&e.jsx(Pe,{}),e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:" p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"flex items-center justify-center rounded-lg bg-gray-50 p-3 text-center",children:"Summary"}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Number of players"}),e.jsxs("div",{className:"flex max-w-fit items-center gap-2 rounded-xl border border-gray-300",children:[e.jsx("button",{onClick:()=>S(f-1),className:"flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50",children:e.jsx(qe,{className:"h-4 w-4"})}),e.jsx("input",{type:"number",className:"w-8 rounded-lg border-none p-0 text-center text-gray-700",value:f,min:1,onChange:n=>S(n.target.value)}),e.jsx("button",{onClick:()=>S(f+1),className:"flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50",children:e.jsx(Ve,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("p",{className:"text-sm text-gray-600",children:"Custom requests are meant for parties of 5 or move players. The requests made will be checked by the club and the club will respond to you over the registered email address."})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-gray-900",children:["Custom request"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsx("textarea",{className:"w-full rounded-xl border border-gray-300 p-2 text-sm",placeholder:"Add a note",rows:4,onChange:n=>G(n.target.value),value:g,maxLength:200,showCount:!0})]})]}),e.jsx(Te,{sports:F,userPermissions:R,onSelectionChange:({sport:n,type:l,subType:C})=>{c(n),N(l),M(C)},isChildren:!0})]})]}),e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsx(Ae,{currentMonth:x,selectedDate:y,onDateSelect:P,onPreviousMonth:ne,onNextMonth:ie,daysOff:i!=null&&i.days_off?JSON.parse(i.days_off):[]})}),e.jsx(Me,{isLoading:j,selectedDate:y,timeRange:p,onTimeClick:s,onNext:o,nextButtonText:"Next: Select coach",startHour:0,endHour:24,interval:30,clubTimes:i!=null&&i.times?JSON.parse(i.times):[],isTimeSlotAvailable:n=>!0})]})})})}),L&&e.jsx(Ye,{coaches:E,onClose:()=>I(!1),selectedDate:y,selectedSport:d,selectedLocation:m,timeRange:X,sports:F,players:K,groups:Q,club:i,isOpen:L,selectedTimes:p,selectedPlayers:a,selectedType:w,selectedSubType:h,userProfile:J,notes:g,numberOfPlayers:f})]})]})}export{Os as default};
