import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as ne}from"./vendor-851db8c1.js";import{u as le}from"./react-hook-form-687afde5.js";import{o as de}from"./yup-2824f222.js";import{c as ce,a as c}from"./yup-54691517.js";import{M as pe,G as me,A as ue,B as xe,t as he,g as ge}from"./index-9f98cff7.js";import{M as T}from"./index-be4468eb.js";import{A as fe}from"./index.esm-9c6194ba.js";import{a as je}from"./index.esm-c561e951.js";import ye from"./EditAdminCmsPage-12dfbd93.js";import be from"./AddAdminCmsPage-f6114018.js";import{A as ve}from"./AddButton-df0c3574.js";import we from"./Skeleton-1e8bf077.js";import{P as Ne}from"./index-eb1bc208.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index-5d1796d5.js";import"./AddButton.module-98aac587.js";import"./react-loading-skeleton-3d87d1f5.js";let R=new pe;const p=[{header:"Page",accessor:"page",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Identifier",accessor:"content_key",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Content Type",accessor:"content_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],bt=()=>{const{dispatch:$}=a.useContext(me),{dispatch:M}=a.useContext(ue),[N,I]=a.useState([]),[n,S]=a.useState(10),[L,O]=a.useState(0),[Se,q]=a.useState(0),[m,z]=a.useState(0),[B,G]=a.useState(!1),[_,H]=a.useState(!1),[C,A]=a.useState(!1),[F,E]=a.useState(!1),[l,u]=a.useState([]),[K,x]=a.useState([]),[U,Y]=a.useState("eq"),[h,g]=a.useState(!0),[J,f]=a.useState(!1),[Q,j]=a.useState(!1),[V,W]=a.useState();ne();const y=a.useRef(null),X=ce({id:c(),email:c(),role:c(),status:c()}),{register:Ce,handleSubmit:Z,formState:{errors:Ae}}=le({resolver:de(X)});function ee(){d(m-1,n)}function te(){d(m+1,n)}const se=(t,i,s)=>{const r=i==="eq"&&isNaN(s)?`${s}`:s,o=`${t},${i},${r}`;x(b=>[...b.filter(v=>!v.includes(t)),o])},ae=()=>{d(0,n,{},K)},ie=t=>{d(0,n,{},t)};async function d(t,i,s={},r=[]){g(!0);try{R.setTable("cms");const o=await R.callRestAPI({payload:{...s},page:t,limit:i,filter:r},"PAGINATE");o&&g(!1);const{list:b,total:k,limit:v,num_pages:D,page:w}=o;I(b),S(v),O(D),z(w),q(k),G(w>1),H(w+1<=D)}catch(o){g(!1),console.log("ERROR",o),he(M,o.message)}}const re=t=>{const i=p.filter(s=>s.accessor).map(s=>{const r=ge(t[s.accessor]);return r?`${s.accessor},cs,${r}`:null}).filter(Boolean);d(0,n,{},i)};a.useEffect(()=>{$({type:"SETPATH",payload:{path:"cms"}});const i=setTimeout(async()=>{await d(1,n)},700);return()=>{clearTimeout(i)}},[]);const P=t=>{y.current&&!y.current.contains(t.target)&&A(!1)};a.useEffect(()=>(document.addEventListener("mousedown",P),()=>{document.removeEventListener("mousedown",P)}),[]);const oe=()=>{u([]),x([]),d(1,n)};return e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsx("form",{className:"relative rounded bg-white",onSubmit:Z(re),children:e.jsx("div",{className:"flex items-center gap-4 text-gray-700 text-nowrap",children:e.jsxs("div",{className:"relative",ref:y,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>A(!C),children:[e.jsx(xe,{}),e.jsx("span",{children:"Filters"}),l.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:l.length})]}),C&&e.jsx("div",{className:"absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg",children:e.jsxs("div",{className:"p-4",children:[l==null?void 0:l.map((t,i)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{Y(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>se(t,U,s.target.value)}),e.jsx("div",{className:"w-1/12 mt-[-10px]",children:e.jsx(je,{className:" cursor-pointer text-xl",onClick:()=>{u(s=>s.filter(r=>r!==t)),x(s=>{const r=s.filter(o=>!o.includes(t));return ie(r),r})}})})]},i)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{E(!F)},children:[e.jsx(fe,{}),"Add filter"]}),F&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:p.slice(0,-1).map(t=>e.jsx("li",{className:`${l.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{l.includes(t.header)||u(i=>[...i,t.accessor]),E(!1)},children:t.header},t.header))})}),l.length>0&&e.jsx("div",{onClick:oe,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:ae,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})}),e.jsx(ve,{onClick:()=>j(!0)})]}),h?e.jsx(we,{}):e.jsxs("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:p.map((t,i)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},i))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:N.map((t,i)=>e.jsx("tr",{children:p.map((s,r)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"text-[#4F46E5]",onClick:()=>{W(t.id),f(!0)},children:[" ","Edit"]})},r):s.mapping&&s.accessor==="status"?e.jsx("td",{className:"px-6 py-5 whitespace-nowrap inline-block text-sm",children:t[s.accessor]===1?e.jsx("span",{className:"bg-[#D1FAE5] rounded-md py-1 px-3 text-[#065F46]",children:s.mapping[t[s.accessor]]}):e.jsx("span",{className:"bg-[#F4F4F4] rounded-md py-1 px-3 text-[#393939]",children:s.mapping[t[s.accessor]]})},r):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},r):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},r))},i))})]}),h&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"Loading..."})}),!h&&N.length===0&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"You Don't have any Data"})})]}),e.jsx(Ne,{currentPage:m,pageCount:L,pageSize:n,canPreviousPage:B,canNextPage:_,updatePageSize:t=>{S(t),d(1,t)},previousPage:ee,nextPage:te}),e.jsx(T,{isModalActive:Q,closeModalFn:()=>j(!1),children:e.jsx(be,{setSidebar:j})}),e.jsx(T,{isModalActive:J,closeModalFn:()=>f(!1),children:e.jsx(ye,{activeId:V,setSidebar:f})})]})};export{bt as default};
