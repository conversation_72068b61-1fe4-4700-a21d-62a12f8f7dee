import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{e as P,M as D,ae as B,af as Z}from"./index-9f98cff7.js";import{b as a,j as E,f as $}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let I=new D;function ve(){const[t,N]=a.useState(null),[m,p]=a.useState(!0),{club_id:f}=E();a.useState(0);const u=$(),[C,O]=a.useState(!1),[g,c]=a.useState([0,0,0]),[d,b]=a.useState(!0),w=a.useMemo(()=>{if(t!=null&&t.splash_screen)try{return JSON.parse(t.splash_screen).slideshow_delay||6e3}catch{return 6e3}return 6e3},[t==null?void 0:t.splash_screen]),y=async()=>{p(!0);try{const n=(await I.callRawAPI("/v3/api/custom/courtmatchup/users/clubs",{filter:[`id,eq,${f}`]},"GET")).clubs.find(r=>r.id==f);N(n)}catch(s){console.log(s)}p(!1)};a.useEffect(()=>{y()},[]);const S=3,j=(t!=null&&t.splash_screen?JSON.parse(t.splash_screen):{images:[]}).images.map((s,n)=>({url:s==null?void 0:s.url,isDefault:!0,id:(s==null?void 0:s.id)||`default-${n}`,type:(s==null?void 0:s.type)||"image"}));console.log({imageList:j}),Math.ceil(j.length/S);const x=t!=null&&t.splash_screen?JSON.parse(t==null?void 0:t.splash_screen):null,i=a.useMemo(()=>{if(!(t!=null&&t.splash_screen))return[[],[],[]];const n=JSON.parse(t.splash_screen).images||[],r=[[],[],[]];return n.forEach((l,h)=>{if(l!=null&&l.url){const v=Math.floor(h/3);v<3&&r[v].push({url:l==null?void 0:l.url,type:(l==null?void 0:l.type)||"image"})}}),r},[t]);a.useEffect(()=>{if(!d)return;const s=setInterval(()=>{c(n=>{const r=[...n];return i.forEach((l,h)=>{l.length>1&&(r[h]=(r[h]+1)%l.length)}),r})},w);return()=>clearInterval(s)},[i,w,d]);const M=(s,n)=>{c(r=>{const l=[...r];return l[s]=n,l})},k=s=>{const n=i[s];n.length<=1||c(r=>{const l=[...r];return l[s]=(l[s]-1+n.length)%n.length,l})},L=s=>{const n=i[s];n.length<=1||c(r=>{const l=[...r];return l[s]=(l[s]+1)%n.length,l})},V=s=>{const n=i[s];return n.length<=1?null:e.jsx("div",{className:"absolute bottom-4 left-1/2 flex -translate-x-1/2 gap-2",children:n.map((r,l)=>e.jsx("button",{onClick:()=>M(s,l),className:`h-2 w-2 rounded-full transition-colors ${g[s]===l?"bg-white":"bg-white/50"}`},l))})},H=s=>i[s].length<=1?null:e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:r=>{r.stopPropagation(),k(s)},className:"absolute left-2 top-1/2 -translate-y-1/2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:e.jsx(B,{className:"h-6 w-6"})}),e.jsx("button",{onClick:r=>{r.stopPropagation(),L(s)},className:"absolute right-2 top-1/2 -translate-y-1/2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:e.jsx(Z,{className:"h-6 w-6"})})]}),o=s=>{const n=i[s];if(!(n!=null&&n.length))return null;const r=n[g[s]];return r?e.jsxs("div",{className:"relative h-full w-full overflow-hidden rounded-lg bg-gray-100",children:[r.type==="video"?e.jsxs("video",{src:r.url,className:"h-full w-full object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,muted:C,children:[e.jsx("source",{src:r.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):r.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"64",height:"64",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-4",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-lg font-medium text-red-600",children:"PDF Document"}),e.jsx("p",{className:"mt-1 text-sm text-red-500",children:"Click to view"})]})}):e.jsx("img",{src:r.url,alt:`Media ${s+1}`,className:"h-full w-full object-cover"}),H(s),V(s),i.some(l=>l.length>1)&&e.jsx("button",{onClick:()=>b(!d),className:"absolute right-2 top-2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:d?e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6 4H10V20H6V4ZM14 4H18V20H14V4Z",fill:"white"})}):e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 5V19L19 12L8 5Z",fill:"white"})})})]}):null},_=()=>{const s=i.reduce((n,r)=>n+(r.length>0?1:0),0);return s===0?null:s===1?e.jsx("div",{className:"h-full w-full",children:e.jsx("div",{className:"h-full",children:o(0)})}):s===2?e.jsxs("div",{className:"grid h-full w-full grid-rows-2 gap-4",children:[e.jsx("div",{className:"h-full min-h-0",children:o(0)}),e.jsx("div",{className:"h-full min-h-0",children:o(1)})]}):e.jsxs("div",{className:"grid h-full w-full grid-rows-[1fr_1fr] gap-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{className:"h-full min-h-0",children:o(0)}),e.jsx("div",{className:"h-full min-h-0",children:o(1)})]}),e.jsx("div",{className:"h-full min-h-0",children:o(2)})]})};return e.jsxs("div",{className:"mx-auto flex min-h-screen max-w-screen-2xl flex-col",children:[m&&e.jsx(P,{}),t&&e.jsxs("div",{className:"flex flex-grow flex-col",children:[e.jsxs("header",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[t.club_logo&&e.jsx("img",{src:t.club_logo,alt:t.name,className:"h-10 w-10 rounded-full"})||e.jsx("span",{className:"text-2xl",children:"🎾"}),e.jsx("span",{className:"text-xl font-semibold",children:t.name})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:()=>u(`/user/signup?club_id=${t==null?void 0:t.id}`),className:"flex items-center gap-2 rounded-xl bg-primaryBlue px-4 py-2 text-white",children:[e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.375 5.62502V3.12502C14.375 2.66478 14.0019 2.29169 13.5417 2.29169H3.12502C2.66478 2.29169 2.29169 2.66478 2.29169 3.12502V13.5417C2.29169 14.0019 2.66478 14.375 3.12502 14.375H5.62502M7.77191 17.7084C8.15596 15.8035 9.75384 14.375 11.6667 14.375C13.5795 14.375 15.1774 15.8035 15.5615 17.7084M7.77191 17.7084H6.45835C5.99812 17.7084 5.62502 17.3353 5.62502 16.875V6.45835C5.62502 5.99812 5.99812 5.62502 6.45835 5.62502H16.875C17.3353 5.62502 17.7084 5.99812 17.7084 6.45835V16.875C17.7084 17.3353 17.3353 17.7084 16.875 17.7084H15.5615M7.77191 17.7084H15.5615M13.5417 10.4167C13.5417 11.4522 12.7022 12.2917 11.6667 12.2917C10.6312 12.2917 9.79169 11.4522 9.79169 10.4167C9.79169 9.38115 10.6312 8.54169 11.6667 8.54169C12.7022 8.54169 13.5417 9.38115 13.5417 10.4167Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"})})}),e.jsx("span",{children:"Sign up"})]}),e.jsxs("button",{onClick:()=>u(`/user/login?club_id=${t==null?void 0:t.id}`),className:"flex items-center rounded-xl border border-gray-300 px-4 py-2 text-gray-500",children:[e.jsx("span",{children:"Log in"}),e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.2917 3.125L16.0417 3.125C16.5019 3.125 16.875 3.4981 16.875 3.95833V16.0417C16.875 16.5019 16.5019 16.875 16.0417 16.875H12.2917M12.5 10H3.125M12.5 10L9.58333 12.9167M12.5 10L9.58333 7.08334",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]})]}),e.jsxs("main",{className:"flex flex-col gap-8 p-4 md:flex-row md:p-8",children:[e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"relative h-[600px] w-full md:mx-auto md:max-w-6xl",children:_()})}),e.jsxs("div",{className:"flex flex-1 flex-col justify-between",children:[e.jsxs("div",{className:"flex max-h-[400px] flex-col gap-4 lg:max-h-[500px] 2xl:max-h-[550px]",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-medium",children:"Welcome!"}),e.jsxs("button",{className:"flex items-center gap-2 text-gray-600",children:[e.jsx("span",{children:"Visit website"}),e.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.2958 9.99926L7.58331 6.28676L8.64381 5.22626L13.4168 9.99926L8.64381 14.7723L7.58331 13.7118L11.2958 9.99926Z",fill:"#525866"})})]})]}),e.jsx("div",{className:"h-full space-y-4 overflow-y-auto",children:e.jsx("p",{className:"text-gray-600",children:x==null?void 0:x.bio})})]}),e.jsxs("div",{className:"mt-0 rounded-xl p-4 shadow-5",children:[e.jsx("button",{onClick:()=>u(`/user/signup?club_id=${t==null?void 0:t.id}`),className:"w-full rounded-xl bg-[#176448] py-2 text-white",children:e.jsxs("span",{className:"flex items-center justify-center gap-2",children:[e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M16 3.5H9V4.75C9 5.16421 8.66421 5.5 8.25 5.5C7.83579 5.5 7.5 5.16421 7.5 4.75V3.5H5.25C4.2835 3.5 3.5 4.2835 3.5 5.25V8H21.5V5.25C21.5 4.2835 20.7165 3.5 19.75 3.5H17.5V4.75C17.5 5.16421 17.1642 5.5 16.75 5.5C16.3358 5.5 16 5.16421 16 4.75V3.5Z",fill:"white"}),e.jsx("path",{d:"M21.5 9.5H3.5V19.75C3.5 20.7165 4.2835 21.5 5.25 21.5H19.75C20.7165 21.5 21.5 20.7165 21.5 19.75V9.5Z",fill:"white"})]}),"Reserve your slot"]})}),e.jsx("p",{className:"mt-5 text-center text-sm text-gray-500",children:"TAKES ONLY 2 MINUTES!"})]})]})]}),e.jsx("footer",{className:"mt-auto border-t p-4 text-center text-sm text-gray-500",children:"Powered by Court Matchup"})]}),!t&&!m&&e.jsx("div",{className:"flex flex-grow items-center justify-center",children:e.jsx("p",{className:"text-2xl font-bold",children:"Club not found"})})]})}export{ve as default};
