import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,f as H}from"./vendor-851db8c1.js";import{M as K,G as L,A as O,t as q,g as m}from"./index-a0784e19.js";import{o as J}from"./yup-2824f222.js";import{u as Q}from"./react-hook-form-687afde5.js";import{c as U,a as p}from"./yup-54691517.js";import{P as W}from"./index-eb1bc208.js";import{A as X}from"./AddButton-df0c3574.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./AddButton.module-98aac587.js";let Y=new K;const w=[{header:"Stripe Id",accessor:"stripe_id"},{header:"Name",accessor:"name"},{header:"Status",accessor:"status",mapping:{0:"Inactive",1:"Active"}},{header:"Action",accessor:""}],Me=()=>{var g,f;const{dispatch:y}=s.useContext(L),{dispatch:v}=s.useContext(O);s.useState("");const[j,N]=s.useState([]),[r,u]=s.useState(10),[x,S]=s.useState(0),[Z,P]=s.useState(0),[c,k]=s.useState(0),[C,A]=s.useState(!1),[E,_]=s.useState(!1),D=H(),R=U({stripe_id:p(),name:p(),status:p()}),{register:l,handleSubmit:I,formState:{errors:h}}=Q({resolver:J(R)}),T=[{key:"",value:"All"},{key:"0",value:"Inactive"},{key:"1",value:"Active"}];function z(t){(async function(){u(t),await n(1,t)})()}function B(){(async function(){await n(c-1>1?c-1:1,r)})()}function F(){(async function(){await n(c+1<=x?c+1:1,r)})()}async function n(t,i,o){try{const a=await Y.getStripeProducts({page:t,limit:i},o),{list:M,total:V,limit:$,num_pages:b,page:d}=a;N(M),u(+$),S(+b),k(+d),P(+V),A(+d>1),_(+d+1<=+b)}catch(a){console.log("ERROR",a),q(v,a.message)}}const G=t=>{const i=m(t.name),o=m(t.status),a=m(t.stripe_id);n(1,r,{name:i,status:o,stripe_id:a})};return s.useEffect(()=>{y({type:"SETPATH",payload:{path:"products"}}),async function(){await n(1,r)}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"p-5 bg-white shadow rounded mb-10",onSubmit:I(G),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsxs("div",{className:"filter-form-holder mt-10 flex flex-wrap",children:[e.jsxs("div",{className:"mb-4 w-full md:w-1/2 pr-2 pl-2",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Stripe Id"}),e.jsx("input",{type:"text",placeholder:"Stripe Id",...l("stripe_id"),className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=h.stripe_id)==null?void 0:g.message})]}),e.jsxs("div",{className:"mb-4 w-full md:w-1/2 pr-2 pl-2",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...l("name"),className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(f=h.name)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4 w-full md:w-1/2 pr-2 pl-2",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Status"}),e.jsx("select",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...l("status"),children:T.map(t=>e.jsx("option",{name:"status",value:t.key,defaultValue:"",children:t.value},t.key))}),e.jsx("p",{className:"text-red-500 text-xs italic"})]})]}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block px-6 py-2.5 bg-blue-600 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg transition duration-150 ease-in-out",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>n(1,r),className:"inline-block px-6 py-2.5 bg-gray-800 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg transition duration-150 ease-in-out",children:"Reset"})]})]}),e.jsxs("div",{className:"overflow-x-auto  p-5 bg-white shadow rounded",children:[e.jsxs("div",{className:"mb-3 text-center justify-between w-full flex  ",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Products "}),e.jsx(X,{link:"/admin/add-product"})]}),e.jsx("div",{className:"shadow overflow-x-auto border-b border-gray-200 ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:w.map((t,i)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},i))})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:j.map((t,i)=>e.jsx("tr",{children:w.map((o,a)=>o.accessor==""?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("button",{className:"mx-1 inline-block px-6 py-2.5 bg-green-500 text-white font-medium text-xs leading-tight uppercase rounded-full shadow-md hover:bg-green-600 hover:shadow-lg focus:bg-green-600 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-700 active:shadow-lg transition duration-150 ease-in-out",onClick:()=>{D("/admin/edit-product/"+t.id,{state:t})},children:[" ","Edit"]})},a):o.mapping?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.mapping[t[o.accessor]]},a):e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t[o.accessor]},a))},i))})]})})]}),e.jsx(W,{currentPage:c,pageCount:x,pageSize:r,canPreviousPage:C,canNextPage:E,updatePageSize:z,previousPage:B,nextPage:F})]})};export{Me as default};
