import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as c,r as I,f as M}from"./vendor-851db8c1.js";import{u as R}from"./react-hook-form-687afde5.js";import{o as G}from"./yup-2824f222.js";import{c as L,a as o,e as H}from"./yup-54691517.js";import{G as k,M as _,b as d,t as K}from"./index-9f98cff7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const _e=()=>{var u,p,x,h,b,g,y,f,v,j,w;const S=L({product_id:o().required(),name:o().required(),amount:H().required(),trial_days:o(),interval:o(),interval_count:o(),type:o()}).required(),{dispatch:l}=c.useContext(k),{dispatch:P}=c.useContext(k),[$,E]=I.useState([]),F=M(),{register:r,handleSubmit:A,setError:C,formState:{errors:a}}=R({resolver:G(S)}),T=[{key:"0",value:""},{key:"1",value:"Day"},{key:"2",value:"Week"},{key:"3",value:"Month"},{key:"4",value:"Year"},{key:"5",value:"Lifetime"}],q=[{key:"recurring",value:"Recurring Payment"},{key:"one_time",value:"Single Payment"}],D=async t=>{let n=new _;try{const s=await n.addStripePrice(t);if(!s.error)d(l,"Added"),F("/admin/price");else if(s.validation){const N=Object.keys(s.validation);for(let i=0;i<N.length;i++){const m=N[i];console.log(m),C(m,{type:"manual",message:s.validation[m]})}}}catch(s){console.log("Error",s),d(l,s.message),K(l,s.message)}};return c.useEffect(()=>{P({type:"SETPATH",payload:{path:"prices"}}),(async()=>{let t=new _;const{list:n,error:s}=await t.getStripeProducts({limit:"all"});if(s){d(l,"Something went wrong while fetching products list");return}E(n)})()},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium mb-4",children:"Add a Price"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:A(D),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"product_id",children:"Product"}),e.jsx("select",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...r("product_id"),children:$.map(t=>e.jsx("option",{value:t.id,children:t.name},`prod_${t.id}`))}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(u=a.product_id)==null?void 0:u.message})]}),e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...r("name"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(p=a.name)!=null&&p.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(x=a.name)==null?void 0:x.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"amount",children:"Amount"}),e.jsx("input",{type:"number",min:1,step:"any",placeholder:"Amount",...r("amount"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(h=a.amount)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(b=a.amount)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"type",children:"Checkout Type"}),e.jsx("select",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...r("type"),children:q.map(t=>e.jsx("option",{value:t.value,children:t.value},t.key))}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=a.type)==null?void 0:g.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"interval",children:"Interval"}),e.jsx("select",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...r("interval"),children:T.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.value},`interval_${t.key}`))}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(y=a.interval)==null?void 0:y.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"interval_count",children:"Interval Count"}),e.jsx("input",{type:"number",step:"1",placeholder:"Interval Count",...r("interval_count"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(f=a.interval_count)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(v=a.interval_count)==null?void 0:v.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"trial_days",children:"Trial Days"}),e.jsx("input",{type:"number",step:"1",placeholder:"7",...r("trial_days"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(j=a.trial_days)!=null&&j.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(w=a.trial_days)==null?void 0:w.message})]}),e.jsx("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{_e as default};
