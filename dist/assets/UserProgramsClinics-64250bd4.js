import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as ee,r as a}from"./vendor-851db8c1.js";import{R as Z,a3 as U,aE as G,G as se,A as te,u as ae,ab as ne,e as re,T as le,M as ie,b as oe,t as ce}from"./index-a0784e19.js";import{q as de}from"./index.esm-09a3a6b8.js";import{f as K,s as me,n as xe}from"./date-fns-07266b7d.js";import{C as ue}from"./Calendar-9031b5fe.js";import{h as he}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";function V({isOpen:r,onClose:b,clinic:u,fetchCoachesForClinic:m}){const S=ee(),[j,O]=a.useState(!1),[y,w]=a.useState([]),[A,D]=a.useState(!1),s=a.useRef(null),c=a.useRef(null),x=()=>{O(!j)};return a.useEffect(()=>{(async()=>{if(r&&u&&u.coach_ids&&Array.isArray(u.coach_ids)&&u.coach_ids.length>0&&m){const k=JSON.stringify([...u.coach_ids].sort());if(s.current===u.id&&c.current===k)return;D(!0);try{const N=await m(u.coach_ids);w(N),s.current=u.id,c.current=k}catch(N){console.error("Error fetching coaches:",N),w([])}finally{D(!1)}}else r||(w([]),D(!1),s.current=null,c.current=null)})()},[r,u==null?void 0:u.id,JSON.stringify(u==null?void 0:u.coach_ids),m]),u?e.jsx(Z,{isOpen:r,onClose:b,title:u.name,showFooter:!1,primaryButtonText:"Join",showOnlyPrimary:!0,className:"!p-0",children:e.jsxs("div",{className:"flex h-full flex-col pb-4",children:[e.jsxs("div",{className:"flex flex-1 flex-col gap-4 p-5 pb-6",children:[e.jsx("div",{className:"inline-flex items-center gap-2",children:u.slots_remaining>0?e.jsxs("span",{className:"rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-sm text-[#176448]",children:["Slots available: ",u.slots_remaining," (out of"," ",u.max_participants,")"]}):e.jsx("span",{className:"rounded-full border border-red-800 bg-red-50 px-3 py-1 text-sm text-red-800",children:"No slots available"})}),e.jsxs("div",{className:"border-1 space-y-1 rounded-xl border border-gray-200 bg-gray-100 p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DATE & TIME"}),e.jsxs("p",{className:"text-base",children:[new Date(u.clinic_date).toLocaleDateString("en-US",{month:"long",day:"numeric"})," ","• ",U(u.clinic_start_time)," -"," ",U(u.clinic_end_time)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DETAILS"}),e.jsx("p",{className:"text-base",children:u.details})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"COACHES"}),e.jsx("div",{className:"space-y-3",children:A?e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 animate-pulse rounded-full bg-gray-200"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 w-24 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-3 w-32 animate-pulse rounded bg-gray-200"})]})]}):y&&y.length>0?y.map(i=>{var k,N,v,F,P,M;return e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((k=i.user)==null?void 0:k.photo)||"/default-avatar.png",alt:`${(N=i.user)==null?void 0:N.first_name} ${(v=i.user)==null?void 0:v.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-base font-medium",children:[(F=i.user)==null?void 0:F.first_name," ",(P=i.user)==null?void 0:P.last_name]}),((M=i.user)==null?void 0:M.email)&&e.jsx("p",{className:"text-sm text-gray-500",children:i.user.email}),i.bio&&e.jsx("p",{className:"mt-1 line-clamp-2 text-xs text-gray-400",children:i.bio})]})]},i.id)}):e.jsx("p",{className:"text-base text-gray-500",children:"No coaches assigned"})})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"SPORT & TYPE"}),e.jsxs("p",{className:"text-base",children:["Tennis • Indoors •"," ",u.surface_id===1?"Hard Court":"Clay Court"]})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"NTRP"}),e.jsx("p",{className:"text-base",children:"4.0-5.0"})]})]}),e.jsx("div",{className:"sticky bottom-0 border-t border-gray-200 bg-gray-100 p-4",children:u.slots_remaining>0?e.jsxs("button",{onClick:()=>{S(`/user/clinic-booking/${u.id}`,{state:{clinic:u}})},className:"flex w-full items-center justify-center gap-2 rounded-xl bg-primaryBlue py-3 text-center text-white hover:bg-blue-900",children:[e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.75 11.5H9.25C8.0197 11.4995 6.81267 11.8354 5.75941 12.4712C4.70614 13.107 3.8467 14.0186 3.274 15.1075C3.2579 14.9054 3.2499 14.7027 3.25 14.5C3.25 10.3578 6.60775 7 10.75 7V2.875L18.625 9.25L10.75 15.625V11.5ZM9.25 10H12.25V12.481L16.2408 9.25L12.25 6.019V8.5H10.75C9.88769 8.49903 9.03535 8.68436 8.25129 9.04332C7.46724 9.40227 6.76999 9.92637 6.20725 10.5797C7.17574 10.1959 8.20822 9.99919 9.25 10Z",fill:"white"})})}),e.jsx("span",{children:" Join"})]}):e.jsx("div",{className:"rounded-2xl border border-gray-200 bg-white p-4 shadow-sm",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Get notifed"}),e.jsx("div",{className:"flex items-center",children:e.jsx("button",{type:"button",className:`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${j?"bg-blue-600":"bg-gray-200"}`,role:"switch","aria-checked":j,onClick:x,children:e.jsx("span",{"aria-hidden":"true",className:`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${j?"translate-x-5":"translate-x-0"}`})})})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"We will email you when slots for this clinic become available again, e.g. if someone opts-put."})]})})})})]})}):null}function Q({getActiveFiltersCount:r,clearFilters:b,setIsFilterModalOpen:u,availableOnly:m,toggleAvailableSlots:S,sortOrder:j,showSortOptions:O,setShowSortOptions:y,sortClinics:w}){return e.jsxs("div",{className:"mb-4 flex flex-col justify-between gap-3 sm:mb-6 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex items-center gap-3 sm:gap-4",children:[e.jsxs("button",{onClick:()=>u(!0),className:"flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-1.5 sm:px-4 sm:py-2",children:[e.jsx(de,{className:"text-blue-600"}),e.jsx("span",{className:"text-sm text-gray-700 sm:text-base",children:"Filter"}),r()>0&&e.jsx("span",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs text-white",children:r()})]}),r()>0&&e.jsx("button",{onClick:b,className:"text-sm text-gray-500 hover:underline sm:text-base",children:"Clear all"})]}),e.jsxs("div",{className:"mt-3 flex flex-wrap items-center gap-3 sm:mt-0 sm:gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"whitespace-nowrap text-xs text-gray-700 sm:text-sm",children:"Available slot only"}),e.jsx("button",{onClick:S,className:`relative h-5 w-10 rounded-full transition-colors duration-200 ease-in-out sm:h-6 sm:w-12 ${m?"bg-blue-600":"bg-gray-200"}`,children:e.jsx("div",{className:`absolute top-0.5 h-4 w-4 transform rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out sm:h-5 sm:w-5 ${m?"translate-x-5 sm:translate-x-6":"translate-x-0.5 sm:translate-x-1"}`})})]}),e.jsxs("div",{className:"relative border-gray-200 sm:border-l sm:pl-4",children:[e.jsxs("button",{onClick:()=>y(!O),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1.5 text-xs sm:gap-2 sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",j==="desc"?"Latest":"Earliest",")"]}),e.jsx(G,{size:16,className:`text-gray-400 transition-transform duration-200 ${O?"rotate-180":""}`})]}),O&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>w("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${j==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",j==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>w("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${j==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",j==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]})}function X({clinic:r}){return e.jsx("div",{className:"cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4",children:e.jsxs("div",{className:"flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsx("h3",{className:"text-base font-medium sm:text-lg",children:r.name}),r.type==1&&e.jsx("span",{className:"rounded bg-blue-600 px-2 py-1 text-xs text-white",children:"REGISTERED"})]}),e.jsxs("p",{className:"mt-1 text-xs text-gray-600 sm:text-sm",children:[new Date(r.clinic_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}),r.clinic_end_date&&e.jsxs(e.Fragment,{children:[" - "," ",new Date(r.clinic_end_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]})," • ",U(r.clinic_start_time)," -"," ",U(r.clinic_end_time)]})]}),e.jsx("div",{className:"flex flex-col gap-2 sm:items-end sm:gap-4",children:r.slots_remaining>0?e.jsxs("span",{className:"w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]",children:["Slots available: ",r.slots_remaining," (out of"," ",r.max_participants,")"]}):e.jsx("span",{className:"w-fit rounded-full border border-red-800 bg-red-50 px-3 py-1 text-xs text-red-800",children:"No slots available"})})]})})}function fe({programs:r,fetchClinics:b,FiltersContent:u,filters:m,setFilters:S,clearFilters:j,fetchCoachesForClinic:O}){const[y,w]=a.useState(!1),[A,D]=a.useState(!1),[s,c]=a.useState(null),[x,i]=a.useState(r),[k,N]=a.useState(!1);a.useState(!1);const[v,F]=a.useState("desc"),[P,M]=a.useState(!1),[C,B]=a.useState(new Date),H=async p=>{N(!0);const _=K(me(p,{weekStartsOn:0}),"yyyy-MM-dd"),L=K(xe(p,{weekStartsOn:0}),"yyyy-MM-dd");let o=[];o.push(`start_date=${_}`),o.push(`end_date=${L}`);const t=Object.entries(m.days).filter(([d,l])=>l).map(([d])=>d.toLowerCase());t.length>0&&o.push(`weekday=${t.join(",")}`);const h=Object.entries(m.timeOfDay).filter(([d,l])=>l).map(([d])=>d.toLowerCase());h.length>0&&o.push(`times=${h.join(",")}`);const n=o.join("&");await b(n),N(!1)},I=p=>{const _=[...x].sort((L,o)=>{const t=new Date(L.clinic_date+" "+L.clinic_start_time),h=new Date(o.clinic_date+" "+o.clinic_start_time);return p==="asc"?t-h:h-t});i(_),F(p),M(!1)};a.useEffect(()=>{let p=[...r];y&&(p=p.filter(L=>parseInt(L.slots_remaining)>0));const _=p.sort((L,o)=>{const t=new Date(L.clinic_date+" "+L.clinic_start_time),h=new Date(o.clinic_date+" "+o.clinic_start_time);return v==="asc"?t-h:h-t});i(_)},[r,v,y]),a.useEffect(()=>{H(C)},[]);const R=()=>{const p=Object.values(m.days).filter(Boolean).length,_=Object.values(m.timeOfDay).filter(Boolean).length,L=m.price.from||m.price.to?1:0;return p+_+L},z=async()=>{N(!0);let p=[];const _=Object.entries(m.days).filter(([t,h])=>h).map(([t])=>t.toLowerCase());_.length>0&&p.push(`weekday=${_.join(",")}`);const L=Object.entries(m.timeOfDay).filter(([t,h])=>h).map(([t])=>t.toLowerCase());L.length>0&&p.push(`times=${L.join(",")}`);const o=p.join("&");await b(o),D(!1),N(!1)},E=()=>{if(w(!y),y)i(r);else{const p=r.filter(_=>parseInt(_.slots_remaining)>0);i(p)}};return console.log("clinics",x),e.jsxs("div",{className:"mx-auto mt-3 max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:mt-5 sm:p-4",children:[e.jsx(Q,{getActiveFiltersCount:R,clearFilters:j,setIsFilterModalOpen:D,availableOnly:y,toggleAvailableSlots:E,sortOrder:v,showSortOptions:P,setShowSortOptions:M,sortClinics:I}),e.jsx("div",{className:"space-y-3 sm:space-y-4",children:x.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:y?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(R()>0||y)&&e.jsx("button",{onClick:()=>{j(),w(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):x.map(p=>e.jsx("div",{onClick:()=>c(p),children:e.jsx(X,{clinic:p})},p.id))}),e.jsx(Z,{isOpen:A,onClose:()=>D(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:z,className:"bg-gray-100",submitting:k,children:e.jsx(u,{filters:m,setFilters:S})}),e.jsx(V,{isOpen:s!==null,onClose:()=>c(null),clinic:s,fetchCoachesForClinic:O})]})}function pe({programs:r,fetchClinics:b,FiltersContent:u,filters:m,setFilters:S,clearFilters:j,clubProfile:O,fetchCoachesForClinic:y}){const[w,A]=a.useState(!1),[D,s]=a.useState(!1),[c,x]=a.useState(new Date),[i,k]=a.useState(null),[N,v]=a.useState(null),[F,P]=a.useState(r),[M,C]=a.useState(!1),[B,H]=a.useState("desc"),[I,R]=a.useState(!1),z=n=>{const d=[...F].sort((l,f)=>{const g=new Date(l.clinic_date+" "+l.clinic_start_time),$=new Date(f.clinic_date+" "+f.clinic_start_time);return n==="asc"?g-$:$-g});P(d),H(n),R(!1)};a.useEffect(()=>{let n=[...r];if(i){const l=i.toISOString().split("T")[0];n=n.filter(f=>f.clinic_date===l)}w&&(n=n.filter(l=>parseInt(l.slots_remaining)>0));const d=n.sort((l,f)=>{const g=new Date(l.clinic_date+" "+l.clinic_start_time),$=new Date(f.clinic_date+" "+f.clinic_start_time);return B==="asc"?g-$:$-g});P(d)},[r,B,i,w]);const E=()=>{const n=Object.values(m.days).filter(Boolean).length,d=Object.values(m.timeOfDay).filter(Boolean).length,l=m.price.from||m.price.to?1:0;return n+d+l},p=async n=>{if(n)try{const d=new Date(c.getFullYear(),c.getMonth(),n,0,0,0);k(d);const l=d.toISOString().split("T")[0];let f=[];f.push(`start_date=${l}`),f.push(`end_date=${l}`);const g=Object.entries(m.days).filter(([T,q])=>q).map(([T])=>T.toLowerCase());g.length>0&&f.push(`weekday=${g.join(",")}`);const $=Object.entries(m.timeOfDay).filter(([T,q])=>q).map(([T])=>T.toLowerCase());$.length>0&&f.push(`times=${$.join(",")}`);const W=f.join("&");await b(l,!1,W)}catch(d){console.error("Error handling date selection:",d)}},_=async()=>{k(null);const n=Object.entries(m.days).filter(([g,$])=>$).map(([g])=>g.toLowerCase()),d=Object.entries(m.timeOfDay).filter(([g,$])=>$).map(([g])=>g.toLowerCase());let l=[];n.length>0&&l.push(`weekday=${n.join(",")}`),d.length>0&&l.push(`times=${d.join(",")}`);const f=l.join("&");await b(null,!1,f)},L=async()=>{C(!0);let n=[];if(i){const g=i.toISOString().split("T")[0];n.push(`start_date=${g}`),n.push(`end_date=${g}`)}const d=Object.entries(m.days).filter(([g,$])=>$).map(([g])=>g.toLowerCase());d.length>0&&n.push(`weekday=${d.join(",")}`);const l=Object.entries(m.timeOfDay).filter(([g,$])=>$).map(([g])=>g.toLowerCase());l.length>0&&n.push(`times=${l.join(",")}`);const f=n.join("&");await b(i?i.toISOString().split("T")[0]:null,!1,f),s(!1),C(!1)},o=()=>{A(!w)},t=()=>{x(new Date(c.setMonth(c.getMonth()-1)))},h=()=>{x(new Date(c.setMonth(c.getMonth()+1)))};return e.jsx("div",{children:e.jsx("div",{className:"mx-auto max-w-6xl p-2 sm:p-4",children:e.jsxs("div",{className:"flex flex-col gap-4 sm:gap-6 md:flex-row md:gap-8",children:[e.jsxs("div",{className:"h-fit w-full rounded-lg bg-white p-3 shadow-sm sm:p-4 sm:shadow-5 md:w-[350px] lg:w-[400px]",children:[e.jsx(ue,{clinics:r,currentMonth:c,selectedDate:i,onDateClick:p,onPreviousMonth:t,onNextMonth:h,onDateSelect:n=>{n&&p(n.getDate())},daysOff:(()=>{try{return O!=null&&O.days_off?JSON.parse(O.days_off):[]}catch(n){return console.error("Error parsing days_off:",n),[]}})()}),i&&e.jsxs("div",{className:"mt-3 flex flex-wrap items-center justify-between border-t border-gray-200 pt-3 sm:mt-4 sm:pt-4",children:[e.jsxs("span",{className:"mr-2 text-xs text-gray-600 sm:text-sm",children:["Showing clinics for"," ",i.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]}),e.jsx("button",{onClick:_,className:"text-xs text-blue-600 hover:underline sm:text-sm",children:"Clear"})]})]}),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"space-y-3 rounded-lg bg-white p-3 shadow-sm sm:space-y-4 sm:p-5",children:[e.jsx(Q,{getActiveFiltersCount:E,clearFilters:j,setIsFilterModalOpen:s,availableOnly:w,toggleAvailableSlots:o,sortOrder:B,showSortOptions:I,setShowSortOptions:R,sortClinics:z}),F.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:w?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(E()>0||w||i)&&e.jsx("button",{onClick:async()=>{await j(),A(!1),k(null)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):F.map(n=>e.jsx("div",{onClick:()=>v(n),children:e.jsx(X,{clinic:n})},n.id))]}),e.jsx(Z,{isOpen:D,onClose:()=>s(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:L,className:"bg-gray-100",submitting:M,children:e.jsx(u,{filters:m,setFilters:S})}),e.jsx(V,{isOpen:N!==null,onClose:()=>v(null),clinic:N,fetchCoachesForClinic:y})]})]})})})}function ge({programs:r,fetchClinics:b,FiltersContent:u,filters:m,setFilters:S,clearFilters:j,fetchCoachesForClinic:O}){const[y,w]=a.useState(!1),[A,D]=a.useState(!1),[s,c]=a.useState(null),[x,i]=a.useState(r),[k,N]=a.useState(!1);a.useState(!1);const[v,F]=a.useState("desc"),[P,M]=a.useState(!1),[C,B]=a.useState(0),H=t=>{const h=[...x].sort((n,d)=>{const l=new Date(n.clinic_date+" "+n.clinic_start_time),f=new Date(d.clinic_date+" "+d.clinic_start_time);return t==="asc"?l-f:f-l});i(h),F(t),M(!1)};a.useEffect(()=>{const t=[...r].sort((h,n)=>{const d=new Date(h.clinic_date+" "+h.clinic_start_time),l=new Date(n.clinic_date+" "+n.clinic_start_time);return v==="asc"?d-l:l-d});i(t)},[r,v]);const I=()=>{const t=Object.values(m.days).filter(Boolean).length,h=Object.values(m.timeOfDay).filter(Boolean).length,n=m.price.from||m.price.to?1:0;return t+h+n},R=async()=>{N(!0);let t=[];t.push(`week=${C}`);const h=Object.entries(m.days).filter(([l,f])=>f).map(([l])=>l.toLowerCase());h.length>0&&t.push(`weekday=${h.join(",")}`);const n=Object.entries(m.timeOfDay).filter(([l,f])=>f).map(([l])=>l.toLowerCase());n.length>0&&t.push(`times=${n.join(",")}`);const d=t.join("&");await b(null,!1,d),D(!1),N(!1)},z=()=>{if(w(!y),y)i(r);else{const t=r.filter(h=>parseInt(h.slots_remaining)>0);i(t)}},[E,p]=a.useState(he()),_=async()=>{if(C>0){const t=C-1;B(t),p(h=>h.clone().subtract(1,"week")),await b(null,!1,`filter=week+${t}`)}},L=async()=>{const t=C+1;B(t),p(h=>h.clone().add(1,"week")),await b(null,!1,`filter=week+${t}`)},o=()=>{const t=E.clone().startOf("week"),h=E.clone().endOf("week"),n=`${t.format("MMM D")} - ${h.format("MMM D")}`;return C===0?`This week (${n})`:C===1?`Next week (${n})`:`${C} weeks from now (${n})`};return a.useEffect(()=>{b(null,!1,"filter=week")},[]),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mx-auto max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:p-4",children:[e.jsx("div",{className:"mx-auto mb-3 mt-3 w-fit max-w-xs rounded-xl bg-white p-1 shadow-sm sm:mb-5 sm:mt-5 sm:max-w-lg",children:e.jsxs("div",{className:"flex items-center justify-between gap-2 rounded-xl bg-gray-50 p-2 sm:gap-4",children:[e.jsx("button",{onClick:_,disabled:C===0,className:`rounded-xl bg-white p-1 text-gray-600 sm:p-2 ${C===0?"cursor-not-allowed opacity-50":"hover:text-gray-800"}`,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:"text-center text-sm font-medium sm:text-lg",children:o()}),e.jsx("button",{onClick:L,className:"rounded-xl bg-white p-1 text-gray-600 hover:text-gray-800 sm:p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})}),e.jsx(Q,{getActiveFiltersCount:I,clearFilters:j,setIsFilterModalOpen:D,availableOnly:y,toggleAvailableSlots:z,sortOrder:v,showSortOptions:P,setShowSortOptions:M,sortClinics:H}),e.jsx("div",{className:"mx-auto mt-4 max-w-4xl space-y-3 sm:space-y-4",children:x.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:y?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(I()>0||y)&&e.jsx("button",{onClick:()=>{j(),w(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):x.map(t=>e.jsx("div",{onClick:()=>c(t),children:e.jsx(X,{clinic:t})},t.id))})]}),e.jsx(Z,{isOpen:A,onClose:()=>D(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:R,className:"bg-gray-100",submitting:k,children:e.jsx(u,{filters:m,setFilters:S})}),e.jsx(V,{isOpen:s!==null,onClose:()=>c(null),clinic:s,fetchCoachesForClinic:O})]})}let J=new le,ye=new ie;function Y({filters:r,setFilters:b,customFilters:u=[],programs:m=[]}){const[S,j]=a.useState({dayOfWeek:!0,timeOfDay:!0,priceRange:!0,...u.reduce((s,c)=>({...s,[`custom_${c.id}`]:!0}),{})}),O=s=>{j(c=>({...c,[s]:!c[s]}))},y=s=>{b(c=>({...c,days:{...c.days,[s.toLowerCase()]:!c.days[s.toLowerCase()]}}))},w=s=>{b(c=>({...c,timeOfDay:{...c.timeOfDay,[s.toLowerCase()]:!c.timeOfDay[s.toLowerCase()]}}))},A=(s,c)=>{b(x=>({...x,price:{...x.price,[s]:c}}))},D=(s,c)=>{b(x=>{var i,k,N;return{...x,customFilters:{...x.customFilters,[s]:(k=(i=x.customFilters)==null?void 0:i[s])!=null&&k.includes(c)?x.customFilters[s].filter(v=>v!==c):[...((N=x.customFilters)==null?void 0:N[s])||[],c]}}})};return e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>O("dayOfWeek"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Day of week"}),e.jsx(G,{size:20,className:`text-gray-400 transition-transform duration-200 ${S.dayOfWeek?"rotate-180":""}`})]}),S.dayOfWeek&&e.jsx("div",{className:"space-y-3",children:["Weekend","Weekday","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"].map(s=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:r.days[s.toLowerCase()],onChange:()=>y(s),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s})]},s))})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>O("timeOfDay"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Time of the day"}),e.jsx(G,{size:20,className:`text-gray-400 transition-transform duration-200 ${S.timeOfDay?"rotate-180":""}`})]}),S.timeOfDay&&e.jsx("div",{className:"space-y-3",children:["Morning","Afternoon","Evening"].map(s=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:r.timeOfDay[s.toLowerCase()],onChange:()=>w(s),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s})]},s))})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>O("priceRange"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Price range"}),e.jsx(G,{size:20,className:`text-gray-400 transition-transform duration-200 ${S.priceRange?"rotate-180":""}`})]}),S.priceRange&&e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"From"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"number",value:r.price.from,onChange:s=>A("from",s.target.value),className:"w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none",placeholder:"0.00"})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"To"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"number",value:r.price.to,onChange:s=>A("to",s.target.value),className:"w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none",placeholder:"0.00"})]})]})]})]}),u.filter(s=>s.enabled).map(s=>{const c=[...new Set(m.map(x=>x[s.key]).filter(x=>x!=null&&x!==""))];return c.length===0?null:e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>O(`custom_${s.id}`),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:s.label}),e.jsx(G,{size:20,className:`text-gray-400 transition-transform duration-200 ${S[`custom_${s.id}`]?"rotate-180":""}`})]}),S[`custom_${s.id}`]&&e.jsx("div",{className:"space-y-3",children:c.map(x=>{var i,k;return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:((k=(i=r.customFilters)==null?void 0:i[s.key])==null?void 0:k.includes(x))||!1,onChange:()=>D(s.key,x),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s.key==="recurring"?x===1?"Yes":"No":x})]},x)})})]},s.id)})]})}function as(){const[r,b]=a.useState(null),[u,m]=a.useState([]),[S,j]=a.useState(null),[O,y]=a.useState([]),[w,A]=a.useState([]),{dispatch:D}=a.useContext(se),{dispatch:s}=a.useContext(te),{user_subscription:c,club_membership:x}=ae(),[i,k]=a.useState([]),[N,v]=a.useState(!1),[F,P]=a.useState([]),M=a.useMemo(()=>!(c!=null&&c.planId)||!(x!=null&&x.length)?null:x.find(o=>o.plan_id===c.planId),[c,x]),[C,B]=a.useState({days:{weekend:!1,weekday:!1,sunday:!1,monday:!1,tuesday:!1,wednesday:!1,thursday:!1,friday:!1,saturday:!1},timeOfDay:{morning:!1,afternoon:!1,evening:!1},price:{from:"",to:""},customFilters:{}}),H=[{id:"table",label:"Table"},{id:"calendar",label:"Calendar"},{id:"weekly",label:"Weekly"}],I=localStorage.getItem("user"),R=async()=>{var o;try{const t=await J.getOne("user",I,{}),h=await J.getOne("clubs",t.model.club_id,{}),n=await J.getList("sports",{filter:[`club_id,eq,${t.model.club_id}`]});console.log("sportsResponse",n),m(n.list),j(h.model);let d="table";if((o=h.model)!=null&&o.clinic_description)try{const l=JSON.parse(h.model.clinic_description);l.default_view&&(d=l.default_view),l.custom_filters&&P(l.custom_filters)}catch(l){console.error("Error parsing clinic_description:",l)}b(d)}catch(t){console.error(t),b("table")}},z=async()=>{const o=await J.getList("coach",{join:["user|user_id"]}),t=await J.getList("user",{filter:["role,cs,user"]});y(o.list),A(t.list)},E=async(o,t=!1,h="")=>{var n;v(!0);try{let d="/v3/api/custom/courtmatchup/user/clinics";if(h)d+=`?${h}`;else{let f=[];if(!t){if(o&&!isNaN(new Date(o).getTime())){const W=new Date(o);W.setHours(0,0,0,0);const T=new Date(W);T.setDate(T.getDate()+6),T.setHours(23,59,59,999),f.push(`start_date=${W.toISOString().split("T")[0]}`),f.push(`clinic_end_date=${T.toISOString().split("T")[0]}`)}const g=Object.entries(C.days).filter(([W,T])=>T).map(([W])=>W.toLowerCase());g.length>0&&f.push(`weekday=${g.join(",")}`);const $=Object.entries(C.timeOfDay).filter(([W,T])=>T).map(([W])=>W.toLowerCase());$.length>0&&f.push(`times=${$.join(",")}`),C.customFilters&&Object.entries(C.customFilters).forEach(([W,T])=>{T&&T.length>0&&f.push(`${W}=${T.join(",")}`)})}((n=M==null?void 0:M.applicable_sports)==null?void 0:n.length)>0&&f.push(`sport_ids=${M.applicable_sports.join(",")}`),f.length===0&&f.push("week=0"),f.length>0&&(d+=`?${f.join("&")}`)}const l=await ye.callRawAPI(d,{},"GET");!l.error&&(l!=null&&l.programs)&&k(l.programs)}catch(d){console.log(d),oe(D,d.message,"3000","error"),ce(s,d.status)}finally{v(!1)}},p=a.useCallback(async o=>{try{if(!o||!Array.isArray(o)||o.length===0)return[];const t=await ne(D,s,"coach",o,"user|user_id");return t.list&&Array.isArray(t.list)?t.list:[]}catch(t){return console.error("Error fetching coaches for clinic:",t),[]}},[D,s]),_=async()=>{B({days:{weekend:!1,weekday:!1,sunday:!1,monday:!1,tuesday:!1,wednesday:!1,thursday:!1,friday:!1,saturday:!1},timeOfDay:{morning:!1,afternoon:!1,evening:!1},price:{from:"",to:""},customFilters:{}}),await E(null,!0)};a.useEffect(()=>{(async()=>(v(!0),await R(),await z(),v(!1),D({type:"SETPATH",payload:{path:"program-clinics"}})))()},[]),a.useEffect(()=>{r&&(async()=>(v(!0),await E(),v(!1)))()},[r]),console.log("programs",i);async function L(o){await _(),b(o)}return e.jsxs(e.Fragment,{children:[(N||!r)&&e.jsx(re,{}),r&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col justify-between bg-white px-3 py-3 sm:flex-row sm:items-center sm:px-4 sm:py-4",children:[e.jsx("h1",{className:"mb-3 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Clinics"}),e.jsx("div",{className:"mb-4 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-8 sm:text-sm",children:H.map(o=>e.jsx("button",{onClick:()=>L(o.id),className:`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${r===o.id?"bg-white-600":"bg-gray-100 text-gray-600"}`,children:o.label},o.id))})]}),e.jsx("div",{className:"px-2 py-2 sm:px-3 sm:py-3",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[r==="table"&&e.jsx(fe,{programs:i,fetchClinics:E,FiltersContent:o=>e.jsx(Y,{...o,customFilters:F,programs:i}),filters:C,setFilters:B,clearFilters:_,fetchCoachesForClinic:p}),r==="calendar"&&e.jsx(pe,{programs:i,fetchClinics:E,FiltersContent:o=>e.jsx(Y,{...o,customFilters:F,programs:i}),filters:C,setFilters:B,clubProfile:S,clearFilters:_,fetchCoachesForClinic:p}),r==="weekly"&&e.jsx(ge,{programs:i,fetchClinics:E,FiltersContent:o=>e.jsx(Y,{...o,customFilters:F,programs:i}),filters:C,setFilters:B,clearFilters:_,fetchCoachesForClinic:p})]})})]})]})}export{as as default};
