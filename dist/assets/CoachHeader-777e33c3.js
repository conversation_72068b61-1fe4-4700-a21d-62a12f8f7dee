import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,L as p,l as m}from"./vendor-851db8c1.js";import{M as x,G as d,A as h,u as f,bb as g,aW as u,aZ as y,t as j}from"./index-9f98cff7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let C=new x;const b=[{to:"/coach/dashboard",text:"Home",icon:t.jsx(g,{className:"text-xl"}),value:"home"},{to:"/coach/availability",text:"My Availability",icon:t.jsx(u,{className:"text-xl"}),value:"availability"},{to:"/coach/statistics",text:"Statistics",icon:t.jsx(y,{className:"text-xl"}),value:"statistics"}],st=()=>{const{state:{isOpen:o,path:l},dispatch:n}=r.useContext(d),{state:v,dispatch:i}=r.useContext(h),{club:s}=f();let c=e=>{n({type:"OPEN_SIDEBAR",payload:{isOpen:e}})};return r.useEffect(()=>{async function e(){try{const a=await C.getProfile();i({type:"UPDATE_PROFILE",payload:a})}catch(a){console.log("Error",a),j(i,a.response.data.message?a.response.data.message:a.message)}}e()},[]),t.jsx("div",{className:`sticky left-0 top-0 z-50 flex h-screen flex-col bg-white shadow-lg transition-all duration-300 ${o?"w-64":"w-20"}`,children:t.jsxs("div",{className:"flex h-full flex-col",children:[t.jsx("div",{className:"flex h-16 items-center justify-between border-b px-4",children:o&&t.jsxs(p,{className:"flex cursor-pointer items-center gap-1 px-4 py-4 ",to:"/",children:[s!=null&&s.club_logo?t.jsx("img",{src:s==null?void 0:s.club_logo,className:"h-6 w-6",alt:""}):t.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M2 12C2 9.28189 3.08445 6.81707 4.84428 5.0146C7.05362 6.54963 8.5 9.10591 8.5 12C8.5 14.8941 7.05362 17.4504 4.84428 18.9854C3.08445 17.1829 2 14.7181 2 12Z",fill:"black"}),t.jsx("path",{d:"M10 12C10 8.72836 8.42889 5.82368 6 3.99927C7.67132 2.74389 9.74879 2 12 2C14.2512 2 16.3287 2.74389 18 3.99927C15.5711 5.82368 14 8.72837 14 12C14 15.2716 15.5711 18.1763 18 20.0007C16.3287 21.2561 14.2512 22 12 22C9.74879 22 7.67132 21.2561 6 20.0007C8.42889 18.1763 10 15.2716 10 12Z",fill:"black"}),t.jsx("path",{d:"M19.1557 5.0146C20.9156 6.81707 22 9.28189 22 12C22 14.7181 20.9156 17.1829 19.1557 18.9854C16.9464 17.4504 15.5 14.8941 15.5 12C15.5 9.10591 16.9464 6.54963 19.1557 5.0146Z",fill:"black"})]}),t.jsx("h4",{className:"font-sans font-bold",children:(s==null?void 0:s.name)||""})]})}),t.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:b.map(e=>t.jsx(m,{to:e.to,className:({isActive:a})=>`flex items-center rounded-lg px-4 py-2 text-sm font-medium transition-colors ${a?"bg-primary-50 text-primary-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:t.jsxs("span",{className:"flex items-center",children:[r.cloneElement(e.icon,{className:`${e.icon.props.className} ${l===e.value?"text-primary-600":"text-gray-400"}`}),o&&t.jsx("span",{className:"ml-3",children:e.text})]})},e.value))}),t.jsx("div",{className:"border-t p-4",children:t.jsx("button",{onClick:()=>c(!o),className:"flex w-full items-center justify-center rounded-lg border bg-white p-2 text-gray-400 transition-colors hover:bg-gray-50",children:t.jsx("svg",{className:`h-6 w-6 transform transition-transform duration-300 ${o?"":"rotate-180"}`,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:t.jsx("path",{d:"M15 18l-6-6 6-6"})})})})]})})};export{st as CoachHeader,st as default};
