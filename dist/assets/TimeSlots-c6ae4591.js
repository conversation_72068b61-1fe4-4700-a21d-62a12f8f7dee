import{j as n}from"./@nivo/heatmap-ba1ecfff.js";import{r as h}from"./vendor-851db8c1.js";import{d as J}from"./index-a0784e19.js";import{M as re}from"./react-tooltip-7a26650a.js";import{f as oe}from"./date-fns-07266b7d.js";const le=({selectedDate:N,timeRange:x,onTimeClick:M,onNext:k,nextButtonText:K="Next",startHour:C=8,endHour:L=24,interval:H=30,className:Q="",multipleSlots:T=!1,timeSlots:ae=[],onTimeSlotsChange:y,individualSelection:de=!1,isTimeSlotAvailable:me,clubTimes:j=[],isLoading:V,coachAvailability:B=[],height:W="h-fit",minBookingTime:X=30,enforceMinBookingTime:Y=!1})=>{var F;const[I,A]=h.useState([]),[d,u]=h.useState([]),[D,$]=h.useState(350),O=h.useRef(null),w=h.useCallback(()=>{const e=[];for(let t=C;t<=L;t++)for(let i=0;i<60;i+=H){const s=t===24?0:t,r=`${s.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`,o=s>=12?"PM":"AM",a=`${s===0?12:s>12?s-12:s}:${i.toString().padStart(2,"0")} ${o}`;e.push({time24:r,time12:a})}return e},[C,L,H]);h.useEffect(()=>{if(x&&x.length>0&&x[0].from&&x[0].until){const e=w(),t=x[0].from,i=x[0].until;if(!e.find(a=>a.time12===t))return;const r=e.findIndex(a=>a.time12===i);if(r===-1)return;const o=e.findIndex(a=>a.time12===t),l=[];for(let a=o;a<r;a++)l.push(e[a].time24);u(l)}else u([])},[x,w]),h.useEffect(()=>{const e=()=>{const t=window.innerHeight;t<600?$(200):t<800?$(300):$(350)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);const _=e=>{if(!j||j.length===0)return!0;const[t,i]=e.split(":"),s=parseInt(t)*60+parseInt(i);return j.some(r=>{const[o,l]=r.from.split(":"),[a,f]=r.until.split(":"),b=parseInt(o)*60+parseInt(l),c=parseInt(a)*60+parseInt(f);return s>=b&&s<=c})},z=e=>I.some(t=>{const i=w(),s=i.findIndex(l=>l.time12===t.from),r=i.findIndex(l=>l.time12===t.until),o=i.findIndex(l=>l.time24===e);return o>=s&&o<=r}),U=e=>{if(!N||!B||B.length===0)return!0;const t=N.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),i=B.find(r=>r.day===t);if(!i)return!1;const s=`${e}:00`;return i.timeslots.includes(s)},ee=e=>{var c,P,Z,q;if(!U(e.time24)||!_(e.time24)||T&&z(e.time24))return;const t=w(),i=t.findIndex(m=>m.time24===e.time24);if(d.includes(e.time24)){if(d.length>=2){const m=[...d].sort();if(e.time24===m[0]||e.time24===m[m.length-1]){const S=d.filter(g=>g!==e.time24);u(S);const p=[...S].sort(),E=(c=t.find(g=>g.time24===p[0]))==null?void 0:c.time12;let G;const ie=t.findIndex(g=>g.time24===p[p.length-1]),R=t[ie+1];G=(R==null?void 0:R.time12)||((P=t.find(g=>g.time24===p[p.length-1]))==null?void 0:P.time12),M({from:E,until:G})}}else d.length===1&&(u([]),M({from:"",until:""}));return}let s;if(d.length===0)s=[e.time24],u(s);else{const m=[...d].sort(),S=m[m.length-1],p=t.findIndex(E=>E.time24===S);Math.abs(i-p)===1?(s=[...d,e.time24],u(s)):(s=[e.time24],u(s))}const r=[...s].sort(),o=(Z=t.find(m=>m.time24===r[0]))==null?void 0:Z.time12;let l;const a=t.findIndex(m=>m.time24===r[r.length-1]),f=t[a+1];l=(f==null?void 0:f.time12)||((q=t.find(m=>m.time24===r[r.length-1]))==null?void 0:q.time12),M({from:o,until:l})},te=()=>{var f,b;if(d.length===0)return;const e=w(),t=[...d].sort(),i=(f=e.find(c=>c.time24===t[0]))==null?void 0:f.time12;let s;const r=e.findIndex(c=>c.time24===t[t.length-1]),o=e[r+1];s=(o==null?void 0:o.time12)||((b=e.find(c=>c.time24===t[t.length-1]))==null?void 0:b.time12);const l={from:i,until:s},a=[...I,l];A(a),u([]),y==null||y(a)},se=e=>{const t=I.filter((i,s)=>s!==e);A(t),y==null||y(t)},ne=e=>d.includes(e),v=w();return n.jsxs("div",{className:`rounded-lg bg-white p-4 shadow-5 ${Q} ${W}`,children:[N&&n.jsx("p",{className:"text-center font-medium",children:oe(N,"EEEE, MMMM d, yyyy")}),Y&&n.jsxs("div",{className:"mb-3 mt-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Minimum booking time: ",X," minutes"]}),n.jsx("div",{ref:O,className:"scrollbar-hide mb-5 mt-2 flex h-full flex-col gap-2 overflow-y-auto",style:{maxHeight:`${D}px`},children:v.map((e,t)=>{const i=_(e.time24),s=U(e.time24),r=T&&z(e.time24),o=s&&i&&!r,l=i?s?"":"Coach not available":"Club Closed";return n.jsxs("button",{onClick:()=>ee(e),disabled:!o,"data-tooltip-id":`time-${t}`,"data-tooltip-content":l,type:"button",className:`
                rounded-lg border-[1.5px] px-4 py-2 text-sm font-medium transition-colors
                ${o?ne(e.time24)?"border-[1.5px] border-primaryBlue bg-primaryBlue/10 text-primaryBlue":"border-gray-200 text-gray-500 hover:bg-gray-50":"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"}
              `,children:[e.time12,!o&&n.jsx(re,{id:`time-${t}`,place:"top",className:"z-50 !bg-gray-900 !px-2 !py-1 !text-xs !text-white"})]},e.time24)})}),d.length>0&&n.jsxs("div",{className:"space-y-2 border-t border-gray-200 pt-4",children:[n.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm",children:[n.jsx("span",{className:"font-medium",children:"From: "}),n.jsx("span",{className:"text-primaryBlue",children:(F=v.find(e=>e.time24===d.sort()[0]))==null?void 0:F.time12}),n.jsx("span",{className:"font-medium",children:"Until: "}),n.jsx("span",{className:"text-primaryBlue",children:(()=>{var s;const e=[...d].sort(),t=v.findIndex(r=>r.time24===e[e.length-1]),i=v[t+1];return(i==null?void 0:i.time12)||((s=v.find(r=>r.time24===e[e.length-1]))==null?void 0:s.time12)})()})]}),T&&n.jsx(J,{className:"mt-2 w-full rounded-lg border border-primaryBlue bg-primaryBlue/10 px-4 py-2 text-primaryBlue hover:bg-primaryBlue/20",onClick:te,disabled:d.length===0,children:"Add Time Range"})]}),T&&I.length>0&&n.jsxs("div",{className:"mt-4 space-y-2 border-t border-gray-200 pt-4",children:[n.jsx("p",{className:"text-center font-medium",children:"Selected Time Ranges"}),n.jsx("div",{className:"flex flex-col justify-center gap-2 ",children:I.map((e,t)=>n.jsxs("div",{className:"grid grid-cols-[auto_auto_auto_auto_auto] items-center gap-2 rounded-lg px-3 py-1 text-sm",children:[n.jsx("span",{className:"text-gray-500",children:"From"}),n.jsx("span",{children:e.from}),n.jsx("span",{children:"-"}),n.jsx("span",{className:"text-gray-500",children:"Until"}),n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("span",{children:e.until}),n.jsx("button",{onClick:()=>se(t),className:"text-primaryBlue hover:text-primaryBlue/80",children:n.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{d:"M15 9L9 15M15 15L9 9M21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C6.89137 21.25 2.75 17.1086 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75C17.1086 2.75 21.25 6.89137 21.25 12Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]})]},t))})]}),k&&n.jsx("div",{className:"sticky bottom-0 bg-white pt-2",children:n.jsx(J,{className:"mt-2 w-full rounded-lg bg-primaryBlue px-4 py-2 text-white disabled:opacity-50",onClick:k,disabled:T?I.length===0:d.length===0,loading:V,children:K})})]})},he=le;export{he as T};
