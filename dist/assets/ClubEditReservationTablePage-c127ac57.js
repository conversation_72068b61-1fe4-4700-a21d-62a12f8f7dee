import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as A,r as l,j as D}from"./vendor-851db8c1.js";import{u as P}from"./react-hook-form-687afde5.js";import{o as C}from"./yup-2824f222.js";import{c as U,a as b}from"./yup-54691517.js";import{M as F,A as L,G as M,t as G,d as O,b as H}from"./index-a0784e19.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as f}from"./MkdInput-e4d2bb0b.js";import{S as $}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let m=new F;const Ce=r=>{const{dispatch:k}=a.useContext(L),I=U({booking_id:b(),buddy_id:b(),user_id:b(),status:b()}).required(),{dispatch:g}=a.useContext(M),[x,q]=a.useState({}),[_,y]=a.useState(!1),[v,h]=a.useState(!1),j=A(),[K,E]=l.useState(0),[V,w]=l.useState(0),[z,N]=l.useState(0),[J,R]=l.useState(0),{register:n,handleSubmit:B,setError:S,setValue:u,formState:{errors:c}}=P({resolver:C(I)}),s=D();l.useEffect(function(){(async function(){try{h(!0),m.setTable("reservation");const e=await m.callRestAPI({id:r.activeId?r.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(u("booking_id",e.model.booking_id),u("buddy_id",e.model.buddy_id),u("user_id",e.model.user_id),u("status",e.model.status),E(e.model.booking_id),w(e.model.buddy_id),N(e.model.user_id),R(e.model.status),h(!1))}catch(e){h(!1),console.log("error",e),G(k,e.message)}})()},[]);const T=async e=>{y(!0);try{m.setTable("reservation");for(let d in x){let i=new FormData;i.append("file",x[d].file);let p=await m.uploadImage(i);e[d]=p.url}const o=await m.callRestAPI({id:r.activeId?r.activeId:Number(s==null?void 0:s.id),booking_id:e.booking_id,buddy_id:e.buddy_id,user_id:e.user_id,status:e.status},"PUT");if(!o.error)H(g,"Updated"),j("/club/reservation"),g({type:"REFRESH_DATA",payload:{refreshData:!0}}),r.setSidebar(!1);else if(o.validation){const d=Object.keys(o.validation);for(let i=0;i<d.length;i++){const p=d[i];S(p,{type:"manual",message:o.validation[p]})}}y(!1)}catch(o){y(!1),console.log("Error",o),S("booking_id",{type:"manual",message:o.message})}};return a.useEffect(()=>{g({type:"SETPATH",payload:{path:"reservation"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Reservation"}),v?t.jsx($,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:B(T),children:[t.jsx(f,{type:"number",page:"edit",name:"booking_id",errors:c,label:"Booking Id",placeholder:"Booking Id",register:n,className:""}),t.jsx(f,{type:"number",page:"edit",name:"buddy_id",errors:c,label:"Buddy Id",placeholder:"Buddy Id",register:n,className:""}),t.jsx(f,{type:"number",page:"edit",name:"user_id",errors:c,label:"User Id",placeholder:"User Id",register:n,className:""}),t.jsx(f,{type:"number",page:"edit",name:"status",errors:c,label:"Status",placeholder:"Status",register:n,className:""}),t.jsx(O,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:_,disable:_,children:"Submit"})]})]})};export{Ce as default};
