import{j as o}from"./@nivo/heatmap-ba1ecfff.js";import{r as s}from"./vendor-851db8c1.js";import{be as l}from"./index-9f98cff7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const a=({children:r,title:i,modalCloseClick:m,modalHeader:e,classes:t,page:p=""})=>o.jsx("div",{style:{zIndex:100000002,transform:"translate(-50%, -50%)"},className:"modal-holder fixed left-[50%] top-[50%] flex h-[100vh] w-full items-center justify-center overflow-auto bg-[#00000099]",children:o.jsxs("div",{className:`${p==="ManagePermissionAddRole"?"w-fit":"w-[80%]"} rounded-lg bg-white py-5 shadow ${t==null?void 0:t.modalDialog} `,children:[e&&o.jsxs("div",{className:"flex justify-between border-b px-5 pb-2",children:[o.jsx("h5",{className:"text-center text-lg font-bold uppercase",children:i}),o.jsx("div",{className:"modal-close cursor-pointer",onClick:m,children:o.jsx(l,{className:"text-xl"})})]}),o.jsx("div",{className:"mt-4 px-5",children:r})]})}),O=s.memo(a);export{O as Modal};
