import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as B,r as a,L as ve,k as De,f as Be}from"./vendor-851db8c1.js";import{u as me}from"./react-hook-form-687afde5.js";import{o as xe}from"./yup-2824f222.js";import{c as pe,a as ue,e as Se}from"./yup-54691517.js";import{M as X,T as Ce,A as _e,G as W,e as ne,d as le,t as O,b as u,n as ze,o as Re,R as Ue,p as Oe,u as Ze,q as Ge,r as qe,v as we,f as He,h as Ve,x as Ye,F as Je,y as We}from"./index-a0784e19.js";import"./index-02625b16.js";import{I as Ke}from"./ImageCropModal-bf06efce.js";import{F as Qe,a as Xe}from"./index.esm-51ae62c8.js";import{S as es}from"./index.esm-92169588.js";import{b as ss}from"./index.esm-c561e951.js";import{u as ke,a as ts,C as Ne}from"./@stripe/react-stripe-js-64f0e61f.js";import{L as as}from"./index.esm-3a36c7d6.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{B as rs}from"./BackButton-11ba52b2.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-image-crop-1f5038af.js";let Q=new X,ls=new Ce;const ns=()=>{const y=pe({email:ue().email().required()}).required(),{dispatch:N}=B.useContext(_e),[S,j]=a.useState("");B.useState({});const[C,E]=a.useState("");a.useState(!1);const[h,g]=a.useState(!1),[p,I]=a.useState({}),[Z,k]=a.useState(!0),[Y,A]=a.useState(null),[F,f]=a.useState(""),{dispatch:v}=B.useContext(W),[w,T]=a.useState(!1),[b,$]=a.useState(null),[z,L]=a.useState(!1),[M,_]=a.useState(null),{register:R,handleSubmit:s,setError:l,setValue:i,formState:{errors:o}}=me({resolver:xe(y)}),H=localStorage.getItem("user");async function D(){var c;k(!0);try{const n=await ls.getList("profile",{filter:[`user_id,eq,${H}`],join:["user|user_id"]}),r=(c=n==null?void 0:n.list)==null?void 0:c[0];if(r){const d=r.user||{},U=r.id,P={...r,...d,profile_id:U,user_id:d.id};I(P),i("email",d==null?void 0:d.email),i("first_name",d==null?void 0:d.first_name),i("last_name",d==null?void 0:d.last_name),i("phone",d==null?void 0:d.phone),i("bio",d==null?void 0:d.bio),j(d==null?void 0:d.email),E(d==null?void 0:d.photo),i("gender",r==null?void 0:r.gender),i("address",r==null?void 0:r.address),i("city",r==null?void 0:r.city),i("state",r==null?void 0:r.state),i("zip_code",r==null?void 0:r.zip_code),i("ntrp",r==null?void 0:r.ntrp),N({type:"UPDATE_PROFILE",payload:P}),k(!1)}}catch(n){O(N,n.response.data.message?n.response.data.message:n.message)}}const K=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],ie=["gender","address","city","state","zip_code","ntrp","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:p==null?void 0:p.user_id,profile_id:p==null?void 0:p.profile_id,defaultValues:p});const x=async(c,n)=>{try{g(!0);const r={[c]:n},d=K.includes(c),U=ie.includes(c);if(d){Q.setTable("user");const P=await Q.callRestAPI({id:p==null?void 0:p.user_id,...r},"PUT");P.error?G(P):(u(v,"Profile Updated",4e3),A(null),f(""),D())}else if(U){Q.setTable("profile");const P=await Q.callRestAPI({id:p==null?void 0:p.profile_id,...r},"PUT");P.error?G(P):(u(v,"Profile Updated",4e3),A(null),f(""),D())}else{u(v,"Unknown field type: "+c,4e3,"error"),g(!1);return}g(!1)}catch(r){g(!1),l(c,{type:"manual",message:r!=null&&r.message&&r==null?void 0:r.message}),O(N,r!=null&&r.message&&r==null?void 0:r.message)}},G=c=>{if(c.validation){const n=Object.keys(c.validation);for(let r=0;r<n.length;r++){const d=n[r];l(d,{type:"manual",message:c.validation[d]})}}},oe=c=>{try{if(c.size>2*1024*1024){u(v,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}_(c.type);const n=new FileReader;n.onload=()=>{$(n.result),L(!0)},n.readAsDataURL(c)}catch(n){u(v,n==null?void 0:n.message,3e3,"error"),console.log(n)}},ee=async c=>{try{T(!0);const n=M==="image/png",r=new File([c],`cropped_profile.${n?"png":"jpg"}`,{type:n?"image/png":"image/jpeg"});let d=new FormData;d.append("file",r);let U=await Q.uploadImage(d);x("photo",U==null?void 0:U.url)}catch(n){u(v,n==null?void 0:n.message,3e3,"error"),console.log(n)}finally{T(!1)}},J=()=>{x("photo",null),I({...p,photo:null})};return B.useEffect(()=>{D()},[]),e.jsxs("div",{className:"",children:[Z||w&&e.jsx(ne,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(Ke,{isOpen:z,onClose:()=>L(!1),image:b,onCropComplete:ee}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:C||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:J,disabled:!C,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:c=>oe(c.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"ntrp",label:"NTRP"},{key:"bio",label:"Bio",type:"textarea"}].map(c=>e.jsx("div",{children:Y===c.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:c.label}),e.jsx("button",{onClick:()=>A(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),c.type==="select"?e.jsxs("select",{value:F,onChange:n=>f(n.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",c.label.toLowerCase()]}),c.options.map(n=>e.jsx("option",{value:n,children:n.charAt(0).toUpperCase()+n.slice(1)},n))]}):c.type==="textarea"?e.jsx("textarea",{value:F,onChange:n=>f(n.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:F,onChange:n=>f(n.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),c.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:c.note}),e.jsx("div",{className:"mt-2",children:e.jsx(le,{loading:h,onClick:()=>x(c.key,F),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:c.label}),e.jsx("button",{onClick:()=>{A(c.key),f((p==null?void 0:p[c.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(p==null?void 0:p[c.key])||"--"}),c.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:c.note})]})},c.key))})]})]})})]})};let is=new X;function os({getData:y,onClose:N}){const[S,j]=a.useState(!1),C=ke(),E=ts(),{dispatch:h}=a.useContext(W),g=pe({user:Se(),token:ue()}),{register:p,setValue:I,handleSubmit:Z,setError:k,formState:{errors:Y}}=me({resolver:xe(g)}),A=async F=>{j(!0),C.createToken(E.getElement(Ne)).then(async f=>{if(console.log(f),f.error){u(h,f.error||"Something went wrong");return}const v={sourceToken:f.token.id};try{const w=await is.createCustomerStripeCard(v);if(!w.error)u(h,"Card added successfully");else if(w.validation){const T=Object.keys(w.validation);for(let b=0;b<T.length;b++){const $=T[b];u(h,w.validation[$],3e3)}}y(),N()}catch(w){console.error(w),u(h,w.message,5e3),O(h,w.code)}finally{j(!1)}})};return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mt-5",children:e.jsxs("form",{className:"",onSubmit:Z(A),children:[e.jsx(Ne,{className:"mb-3 rounded p-4 shadow-inner",options:{hidePostalCode:!0,style:{base:{backgroundColor:"",fontSize:"14px",lineHeight:"20px"}}}}),e.jsx(le,{loading:S,type:"submit",className:"inline-block rounded-lg bg-primaryBlue px-3 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:S?"Adding...":"Add card"})]})})})}let de=new X;function cs(){var R;const[y,N]=a.useState("bank_cards"),[S,j]=a.useState(!1),[C,E]=a.useState(!1),[h,g]=a.useState(""),[p,I]=a.useState(10),[Z,k]=a.useState(!1),[Y,A]=a.useState(!1),[F,f]=a.useState(!1),[v,w]=B.useState({});ke();const T=pe({user:Se(),token:ue()}),{dispatch:b}=B.useContext(W);me({resolver:xe(T)});const $=[{id:"bank_cards",label:"Bank cards",icon:e.jsx(Oe,{})}],z=async s=>{E(!0);try{console.log("Saving card:",s),await new Promise(l=>setTimeout(l,1e3)),j(!1)}catch(l){console.error("Error saving card:",l)}finally{E(!1)}};async function L(s){var l,i;try{f(!0);const{data:o,limit:H,error:D,message:K}=await de.getCustomerStripeCards(s);if(console.log(o),D&&u(b,K,5e3),!o)return;h||g(((l=o==null?void 0:o.data[0])==null?void 0:l.id)??""),w(o),I(+H),k(h&&h!==((i=o.data[0])==null?void 0:i.id)),A(o.has_more)}catch(o){console.error("ERROR",o),u(b,o.message,5e3),O(dispatch,o.code)}finally{f(!1)}}const M=async s=>{f(!0);const{error:l,message:i}=await de.setStripeCustomerDefaultCard(s);if(u(b,i),l){console.error(l);return}L({}),f(!1)},_=async s=>{f(!0);const{isDeleted:l,error:i,message:o}=await de.deleteCustomerStripeCard(s);if(u(b,o),i){console.error(i);return}L({}),f(!1)};return B.useEffect(()=>{L({})},[]),e.jsxs("div",{className:"mx-auto max-w-2xl p-4 sm:p-6",children:[e.jsx("h2",{className:"mb-6 text-2xl font-semibold",children:"Payment methods"}),F&&e.jsx(ne,{}),e.jsx("div",{className:"mb-6 flex flex-wrap gap-4 border-b sm:flex-nowrap sm:gap-0 sm:space-x-4",children:$.map(s=>e.jsxs("button",{onClick:()=>N(s.id),className:`flex items-center space-x-2 px-1 pb-2 ${y===s.id?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-500 hover:text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:s.icon}),e.jsx("span",{children:s.label})]},s.id))}),y==="bank_cards"&&e.jsxs("div",{className:"space-y-4",children:[(R=v==null?void 0:v.data)==null?void 0:R.map(s=>e.jsxs("div",{className:"flex flex-col justify-between gap-3 rounded-xl border p-4",children:[e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("div",{className:"text-sm text-gray-700 sm:text-base",children:s.customer.email}),e.jsx("div",{className:"flex items-center justify-end space-x-4",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>M(s.id),className:`text-sm sm:text-base ${s.id===s.customer.default_source?"text-green-600":"text-blue-600"} underline`,children:s.id===s.customer.default_source?"Default":"Set Default"}),e.jsx("button",{onClick:()=>_(s.id),className:"text-sm text-gray-500 underline sm:text-base",children:"Delete"})]})})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex space-x-2",children:[s.brand==="Visa"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#1A1F71] text-white",children:e.jsx(ss,{size:18})}),s.brand==="Mastercard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#EB001B] text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:"fill: none"}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:"fill: #ff5f00"}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:"fill: #eb001b"}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"})]})]})})}),s.brand==="MasterCard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded  text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:{fill:"none"}}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:{fill:"#ff5f00"}}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#eb001b"}}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}})]})]})})}),s.brand==="American Express"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#006FCF] text-white",children:e.jsx(es,{size:18})}),s.brand==="Discover"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#FF6000] text-sm font-bold text-white",children:"DISC"}),s.brand==="Diners Club"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0069AA] text-white",children:e.jsx(ze,{size:18})}),s.brand==="JCB"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0B4EA2] text-white",children:e.jsx(Re,{size:18})}),s.brand==="UnionPay"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#00447C] text-sm font-bold text-white",children:"UP"})]}),e.jsxs("p",{className:"text-sm text-black sm:text-base",children:[s.brand," • ",s.last4]})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["Exp. ",s.exp_month,"/",s.exp_year.toString().slice(-2)]})})]})]},s.id)),e.jsxs("button",{onClick:()=>j(!0),className:"flex w-full items-center justify-center space-x-2 rounded-lg border border-blue-600 px-4 py-2 text-blue-600 hover:bg-blue-50 sm:w-auto",children:[e.jsx("span",{children:"+"}),e.jsx("span",{children:"Add card"})]})]}),e.jsx(Ue,{isOpen:S,onClose:()=>j(!1),title:"Add card",primaryButtonText:"Add card",onPrimaryAction:z,submitting:C,showFooter:!1,children:e.jsx(os,{onSubmit:z,getData:L,onClose:()=>j(!1)})})]})}let V=new X,ds=new Ce;const re=y=>new Date(y*1e3).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});function ms(){var he,fe;const[y,N]=a.useState({}),[S,j]=a.useState([]),[C,E]=a.useState(10),[h,g]=a.useState(1),[p,I]=a.useState(1),[Z,k]=a.useState(0),[Y,A]=a.useState(!1),[F,f]=a.useState(!1),[v,w]=a.useState(!1),[T,b]=a.useState(!1),[$,z]=a.useState([]),[L,M]=a.useState(!1),[_,R]=a.useState({}),[s,l]=a.useState({}),[i,o]=a.useState(null),[H,D]=a.useState(!1),[K,ie]=a.useState([]),[x,G]=a.useState(null),[oe,ee]=a.useState(!1),[J,c]=a.useState(null),{triggerRefetch:n}=Ze(),{dispatch:r}=a.useContext(W),{dispatch:d,state:U}=B.useContext(_e);async function P(t,m,te={}){w(!0);try{console.log(U);const ae=i||parseInt(localStorage.getItem("user"));te.user_id=ae;const Te=await V.getCustomerStripeSubscriptions({page:t,limit:m},te),{list:ge,total:$e,limit:Le,num_pages:be,page:ce}=Te,ye={};ge.forEach(je=>{je.status==="active"&&(ye[je.subId]=!0)}),N(ye),j(ge),E(+Le),g(+be),I(+ce),k(+$e),A(+ce>1),f(+ce+1<=+be)}catch(ae){console.error(ae),O(d,ae.code)}finally{w(!1)}}const q=parseInt(localStorage.getItem("user"));async function se(){try{const t=i||q,m=await V.getCustomerStripeSubscription(t);l(m.customer)}catch(t){console.error(t),O(d,t.code)}}const Pe=async t=>{M(!0);try{const m=await V.cancelStripeSubscription(t);if(m.error){console.error(m.message),u(r,m.message,7500,"error");return}u(r,m.message,1e4,"success"),P(1,C),b(!1),R({})}catch(m){console.error(m),u(r,m.message,7500,"error"),O(d,m.code)}finally{M(!1)}},Ie=async()=>{try{const t=await ds.getList("user",{filter:[`guardian,eq,${q}`,"role,cs,user"]});z(t.list)}catch(t){console.error("Error fetching family members:",t)}},Ee=async()=>{try{const t=localStorage.getItem("role"),m=await V.callRawAPI(`/v3/api/custom/courtmatchup/${t}/profile`,{},"GET");ie(m.membership_plans||[])}catch(t){console.error("Error fetching membership plans:",t)}},Ae=async()=>{try{const m=(await V.getCustomerStripeCards()).find(te=>te.isDefault);c(m)}catch(t){console.error("Error fetching default card:",t)}},Me=async()=>{if(!x)return;const t=i||q;ee(!0);try{if(s!=null&&s.subId){const m=await V.updateStripeSubscriptionV3({user_id:t,activeSubscriptionId:s.subId,newPlanId:x.plan_id});m.error?u(r,m.message,7500,"error"):(u(r,"Subscription updated successfully",3e3),P(1,C),se(),D(!1),G(null))}else{const m=await V.createStripeSubscriptionV3({user_id:t,planId:x.plan_id});m.error?u(r,m.message,7500,"error"):(u(r,"Subscription created successfully",3e3),P(1,C),se(),D(!1),G(null))}}catch(m){console.error(m),u(r,m.message,7500,"error"),O(d,m.code)}finally{ee(!1)}};a.useEffect(()=>{P(1,C),Ie(),Ee(),Ae()},[q]),a.useEffect(()=>{se()},[q]),a.useEffect(()=>{i!==null&&(P(1,C),se())},[i]);const Fe=t=>{o(t)};return console.log("currentTableData",S),e.jsxs("div",{className:"mx-auto max-w-3xl p-3 sm:p-6",children:[v&&e.jsx(ne,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Membership"}),e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row",children:[e.jsx(ve,{to:"/user/membership/buy",className:"w-full rounded-lg bg-gray-600 px-4 py-2 text-center text-white transition-colors hover:bg-gray-700 sm:w-auto",children:"Browse Plans"}),e.jsx("button",{onClick:()=>D(!0),className:"w-full rounded-lg bg-blue-600 px-4 py-2 text-center text-white transition-colors hover:bg-blue-700 sm:w-auto",children:i?`Buy plan for ${((he=$.find(t=>t.id===i))==null?void 0:he.first_name)||"family member"}`:"Buy new plan"})]})]}),e.jsx("div",{className:"mb-4 border-b border-gray-200"}),e.jsx("div",{className:"relative mb-4 flex justify-end",children:e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"relative",children:e.jsxs("select",{value:i||q,onChange:t=>{const m=parseInt(t.target.value);m===q?o(null):Fe(m)},className:"w-full appearance-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[e.jsx("option",{value:q,children:"Myself"}),$.map(t=>e.jsxs("option",{value:t.id,children:[t.first_name," ",t.last_name," (",t.family_role||"Family Member",")"]},t.id))]})}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Select the user whose membership you want to manage"})]})}),!v&&S.length===0&&e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-xl bg-gray-50 px-4 py-12 text-center",children:[e.jsx("div",{className:"mb-4 rounded-full bg-blue-100 p-3",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Active Memberships"}),e.jsx("p",{className:"mb-6 text-sm text-gray-600",children:"You currently don't have any active membership plans."}),e.jsx(ve,{to:"/user/membership/buy",className:"rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700",children:"Browse Plans"})]}),S.map(t=>e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50",children:[e.jsxs("button",{onClick:()=>{N(m=>({...m,[t.subId]:!m[t.subId]}))},className:"flex w-full cursor-pointer flex-col gap-2 p-4 hover:bg-gray-100 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[y[t.subId]?e.jsx(Ge,{size:20}):e.jsx(qe,{size:20}),e.jsxs("span",{className:"text-sm font-medium sm:text-base",children:[re(t.currentPeriodStart)," -"," ",re(t.currentPeriodEnd)]})]}),e.jsxs("div",{className:"flex items-center justify-between gap-3 pl-7 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600 sm:text-base",children:t.planName}),e.jsx("span",{className:`rounded-full px-3 py-1 text-sm capitalize ${t.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.status})]})]}),y[t.subId]&&e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6 rounded-xl border bg-white p-4",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row sm:items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Plan price"}),e.jsxs("div",{className:"text-left sm:text-right",children:[e.jsx("div",{className:"font-semibold",children:we(t.planAmount)}),e.jsx("div",{className:"text-sm text-gray-500",children:"Billed annually"})]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Purchased on"}),e.jsx("span",{className:"text-sm sm:text-base",children:re(t.createdAt)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{className:"text-sm sm:text-base",children:re(t.currentPeriodEnd)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Subscription ID"}),e.jsx("span",{className:"break-all text-sm sm:text-base",children:t.subId})]}),t.status==="active"&&e.jsx("div",{className:"flex justify-center sm:justify-start",children:e.jsx("button",{onClick:()=>{b(!0),R(t)},className:"w-full rounded-xl bg-red-500 px-5 py-2 text-white transition-colors hover:bg-red-600 sm:w-auto",children:"Cancel plan"})})]})})]},t.subId)),e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center p-4 ${T?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-lg rounded-3xl bg-white p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[e.jsx("div",{children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#FDEDF0"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1 22.7V24.5H20.9V22.7H19.1ZM19.1 15.5V20.9H20.9V15.5H19.1Z",fill:"#DF1C41"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Are you sure?"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to cancel membership?"})]})]}),e.jsxs("div",{className:"flex flex-col-reverse gap-3 border-t pt-4 sm:flex-row sm:justify-end",children:[e.jsx("button",{onClick:()=>{b(!1),R({})},className:"w-full rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:"Go back"}),e.jsx(le,{onClick:()=>{Pe(_.subId),n()},className:"w-full rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700 sm:w-auto",loading:L,children:"Yes, cancel"})]})]})]}),H&&e.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 max-h-[90vh] w-full max-w-4xl overflow-y-auto rounded-3xl bg-white p-6",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:i?`Select Plan for ${((fe=$.find(t=>t.id===i))==null?void 0:fe.first_name)||"Family Member"}`:"Select Membership Plan"}),e.jsx("button",{onClick:()=>{D(!1),G(null)},className:"rounded-lg p-2 hover:bg-gray-100",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsx("div",{className:"mb-6 grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:K.map(t=>e.jsxs("div",{className:`cursor-pointer rounded-xl border p-4 transition-all ${(x==null?void 0:x.plan_id)===t.plan_id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>G(t),children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold",children:t.plan_name}),(x==null?void 0:x.plan_id)===t.plan_id&&e.jsx("div",{className:"rounded-full bg-blue-500 p-1",children:e.jsx("svg",{className:"h-4 w-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),e.jsx("div",{className:"mb-4 text-2xl font-bold",children:t.price===0?e.jsx("span",{className:"text-green-600",children:"Free"}):e.jsxs(e.Fragment,{children:[we(t.price),e.jsx("span",{className:"text-sm font-normal text-gray-500",children:"/month"})]})}),t.description&&e.jsx("p",{className:"text-sm text-gray-600",children:t.description})]},t.plan_id))}),e.jsxs("div",{className:"flex flex-col-reverse gap-3 border-t pt-4 sm:flex-row sm:justify-end",children:[e.jsx("button",{onClick:()=>{D(!1),G(null)},className:"w-full rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:"Cancel"}),e.jsx(le,{onClick:Me,loading:oe,disabled:!x||(x==null?void 0:x.price)>0&&!J,className:`w-full rounded-lg px-4 py-2 sm:w-auto ${x&&((x==null?void 0:x.price)===0||J)?"bg-blue-600 text-white hover:bg-blue-700":"cursor-not-allowed bg-gray-300 text-gray-500"}`,children:(x==null?void 0:x.price)===0?"Activate Free Plan":J?"Complete Purchase":"Add payment method to continue"})]}),(x==null?void 0:x.price)>0&&!J&&e.jsx("div",{className:"mt-4 rounded-lg bg-yellow-50 p-4",children:e.jsxs("div",{className:"flex",children:[e.jsx("svg",{className:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Payment method required"}),e.jsx("p",{className:"mt-1 text-sm text-yellow-700",children:"Please add a payment method to your account before purchasing a paid plan."})]})]})})]})]})]})}let xs=new X;function ps(){const{dispatch:y}=B.useContext(W),[N,S]=a.useState({}),[j,C]=a.useState([]);a.useState(0),a.useState(!1),a.useState(!1);const[E,h]=a.useState(""),[g,p]=a.useState(""),[I,Z]=a.useState(""),[k,Y]=a.useState("desc"),[A,F]=a.useState(!1),[f,v]=a.useState(null),w=a.useRef({}),T=s=>{S(l=>({...l,[s]:!l[s]}))},b=async(s={})=>{F(!0);try{let l=new URLSearchParams;s.sort&&l.append("sort",s.sort),s.invoice_type&&l.append("invoice_type",s.invoice_type),s.search&&l.append("search",s.search);const i=await xs.callRawAPI(`/v3/api/custom/courtmatchup/user/billing/invoices${l.toString()?`?${l.toString()}`:""}`,{},"GET");if(i.error){u(y,i.message,5e3);return}C(i.invoices||[])}catch(l){console.error("ERROR",l),u(y,l.message,5e3),O(dispatch,l.message)}finally{F(!1)}},$=s=>{h(s.target.value),b({sort:k})},z=()=>!g&&!I?j:j.filter(s=>{try{const l=new Date(s.date);if(isNaN(l.getTime()))return!1;const i=g?new Date(g):null,o=I?new Date(I):null;return i&&o?l>=i&&l<=o:i?l>=i:o?l<=o:!0}catch{return console.error("Invalid date:",s.date),!1}}),L=()=>{const s=k==="asc"?"desc":"asc";Y(s),b({sort:s})};a.useEffect(()=>{b({sort:k})},[]);const M=s=>new Date(s).toLocaleDateString(),_=s=>Number(s).toLocaleString("en-US",{style:"currency",currency:"usd"}),R=s=>{v(s);const l=window.open("","_blank");if(!l){u(y,"Please allow pop-ups to print receipts",5e3);return}if(!w.current[s]){u(y,"Receipt content not found",5e3);return}const o=j.find(H=>H.id===s);l.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt #${o.receipt_id}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .receipt-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 1px solid #eee;
              padding-bottom: 20px;
            }
            .receipt-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .receipt-id {
              font-size: 16px;
              color: #666;
            }
            .receipt-date {
              font-size: 14px;
              color: #666;
              margin-top: 5px;
            }
            .receipt-body {
              margin-bottom: 30px;
            }
            .receipt-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
              padding: 5px 0;
              border-bottom: 1px solid #f5f5f5;
            }
            .receipt-label {
              font-weight: 500;
              color: #666;
            }
            .receipt-total {
              margin-top: 20px;
              font-size: 18px;
              font-weight: bold;
              border-top: 2px solid #eee;
              padding-top: 10px;
            }
            .receipt-footer {
              margin-top: 40px;
              text-align: center;
              font-size: 14px;
              color: #999;
            }
            @media print {
              body {
                padding: 0;
                margin: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-header">
            <div class="receipt-title">Payment Receipt</div>
            <div class="receipt-id">Invoice #${o.receipt_id}</div>
            <div class="receipt-date">Date: ${M(o.date)}</div>
          </div>

          <div class="receipt-body">
            <div class="receipt-row">
              <span class="receipt-label">Type:</span>
              <span>${o.type}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Amount:</span>
              <span>${_(o.amount)}</span>
            </div>
            ${o.coach_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Coach Fee:</span>
              <span>${_(o.coach_fee)}</span>
            </div>
            `:""}
            ${o.service_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Service Fee:</span>
              <span>${_(o.service_fee)}</span>
            </div>
            `:""}
            ${o.club_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Club Fee:</span>
              <span>${_(o.club_fee)}</span>
            </div>
            `:""}
            <div class="receipt-row">
              <span class="receipt-label">Valid Until:</span>
              <span>${M(o.valid_until)}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Payment Method:</span>
              <span>${o.payment_method}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Status:</span>
              <span class="capitalize">${o.status}</span>
            </div>

            <div class="receipt-total">
              <div class="receipt-row">
                <span class="receipt-label">Total:</span>
                <span>${_(o.total_amount)}</span>
              </div>
            </div>
          </div>

          <div class="receipt-footer">
            Thank you for your payment.
          </div>

          <script>
            window.onload = function() {
              window.print();
              setTimeout(function() {
                window.close();
              }, 500);
            };
          <\/script>
        </body>
      </html>
    `),l.document.close(),setTimeout(()=>{v(null)},1e3)};return e.jsxs("div",{className:"mx-auto max-w-3xl p-4 sm:p-6",children:[A&&e.jsx(ne,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Billing"}),e.jsxs("select",{className:"w-full rounded-lg border-gray-200 px-4 py-2 capitalize sm:w-auto",onChange:s=>b({invoice_type:s.target.value,sort:k}),children:[e.jsx("option",{value:"",children:"All bills"}),e.jsx("option",{value:"subscription",children:"Subscription"}),e.jsx("option",{value:"lesson",children:"Lesson"}),e.jsx("option",{value:"clinic",children:"Clinic"}),e.jsx("option",{value:"club_court",children:"Club Court"})]})]}),e.jsx("div",{className:"mb-5 border-b border-gray-200"}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(He,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:E,onChange:$,placeholder:"Search by plan name or invoice number",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:g,onChange:s=>p(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:I,onChange:s=>Z(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(g||I)&&e.jsx("button",{onClick:()=>{p(""),Z("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:L,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:[k==="asc"?"Oldest first":"Latest first",e.jsx(as,{className:`transform ${k==="desc"?"rotate-180":""}`})]})]}),e.jsx("div",{className:"space-y-4",children:z().length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No billing records found"}):z().map(s=>e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>T(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ve,{className:`transform transition-transform ${N[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.type})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:M(s.create_at)}),e.jsx("span",{className:"font-medium",children:_(s.total_amount)})]})]}),N[s.id]&&e.jsx("div",{className:"mt-2 rounded-lg bg-white p-4",children:e.jsxs("div",{className:"space-y-4",ref:l=>w.current[s.id]=l,children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Amount"}),e.jsx("span",{children:_(s.amount)})]}),s.coach_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Coach fee"}),e.jsx("span",{children:_(s.coach_fee)})]}),s.service_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Service fee"}),e.jsx("span",{children:_(s.service_fee)})]}),s.club_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Club fee"}),e.jsx("span",{children:_(s.club_fee)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice ID"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Date"}),e.jsx("span",{children:M(s.date)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{children:M(s.valid_until)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Status"}),e.jsx("span",{className:"capitalize",children:s.status})]}),e.jsxs("button",{onClick:l=>{l.stopPropagation(),R(s.id)},className:"mt-4 flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50",children:[e.jsx(Ye,{className:"text-lg"}),"Print Receipt"]})]})})]},s.id))})]})}const rt=()=>{const{dispatch:y}=B.useContext(W),[N]=De(),[S,j]=a.useState("profile"),C=Be();a.useEffect(()=>{const h=N.get("tab");h&&j({"payment-methods":"payment-methods",profile:"profile",membership:"membership",billing:"billing"}[h]||"profile")},[N.get("tab")]);const E=[{label:"Profile details",value:"profile",icon:Je},{label:"Payment methods",value:"payment-methods",icon:Qe},{label:"Membership",value:"membership",icon:Xe},{label:"Billing",value:"billing",icon:We}];return a.useEffect(()=>{y({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(rs,{onBack:()=>C("/user/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:E.map(h=>{const g=h.icon;return e.jsxs("button",{onClick:()=>{j(h.value),C(`/user/profile?tab=${h.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${S===h.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(g,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:h.label})]},h.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[S==="profile"&&e.jsx(ns,{}),S==="payment-methods"&&e.jsx(cs,{}),S==="membership"&&e.jsx(ms,{}),S==="billing"&&e.jsx(ps,{})]})]})]})})};export{rt as default};
