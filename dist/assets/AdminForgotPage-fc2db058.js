import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as S,b as F,L}from"./vendor-851db8c1.js";import{u as B}from"./react-hook-form-687afde5.js";import{o as C}from"./yup-2824f222.js";import{c as R,a as $}from"./yup-54691517.js";import{G as q,d as A,M as D,b as G,t as M}from"./index-9f98cff7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const ye=()=>{var l,n;const[i,a]=S.useState(!1),y=R({email:$().email().required()}).required(),{register:j,handleSubmit:N,setError:m,formState:{errors:s}}=B({resolver:C(y)}),{dispatch:r}=F.useContext(q),k=async v=>{var d,p,c,u,x,g,b,f;let E=new D;try{a(!0);const e=await E.forgot(v.email,admin);if(!e.error)G(r,"Reset Code Sent");else if(e.validation){const h=Object.keys(e.validation);for(let o=0;o<h.length;o++){const w=h[o];m(w,{type:"manual",message:e.validation[w]})}}a(!1)}catch(e){a(!1),console.log("Error",e),m("email",{type:"manual",message:(p=(d=e==null?void 0:e.response)==null?void 0:d.data)!=null&&p.message?(u=(c=e==null?void 0:e.response)==null?void 0:c.data)==null?void 0:u.message:e==null?void 0:e.message}),M(r,(g=(x=e==null?void 0:e.response)==null?void 0:x.data)!=null&&g.message?(f=(b=e==null?void 0:e.response)==null?void 0:b.data)==null?void 0:f.message:e==null?void 0:e.message)}};return t.jsx(t.Fragment,{children:t.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[t.jsxs("form",{onSubmit:N(k),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),t.jsx("input",{type:"email",placeholder:"Email",...j("email"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${s&&((l=s.email)!=null&&l.message)?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:s&&((n=s.email)==null?void 0:n.message)})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx(A,{className:"bg-primaryBlue disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",type:"submit",loading:i,disabled:i,children:"Forgot Password"}),t.jsx(L,{className:"inline-block align-baseline font-bold text-sm text-primaryBlue",to:"/admin/login",children:"Login?"})]})]}),t.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{ye as default};
