import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as Se,r as u}from"./vendor-851db8c1.js";import{M as ye,T as ve,G as be,A as je,c as ke,i as O,E as _e,V as Ce,W as Ne,t as De,X as Te,Y as z,v as Ee,Z as S,_ as Pe}from"./index-a0784e19.js";import{c as Re,a as m}from"./yup-54691517.js";import{u as Ae}from"./react-hook-form-687afde5.js";import{o as Fe}from"./yup-2824f222.js";import{P as Le}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import $e from"./Skeleton-1e8bf077.js";import{A as Oe,C as ze}from"./CheckinModal-239c1dd6.js";import{L as Me}from"./LoadingOverlay-87926629.js";import{P as Ge,R as Ie,T as Ve,F as Be}from"./ReservationStatus-5ced670f.js";import{D as qe}from"./DataTable-a2248415.js";import{H as He}from"./HistoryComponent-0c9f35b4.js";let M=new ye,Ke=new ve;const Ue=[{header:"Date",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"By",accessor:"user",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Sport",accessor:"sport",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{1:"Paid",0:"Reserved",2:"Failed"}}],Je=({sports:d,club:o,courts:y})=>{const{dispatch:v,state:We}=a.useContext(be),{dispatch:G}=a.useContext(je),[I,V]=a.useState([]),[c,b]=a.useState(10),[B,q]=a.useState(0),[Xe,H]=a.useState(0),[p,K]=a.useState(1),[U,J]=a.useState(!1),[W,X]=a.useState(!1),[Ye,Y]=a.useState(!1);a.useState(!1),a.useState([]),a.useState([]),a.useState("eq");const[g,f]=a.useState(!0),[Z,Ze]=a.useState(!1),[Q,Qe]=a.useState(!1);a.useState(),Se();const j=a.useRef(null),[w,k]=a.useState(!1),[x,h]=a.useState(null),[ee,_]=u.useState(!1),[se,te]=u.useState(!1),[ae,ie]=u.useState(!1),[C,ne]=u.useState([]),le=Re({id:m(),email:m(),role:m(),status:m()}),{register:re,handleSubmit:oe,formState:{errors:we}}=Ae({resolver:Fe(le)});function ce(){n(p-1,c)}function de(){n(p+1,c)}const pe=async()=>{try{M.setTable("user");const e=await M.callRestAPI({filter:[`club_id,eq,${o==null?void 0:o.id}`,"role,cs,user"]},"GETALL");ne(e.list||[])}catch(e){console.error("Error fetching players:",e),showToast(v,"Error fetching players",3e3,"error")}};async function n(e,t,l={},r=[]){f(!(Q||Z));try{const i=await Ke.getPaginate("reservation",{page:e,limit:t,filter:[...r,`courtmatchup_reservation.club_id,cs,${o==null?void 0:o.id}`],join:["clubs|club_id","booking|booking_id","user|user_id"],size:c});i&&(f(!1),V(i.list),b(i.limit),q(i.num_pages),K(i.page),H(i.total),J(i.page>1),X(i.page+1<=i.num_pages))}catch(i){f(!1),console.log("ERROR",i),De(G,i.message)}}const ue=e=>{e.search?n(1,c,{},[`first_name,cs,${e.search}`,`last_name,cs,${e.search}`]):n(1,c)},me=async e=>{const t=e.target.value;t===""?await n(1,c):await n(1,c,{},[`sport_id,eq,${parseInt(t)}`])},ge=async e=>{e.target.value===""?await n(p,c):await n(p,c,{},[`courtmatchup_booking.reservation_type,cs,${e.target.value}`])},fe=async e=>{e.target.value===""?await n(p,c):await n(p,c,{},[`courtmatchup_booking.status,cs,${e.target.value}`])};a.useEffect(()=>{v({type:"SETPATH",payload:{path:"reservations"}}),o!=null&&o.id&&(n(1,c,{}),pe())},[o==null?void 0:o.id]);const N=e=>{j.current&&!j.current.contains(e.target)&&Y(!1)};a.useEffect(()=>(document.addEventListener("mousedown",N),()=>{document.removeEventListener("mousedown",N)}),[]);const xe=e=>{var l,r,i,D,T,E,P,R,A,F,L,$;const t={...e,id:(l=e.booking)==null?void 0:l.id,date:(r=e.booking)==null?void 0:r.date,startTime:(i=e.booking)==null?void 0:i.start_time,endTime:(D=e.booking)==null?void 0:D.end_time,sport_id:(T=e.booking)==null?void 0:T.sport_id,type:(E=e.booking)==null?void 0:E.type,sub_type:(P=e.booking)==null?void 0:P.subtype,reservation_type:(R=e.booking)==null?void 0:R.reservation_type,price:(A=e.booking)==null?void 0:A.price,status:(F=e.booking)==null?void 0:F.status,player_ids:(L=e.booking)==null?void 0:L.player_ids,coach_ids:($=e.booking)==null?void 0:$.coach_ids};h(t),k(!0)},he={type:e=>{var t;return s.jsx("span",{className:"capitalize",children:((t=O.find(l=>{var r;return l.value==((r=e==null?void 0:e.booking)==null?void 0:r.reservation_type)}))==null?void 0:t.label)||"--"})},sport:e=>{var t;return s.jsx("span",{className:"capitalize",children:((t=d.find(l=>{var r;return l.id===((r=e==null?void 0:e.booking)==null?void 0:r.sport_id)}))==null?void 0:t.name)||"--"})},date:e=>{var t,l,r;return s.jsxs(s.Fragment,{children:[Te((t=e==null?void 0:e.booking)==null?void 0:t.date)," "," | "," ",z((l=e==null?void 0:e.booking)==null?void 0:l.start_time)," "," - "," ",z((r=e==null?void 0:e.booking)==null?void 0:r.end_time)]})},players:e=>{var t,l;return s.jsx(s.Fragment,{children:(t=e==null?void 0:e.booking)!=null&&t.player_ids?`${JSON.parse((l=e==null?void 0:e.booking)==null?void 0:l.player_ids).length} players`:"0 players"})},bill:e=>{var t;return s.jsx(s.Fragment,{children:Ee((t=e==null?void 0:e.booking)==null?void 0:t.price)})},user:e=>{var t,l,r,i;return s.jsx(s.Fragment,{children:!((t=e==null?void 0:e.user)!=null&&t.first_name)||!((l=e==null?void 0:e.user)!=null&&l.last_name)?"--":`${(r=e==null?void 0:e.user)==null?void 0:r.first_name} ${(i=e==null?void 0:e.user)==null?void 0:i.last_name}`})},status:e=>s.jsxs(s.Fragment,{children:[e.booking.status==S.SUCCESS&&s.jsx(Ge,{}),e.booking.status==S.PENDING&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Ie,{}),s.jsx(Ve,{timeLeft:Pe(e==null?void 0:e.reservation_updated_at)})]}),e.booking.status==S.FAIL&&s.jsx(Be,{})]})};return s.jsxs("div",{className:"h-screen px-8",children:[g&&s.jsx(Me,{}),s.jsxs("div",{className:"flex flex-col gap-6 py-3",children:[s.jsx("div",{className:"w-full max-w-xl",children:s.jsxs("form",{className:"relative flex items-center",onSubmit:oe(ue),children:[s.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:s.jsx(ke,{className:"text-gray-500"})}),s.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search reservations...",...re("search")})]})}),s.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[s.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[s.jsx("input",{type:"date",className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500"}),s.jsx("input",{type:"time",defaultValue:"00:00",className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500"})]}),s.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"Sport: All",onChange:me,children:[s.jsx("option",{value:"",children:"Sport: All"}),d==null?void 0:d.map(e=>s.jsx("option",{value:e.id,children:e.name},e.id))]}),s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"All",onChange:fe,children:[s.jsx("option",{value:"",children:"Status: All"}),s.jsx("option",{value:"0",children:"Reserved"}),s.jsx("option",{value:"1",children:"Paid"}),s.jsx("option",{value:"2",children:"Failed"})]}),s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"All",onChange:ge,children:[s.jsx("option",{value:"",children:"Reservation Type: All"}),O.map(e=>s.jsx("option",{value:e.value,children:e.label},e.value))]})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs("button",{onClick:()=>_(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[s.jsx("span",{children:"+"}),"Add new"]}),s.jsx(He,{activityType:_e.court_reservation,emptyMessage:"No reservations found",title:"Reservations"})]})]})]}),g?s.jsx($e,{}):s.jsx("div",{className:"overflow-x-auto",children:s.jsx(qe,{columns:Ue,data:I,loading:g,renderCustomCell:he,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",cellClassName:"whitespace-nowrap px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",onClick:e=>xe(e)})}),s.jsx(Le,{currentPage:p,pageCount:B,pageSize:c,canPreviousPage:U,canNextPage:W,updatePageSize:e=>{b(e),n(1,e)},previousPage:ce,nextPage:de,gotoPage:e=>n(e,c)}),s.jsx(Ce,{isOpen:w,onClose:()=>k(!1),event:x,users:C,sports:d,club:o,fetchData:n,courts:y}),s.jsx(Oe,{isOpen:ee,club:o,onClose:()=>_(!1),sports:d,players:C}),se&&s.jsx(ze,{courts:y,onClose:()=>te(!1),reservation:x,getData:n,sports:d,setReservation:h}),ae&&s.jsx(Ne,{reservation:x,onClose:()=>ie(!1),getData:n,setReservation:h})]})},gs=Je;export{gs as L};
