import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as t,f as n,r as c}from"./vendor-851db8c1.js";import{M as u,A as f,G as h,L as w}from"./index-a0784e19.js";import"./index-be4468eb.js";import{M as x}from"./index-2dd2e3aa.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";new u;const S=[{header:"Header",accessor:"header",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],ie=()=>{t.useContext(f),t.useContext(h),n();const[b,r]=t.useState(!1),[g,l]=t.useState(!1),[j,m]=t.useState(),p=c.useRef(null),[A,d]=t.useState([]),s=(o,i,a=[])=>{switch(o){case"add":r(i);break;case"edit":l(i),d(a),m(a[0]);break}};return e.jsx(e.Fragment,{children:e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(w,{children:e.jsx(x,{columns:S,tableRole:"user",table:"pages",actionId:"id",actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!1,multiple:!1,action:o=>s("edit",!0,o)},delete:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>s("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:p})})})})})};export{ie as default};
