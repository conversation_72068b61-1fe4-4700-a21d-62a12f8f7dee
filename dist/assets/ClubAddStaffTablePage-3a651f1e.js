import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as o,f as E}from"./vendor-851db8c1.js";import{u as I}from"./react-hook-form-687afde5.js";import{o as k}from"./yup-2824f222.js";import{c as D,a as n}from"./yup-54691517.js";import{G as C,A as F,d as P,M as R,b as T,t as L}from"./index-9f98cff7.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as _}from"./MkdInput-ebcf18ce.js";import"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Ne=({setSidebar:j})=>{var x,h,g,y;const{dispatch:d}=o.useContext(C),v=D({user_id:n(),club_id:n(),permission_level:n(),account_details:n()}).required(),{dispatch:w}=o.useContext(F),[u,M]=o.useState({}),[f,c]=o.useState(!1),S=E(),{register:l,handleSubmit:N,setError:b,setValue:O,formState:{errors:s}}=I({resolver:k(v)});o.useState([]);const A=async r=>{let p=new R;c(!0);try{for(let i in u){let a=new FormData;a.append("file",u[i].file);let m=await p.uploadImage(a);r[i]=m.url}p.setTable("staff");const t=await p.callRestAPI({user_id:r.user_id,club_id:r.club_id,permission_level:r.permission_level,account_details:r.account_details},"POST");if(!t.error)T(d,"Added"),S("/club/staff"),j(!1),d({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const i=Object.keys(t.validation);for(let a=0;a<i.length;a++){const m=i[a];b(m,{type:"manual",message:t.validation[m]})}}c(!1)}catch(t){c(!1),console.log("Error",t),b("user_id",{type:"manual",message:t.message}),L(w,t.message)}};return o.useEffect(()=>{d({type:"SETPATH",payload:{path:"staff"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Staff"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:N(A),children:[e.jsx(_,{type:"number",page:"add",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:l,className:""}),e.jsx(_,{type:"number",page:"add",name:"club_id",errors:s,label:"Club Id",placeholder:"Club Id",register:l,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"permission_level",children:"Permission Level"}),e.jsx("textarea",{placeholder:"Permission Level",...l("permission_level"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(x=s.permission_level)!=null&&x.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(h=s.permission_level)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"account_details",children:"Account Details"}),e.jsx("textarea",{placeholder:"Account Details",...l("account_details"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(g=s.account_details)!=null&&g.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(y=s.account_details)==null?void 0:y.message})]}),e.jsx(P,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{Ne as default};
