import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as H,b as F}from"./vendor-851db8c1.js";import{v as o,A as ce,G as xe}from"./index-a0784e19.js";import{M as te,H as ue}from"./HeatMapChart-da4e0846.js";import{R as b,P as se,a as re,C as ne,T as y,L as k,B as oe,b as de,X as ae,Y as he,c as M}from"./recharts-614cb831.js";import{M as ge}from"./react-tooltip-7a26650a.js";const N=["#8884d8","#82ca9d","#FFBB28","#FF8042"],we=({stats:i})=>{var C;const[u,m]=H.useState(!1),[j,g]=H.useState(!1),a=(C=i.revenueByModule)==null?void 0:C.map(t=>({name:t.module,value:Number(t.revenue)||0})),w=F.useMemo(()=>{if(!(i!=null&&i.revenueByDay))return[];const t=i.revenueByDay.reduce((d,n)=>(d[n.day]||(d[n.day]={day:n.day,clinic:0,coach:0}),d[n.day][n.module]=Number(n.revenue)||0,d),{}),s=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];return Object.values(t).sort((d,n)=>s.indexOf(d.day)-s.indexOf(n.day))},[i==null?void 0:i.revenueByDay]),v=({title:t,children:s,onFullView:d})=>e.jsxs("div",{className:"rounded-2xl bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-base font-semibold text-gray-800",children:t}),e.jsx("button",{onClick:d,className:"",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.75 12.75V17.25C5.75 17.8023 6.19772 18.25 6.75 18.25H11.25M12.75 5.75H17.25C17.8023 5.75 18.25 6.19772 18.25 6.75V11.25",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]}),s]}),p=({active:t,payload:s,label:d})=>t&&s&&s.length?e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-white p-3 shadow-lg",children:[e.jsx("p",{className:"font-medium text-gray-900",children:d}),s.map((n,f)=>e.jsxs("p",{style:{color:n.color},children:[n.name,": ",o(n.value)]},f))]}):null;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsx(v,{title:"Revenue Made Through Each Module",onFullView:()=>m(!0),children:e.jsx("div",{className:"h-[300px]",children:e.jsx(b,{width:"100%",height:"100%",children:e.jsxs(se,{children:[e.jsx(re,{data:a,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",fill:"#8884d8",label:({name:t,value:s})=>`${t}: ${o(s)}`,children:a==null?void 0:a.map((t,s)=>e.jsx(ne,{fill:N[s%N.length]},`cell-${s}`))}),e.jsx(y,{formatter:t=>o(t)}),e.jsx(k,{})]})})})}),e.jsx(v,{title:"Revenue from Each Module",onFullView:()=>g(!0),children:e.jsx("div",{className:"h-[300px]",children:e.jsx(b,{width:"100%",height:"100%",children:e.jsxs(oe,{data:w,children:[e.jsx(de,{strokeDasharray:"3 3"}),e.jsx(ae,{dataKey:"day"}),e.jsx(he,{tickFormatter:t=>o(t)}),e.jsx(y,{content:e.jsx(p,{})}),e.jsx(k,{}),e.jsx(M,{dataKey:"clinic",name:"Clinic",fill:"#8884d8"}),e.jsx(M,{dataKey:"coach",name:"Coach",fill:"#82ca9d"})]})})})})]}),e.jsx(te,{isOpen:u,onClose:()=>m(!1),title:"Module Distribution",children:e.jsx("div",{className:"h-[600px] w-full",children:e.jsx(b,{width:"100%",height:"100%",children:e.jsxs(se,{children:[e.jsx(re,{data:a,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",fill:"#8884d8",label:({name:t,value:s})=>`${t}: ${o(s)}`,children:a==null?void 0:a.map((t,s)=>e.jsx(ne,{fill:N[s%N.length]},`cell-${s}`))}),e.jsx(y,{formatter:t=>o(t)}),e.jsx(k,{})]})})})}),e.jsx(te,{isOpen:j,onClose:()=>g(!1),title:"Revenue Distribution",children:e.jsx("div",{className:"h-[600px] w-full",children:e.jsx(b,{width:"100%",height:"100%",children:e.jsxs(oe,{data:w,children:[e.jsx(de,{strokeDasharray:"3 3"}),e.jsx(ae,{dataKey:"day"}),e.jsx(he,{tickFormatter:t=>o(t)}),e.jsx(y,{content:e.jsx(p,{})}),e.jsx(k,{}),e.jsx(M,{dataKey:"clinic",name:"Clinic",fill:"#8884d8"}),e.jsx(M,{dataKey:"coach",name:"Coach",fill:"#82ca9d"})]})})})})]})},be=({stats:i,fetchDashboardStats:u})=>{var s,d,n,f,B,E,_,D,V,Z,R,S,A,T,L,O,z,K,P,$,Y,I,U,G,W,X,q,J,Q,ee,ie,le;F.useContext(ce);const{dispatch:m}=F.useContext(xe),[j,g]=H.useState(new Date),[a,w]=H.useState(new Date);console.log("model",i);const v=l=>{const h=l.target.value;g(new Date(h)),u(h,a.toISOString().split("T")[0])},p=l=>{const h=l.target.value;w(new Date(h)),u(j.toISOString().split("T")[0],h)},C=l=>{const h=l.target.value,r=new Date;let x=new Date,c=new Date;switch(h){case"this_day":x=new Date(r.setHours(0,0,0,0)),c=new Date(r.setHours(23,59,59,999));break;case"this_week":x=new Date(r.setDate(r.getDate()-r.getDay())),x.setHours(0,0,0,0),c=new Date(r.setDate(r.getDate()-r.getDay()+6)),c.setHours(23,59,59,999);break;case"this_month":x=new Date(r.getFullYear(),r.getMonth(),1),c=new Date(r.getFullYear(),r.getMonth()+1,0),c.setHours(23,59,59,999);break;case"this_year":x=new Date(r.getFullYear(),0,1),c=new Date(r.getFullYear(),11,31),c.setHours(23,59,59,999);break;default:return}g(x),w(c),u(x,c)};F.useEffect(()=>{m({type:"SETPATH",payload:{path:"dashboard"}})},[]);const t=[{title:"Coach reservations",stats:[{label:"Hours",value:((d=(s=i==null?void 0:i.coachReservations)==null?void 0:s[0])==null?void 0:d.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{label:"Revenue",value:o(Number((f=(n=i==null?void 0:i.coachReservations)==null?void 0:n[0])==null?void 0:f.total_revenue)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}]},{title:"Lesson reservations",stats:[{label:"Hours",value:((E=(B=i==null?void 0:i.lessonReservations)==null?void 0:B[0])==null?void 0:E.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{label:"Revenue",value:o(Number((D=(_=i==null?void 0:i.lessonReservations)==null?void 0:_[0])==null?void 0:D.total_revenue)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}]},{title:"Programs/Clinics",stats:[{label:"Hours",value:((Z=(V=i==null?void 0:i.clinicReservations)==null?void 0:V[0])==null?void 0:Z.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{label:"Revenue",value:o(Number((S=(R=i==null?void 0:i.clinicReservations)==null?void 0:R[0])==null?void 0:S.total_revenue)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}]},{title:"Find a buddy module",id:"find_a_buddy_module",stats:[{label:"Hours",value:((T=(A=i==null?void 0:i.buddyStatistics)==null?void 0:A[0])==null?void 0:T.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{label:"Revenue",value:o(((O=(L=i==null?void 0:i.buddyStatistics)==null?void 0:L[0])==null?void 0:O.total_revenue)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}]},{title:"Total revenue",stats:[{label:"Hours",value:((K=(z=i==null?void 0:i.totalRevenue)==null?void 0:z[0])==null?void 0:K.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{label:"Revenue",value:o(Number(($=(P=i==null?void 0:i.totalRevenue)==null?void 0:P[0])==null?void 0:$.total_revenue)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}]},{title:"Total expenses",id:"total_expenses",stats:[{label:"Hours",value:((I=(Y=i==null?void 0:i.totalExpenses)==null?void 0:Y[0])==null?void 0:I.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{label:"Expense",value:o(Number((G=(U=i==null?void 0:i.totalExpenses)==null?void 0:U[0])==null?void 0:G.total_expense)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}]},{title:"Total profit",id:"total_profit",stats:[{label:"Hours",value:((X=(W=i==null?void 0:i.totalProfit)==null?void 0:W[0])==null?void 0:X.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{label:"Profit",value:o(Number((J=(q=i==null?void 0:i.totalProfit)==null?void 0:q[0])==null?void 0:J.total_profit)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}]},{title:"Court utilization",id:"court_utilization",stats:[{label:"Hours",value:((ee=(Q=i==null?void 0:i.courtUtilization)==null?void 0:Q[0])==null?void 0:ee.total_used_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{label:"Revenue",value:o(((le=(ie=i==null?void 0:i.courtUtilization)==null?void 0:ie[0])==null?void 0:le.utilization_percentage)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}]}];return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-5 ",children:[e.jsx("input",{type:"date",onChange:v,value:j.toISOString().split("T")[0],className:"rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"}),"-",e.jsx("input",{type:"date",onChange:p,value:a.toISOString().split("T")[0],className:"rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsx("div",{children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("div",{className:"relative",children:[e.jsxs("select",{onChange:C,className:"flex appearance-none items-center gap-2 rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-8 text-sm text-gray-500",children:[e.jsx("option",{value:"this_day",children:"This day"}),e.jsx("option",{value:"this_week",children:"This week"}),e.jsx("option",{value:"this_month",children:"This month"}),e.jsx("option",{value:"this_year",children:"This year"})]}),e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-3 flex items-center",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.875 3.125H4.125C3.57272 3.125 3.125 3.57272 3.125 4.125V6.25245C3.125 6.51767 3.23036 6.77202 3.41789 6.95956L7.83211 11.3738C8.01964 11.5613 8.125 11.8157 8.125 12.0809V17.7083L11.875 16.6667V12.0809C11.875 11.8157 11.9804 11.5613 12.1679 11.3738L16.5821 6.95956C16.7696 6.77202 16.875 6.51767 16.875 6.25245V4.125C16.875 3.57272 16.4273 3.125 15.875 3.125Z",stroke:"#868C98","stroke-width":"1.5","stroke-linejoin":"round"})})}),e.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-2 flex items-center"})]})})})]}),e.jsx("div",{className:"grid grid-cols-1 items-center justify-center gap-6 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4",children:t.map(l=>{var h,r;return l.id==="court_utilization"?e.jsxs("div",{className:"relative mx-auto h-full w-full rounded-2xl bg-white p-6 shadow-sm md:max-w-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-gray-800",children:l.title}),e.jsx("div",{className:"flex h-[100px] items-center justify-center",children:e.jsx("p",{className:"text-6xl font-semibold",children:(r=(h=i==null?void 0:i.courtUtilization)==null?void 0:h[0])!=null&&r.utilization_percentage?`${i.courtUtilization[0].utilization_percentage}%`:"0%"})})]},l.id):l.id==="find_a_buddy_module"?e.jsxs("div",{className:"relative mx-auto w-full rounded-2xl bg-white p-6 shadow-sm md:max-w-sm",children:[e.jsxs("div",{className:"mb-4 flex items-center gap-2",children:[e.jsx("h2",{className:" text-lg font-semibold text-gray-800",children:l.title}),e.jsx("span",{"data-tooltip-id":"find-a-buddy-tooltip",children:e.jsxs("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M8.0625 8.25H8.99999L9 12.1875M15.9375 9C15.9375 12.8315 12.8315 15.9375 9 15.9375C5.16852 15.9375 2.0625 12.8315 2.0625 9C2.0625 5.16852 5.16852 2.0625 9 2.0625C12.8315 2.0625 15.9375 5.16852 15.9375 9Z",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M9 5.5C8.72386 5.5 8.5 5.72386 8.5 6C8.5 6.27614 8.72386 6.5 9 6.5C9.27614 6.5 9.5 6.27614 9.5 6C9.5 5.72386 9.27614 5.5 9 5.5Z",fill:"#525866",stroke:"#525866","stroke-width":"0.25"})]})})]}),e.jsx(ge,{id:"find-a-buddy-tooltip",place:"top",className:"z-50 max-w-md rounded-lg border border-gray-200 bg-white p-4 text-gray-700 shadow-lg",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Only counts hours and revenue when Find a buddy request made on Find a buddy module and the court is booked through that Module"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col items-center gap-3 text-center",children:[e.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:l.stats[0].icon}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:l.stats[0].label}),e.jsx("p",{className:"text-xl font-semibold",children:l.stats[0].value})]})]}),e.jsx("div",{className:"h-20 w-[1px] bg-gray-200"}),e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3 text-center",children:[e.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:l.stats[1].icon}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:l.stats[1].label}),e.jsxs("p",{className:"text-xl font-semibold",children:[l.stats[1].value,".00"]})]})]})]})]},l.id):l.id==="total_expenses"||l.id==="total_profit"?e.jsxs("div",{className:"relative mx-auto w-full rounded-2xl bg-white p-6 shadow-sm md:max-w-sm",children:[e.jsx("div",{className:"mb-4 flex items-center gap-2",children:e.jsx("h2",{className:" text-lg font-semibold text-gray-800",children:l.title})}),e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3 text-center",children:[e.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:l.stats[1].icon}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:l.stats[1].label}),e.jsxs("p",{className:"text-xl font-semibold",children:[l.stats[1].value,".00"]})]})]})})]},l.id):e.jsxs("div",{className:"mx-auto w-full rounded-2xl bg-white p-6 shadow-sm md:max-w-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-gray-800",children:l.title}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3 text-center",children:[e.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:l.stats[0].icon}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:l.stats[0].label}),e.jsx("p",{className:"text-xl font-semibold",children:l.stats[0].value})]})]}),e.jsx("div",{className:"h-20 w-[1px] bg-gray-200"}),e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3 text-center",children:[e.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:l.stats[1].icon}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-600",children:l.stats[1].label}),e.jsxs("p",{className:"text-xl font-semibold",children:[l.stats[1].value,".00"]})]})]})]})]},l.id)})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 xl:grid-cols-3",children:[e.jsx("div",{className:"col-span-2",children:e.jsx(we,{stats:i})}),e.jsx("div",{className:"col-span-1",children:e.jsx(ue,{stats:i})})]})]})})};export{be as C};
