import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,u as ue,f as xe,r as a}from"./vendor-851db8c1.js";import{u as pe}from"./react-hook-form-687afde5.js";import{o as fe}from"./yup-2824f222.js";import{c as he,a as n}from"./yup-54691517.js";import{M as ge,G as ye,A as be,a5 as G,d as Se,b as y,a8 as je,H as ve,E as Ne,J as we}from"./index-9f98cff7.js";import"./react-quill-73fb9518.js";/* empty css                   */import{C as Ce}from"./Calendar-9031b5fe.js";import{B as _e}from"./BackButton-11ba52b2.js";import{S as Te}from"./SuccessModal-e9ef416e.js";import{T as ke}from"./TimeSlots-9a2fb5c0.js";import{S as Me}from"./SportTypeSelection-ee0cc3da.js";import{B as Be}from"./ReservationSummary-ad645e94.js";import{f as Pe}from"./date-fns-07266b7d.js";let _=new ge;const Xe=({setSidebar:qe,users:T,coaches:Le,role:Q,club:l,sports:u})=>{const{dispatch:x,state:Re}=r.useContext(ye),i=ue();console.log("locationss",i);const K=he({club_id:n(),user_id:n(),sport:n(),level:n(),start_time:n(),end_time:n(),num_players_needed:n(),players:n(),bio:n(),status:n()}).required();r.useContext(be),r.useState({});const[k,M]=r.useState(!1),[U,b]=r.useState(!1),S=xe();pe({resolver:fe(K)}),r.useState([]);const[c,V]=r.useState(new Date),[W,X]=r.useState(new Date),[B,j]=r.useState("calendar"),[v,Y]=a.useState([]),[p,ee]=a.useState(null),[o,P]=a.useState([]),[q,te]=a.useState(2.5),[L,se]=a.useState(5),[R,ae]=a.useState(1),[A,le]=a.useState(1),[E,re]=a.useState(""),[O,ne]=a.useState(""),[Ae,D]=a.useState([]),[f,N]=a.useState(null),[Ee,$]=a.useState([]),[F,h]=a.useState(null),[w,ie]=a.useState([]),C=T.filter(e=>`${e.first_name||""} ${e.last_name||""}`.toLowerCase().includes(O.toLowerCase()));r.useEffect(()=>{x({type:"SETPATH",payload:{path:"find_a_buddy_requests"}})},[]);const oe=e=>{Y([{from:e.from,until:e.until}])},de=e=>{o.includes(e)?P(o.filter(s=>s!==e)):P([...o,e])},ce=async()=>{var e,s,m,I,J,z;try{if(M(!0),!p||!c||v.length===0){y(x,"Please fill in all required fields",3e3,"error");return}const{start_time:g,end_time:me}=je(w),H={sport_id:parseInt(p),type:f,ntrp:parseFloat(q),max_ntrp:parseFloat(L),num_players:parseInt(R),num_needed:parseInt(A),need_coach:!0,notes:E,player_ids:JSON.stringify(o),date:Pe(c,"yyyy-MM-dd"),start_time:g,slots:JSON.stringify(w),end_time:me,sub_type:F,user_id:(s=(e=i.state)==null?void 0:e.player)==null?void 0:s.id};_.setTable("buddy");const Z=await _.callRestAPI(H,"POST");await ve(_,{user_id:localStorage.getItem("user"),activity_type:Ne.find_a_buddy,action_type:we.CREATE,data:H,club_id:l==null?void 0:l.id,description:`Created a buddy request on behalf of ${(I=(m=i==null?void 0:i.state)==null?void 0:m.player)==null?void 0:I.first_name} ${(z=(J=i==null?void 0:i.state)==null?void 0:J.player)==null?void 0:z.last_name} for ${o.length} players`}),Z.error?y(x,Z.message||"Error creating request",3e3,"error"):(b(!0),y(x,"Request created successfully",3e3,"success"),setTimeout(()=>{S(Q==="admin"?"/admin/find-a-buddy":"/club/find-a-buddy")},2e3))}catch(g){console.error("Error submitting request:",g),y(x,"Error creating request: "+g.message,3e3,"error")}finally{M(!1)}},d=u==null?void 0:u.find(e=>e.id===p);return a.useEffect(()=>{d?(D(d.sport_types||[]),console.log("selectedSportData",d),N(null),h(null)):(D([]),N(null),h(null))},[p,d]),a.useEffect(()=>{if(f&&d){const e=d.sport_types.find(s=>s.type===f);$((e==null?void 0:e.subtype)||[]),h(null)}else $([]),h(null)},[f,d]),t.jsxs("div",{className:"mx-auto max-w-7xl p-4",children:[t.jsx(_e,{onBack:()=>{B==="details"?j("calendar"):S(-1)}}),B==="calendar"?t.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[t.jsx(Me,{onSelectionChange:({sport:e,type:s,subType:m})=>{ee(e),N(s),h(m)},sports:u}),t.jsx("div",{className:"h-fit rounded-lg bg-white p-6",children:t.jsx(Ce,{currentMonth:W,selectedDate:c,onDateSelect:V,onMonthChange:X,showNextButton:!1,nextButtonText:"Next",onNextButtonClick:()=>{j("details")},daysOff:l!=null&&l.days_off?JSON.parse(l==null?void 0:l.days_off):[]})}),c&&t.jsx(ke,{onTimeClick:oe,selectedDate:c,timeRange:v,onTimeSlotsChange:e=>{ie(e)},onNext:()=>{j("details")},nextButtonText:"Next",startHour:0,endHour:23,interval:30,className:"h-fit rounded-lg bg-white p-4 shadow-5",multipleSlots:!0,individualSelection:!1,isTimeSlotAvailable:e=>!0,clubTimes:l!=null&&l.times?JSON.parse(l==null?void 0:l.times):[]})]}):t.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[t.jsx(Be,{selectedDate:c,selectedTimes:v,selectedSport:p,selectedType:f,selectedSubType:F,sports:u,timeSlots:w}),t.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[t.jsx("div",{className:"mb-5 rounded-lg bg-gray-50 p-2",children:t.jsx("p",{className:"text-center text-lg font-medium",children:"Players"})}),t.jsx("div",{className:"mb-4",children:t.jsx("div",{className:"mb-4",children:t.jsxs("div",{className:"flex  items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[t.jsx("span",{className:"w-5",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),t.jsx("input",{type:"text",placeholder:"Search by name",value:O,onChange:e=>ne(e.target.value),className:"w-full border-none bg-transparent text-sm focus:outline-none focus:ring-0"})]})})}),t.jsx("div",{className:"",children:o.length>0&&t.jsx("p",{className:"mb-3 flex flex-row flex-wrap items-center gap-2 gap-y-2 text-sm text-gray-500",children:o.map(e=>{const s=T.find(m=>m.id===e);return t.jsxs("div",{className:"flex w-fit items-center gap-2 rounded-full bg-gray-50 p-1 px-2",children:[t.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",className:"h-6 w-6 rounded-full",alt:(s==null?void 0:s.first_name)+" "+(s==null?void 0:s.last_name)}),t.jsxs("div",{className:"text-sm text-black",children:[s==null?void 0:s.first_name," ",s==null?void 0:s.last_name]}),t.jsx("div",{children:t.jsx("svg",{width:"15",height:"15",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M13 7L7 13M13 13L7 7M19.25 10C19.25 15.1086 15.1086 19.25 10 19.25C4.89137 19.25 0.75 15.1086 0.75 10C0.75 4.89137 4.89137 0.75 10 0.75C15.1086 0.75 19.25 4.89137 19.25 10Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]},e)})})}),t.jsxs("div",{className:"max-h-[400px] space-y-2 overflow-y-auto rounded-lg bg-gray-50 p-2",children:[C.length>0&&C.map(e=>t.jsxs("div",{className:"flex items-center gap-2 p-2",children:[t.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500",checked:o.includes(e.id),onChange:()=>de(e.id)}),t.jsx("img",{src:e.photo||"/default-avatar.png",className:"h-8 w-8 rounded-full",alt:e.name}),t.jsxs("span",{children:[e.first_name," ",e.last_name]})]},e.id)),!C.length&&t.jsx("p",{className:"text-center text-sm text-gray-500",children:"No players found"})]})]}),t.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[t.jsx("div",{className:"rounded-lg bg-gray-50 p-2",children:t.jsx("p",{className:"text-center text-lg font-medium",children:"Other details"})}),t.jsxs("div",{className:"mt-4 space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"mb-3 block text-sm text-gray-500",children:"My group NTRP score"}),t.jsxs("div",{className:"flex gap-4",children:[t.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[t.jsx("div",{className:"flex w-fit items-center justify-center gap-2 self-stretch bg-slate-50 px-2 py-2.5 text-neutral-400",children:"Min"}),t.jsx("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:q,onChange:e=>te(e.target.value),children:G.map(e=>t.jsx("option",{value:e,children:e},e))})]}),t.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[t.jsx("div",{className:"flex w-fit items-center justify-center gap-2 self-stretch bg-slate-50 px-2 py-2.5 text-neutral-400",children:"Max"}),t.jsx("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:L,onChange:e=>se(e.target.value),children:G.map(e=>t.jsx("option",{value:e,children:e},e))})]})]})]}),t.jsxs("div",{className:"flex items-center justify-between gap-2",children:[t.jsx("label",{className:"block flex-1 text-sm font-medium",children:"Players playing"}),t.jsx("select",{value:R,onChange:e=>ae(Number(e.target.value)),className:"max-w-24 flex-1 flex-shrink rounded-xl border border-gray-300 p-2",children:[1,2,3,4].map(e=>t.jsx("option",{value:e,children:e},e))})]}),t.jsxs("div",{className:"flex items-center justify-between gap-2",children:[t.jsx("label",{className:"block flex-1 text-sm font-medium",children:"Players needed"}),t.jsx("select",{value:A,onChange:e=>le(Number(e.target.value)),className:"max-w-24 flex-1 flex-shrink rounded-xl border border-gray-300 p-2",children:[1,2,3,4].map(e=>t.jsx("option",{value:e,children:e},e))})]}),t.jsxs("div",{children:[t.jsxs("label",{className:"block text-sm font-medium",children:["Short bio ",t.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),t.jsx("textarea",{value:E,onChange:e=>re(e.target.value),className:"mt-2 w-full rounded-xl border border-gray-300 p-2",rows:3})]}),t.jsx(Se,{onClick:ce,loading:k,className:"mt-4 w-full rounded-xl bg-blue-900 py-3 font-medium text-white disabled:opacity-50",children:k?"Submitting...":"Submit request"})]})]})]}),U&&t.jsx(Te,{onContinue:()=>b(!1),title:"Request made successfully",onClose:()=>{b(!1),S("/club/find-a-buddy")},description:"Request made successfully. A confirmation email has been sent to the players."})]})};export{Xe as A};
