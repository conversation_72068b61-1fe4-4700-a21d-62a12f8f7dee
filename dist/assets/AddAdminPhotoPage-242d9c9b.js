import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as i,f as c}from"./vendor-851db8c1.js";import{X as u}from"./@uppy/xhr-upload-ffe6b130.js";import{u as f,D as h}from"./@uppy/react-588f1389.js";import{M as x,A as b,G as g,b as E}from"./index-a0784e19.js";import{a as j}from"./@uppy/core-0760343f.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/aws-s3-multipart-9d71a8f2.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/compressor-11f993e4.js";import"./@uppy/drag-drop-9b58d36f.js";import"./@uppy/progress-bar-4d089812.js";import"./@uppy/file-input-630decca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let a=new x;const pt=({setSidebar:p})=>{const{dispatch:m}=i.useContext(b),d=c(),{dispatch:s}=i.useContext(g),l=f(()=>{let o=new j;return o.use(u,{id:"XHRUpload",method:"post",formData:!0,limit:0,fieldName:"file",allowedMetaFields:["caption","size"],headers:a.getHeader(),endpoint:a.uploadUrl()}),o.on("file-added",e=>{o.setFileMeta(e.id,{size:e.size,caption:""})}),o.on("upload-success",async(e,r)=>{r.status,r.body,console.log("response",r),E(s,"Uploaded"),d("/admin/photo")}),o.on("upload-error",(e,r,n)=>{n.status==401&&tokenExpireError(m,"TOKEN_EXPIRED")}),o});return i.useEffect(()=>{s({type:"SETPATH",payload:{path:"photo"}})},[]),t.jsxs("div",{className:"relative p-4 flex-auto",children:[t.jsxs("div",{className:"flex items-center pb-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("span",{className:"text-lg font-semibold",children:"Add Photo"})}),t.jsx("div",{className:"flex items-center gap-4",children:t.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>p(!1),children:"Cancel"})})]}),t.jsx(h,{uppy:l})]})};export{pt as default};
