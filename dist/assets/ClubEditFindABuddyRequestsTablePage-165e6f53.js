import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as u,f as G,r as a,j as O}from"./vendor-851db8c1.js";import{u as H}from"./react-hook-form-687afde5.js";import{o as K}from"./yup-2824f222.js";import{c as V,a as r}from"./yup-54691517.js";import{M as z,A as J,G as Q,t as W,d as X,b as Y}from"./index-a0784e19.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as i}from"./MkdInput-e4d2bb0b.js";import{S as Z}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let b=new z;const Xe=c=>{var N,j,v,w;const{dispatch:E}=u.useContext(J),I=V({club_id:r(),user_id:r(),sport:r(),level:r(),start_time:r(),end_time:r(),num_players_needed:r(),players:r(),bio:r(),status:r()}).required(),{dispatch:f}=u.useContext(Q),[h,ee]=u.useState({}),[g,x]=u.useState(!1),[T,_]=u.useState(!1),P=G(),[te,k]=a.useState(0),[se,A]=a.useState(0),[ae,R]=a.useState(""),[le,C]=a.useState(""),[oe,F]=a.useState(""),[re,q]=a.useState(""),[ie,B]=a.useState(0),[de,L]=a.useState(""),[me,D]=a.useState(""),[ne,U]=a.useState(""),{register:l,handleSubmit:M,setError:S,setValue:o,formState:{errors:s}}=H({resolver:K(I)}),m=O();a.useEffect(function(){(async function(){try{_(!0),b.setTable("find_a_buddy_requests");const e=await b.callRestAPI({id:c.activeId?c.activeId:Number(m==null?void 0:m.id)},"GET");e.error||(o("club_id",e.model.club_id),o("user_id",e.model.user_id),o("sport",e.model.sport),o("level",e.model.level),o("start_time",e.model.start_time),o("end_time",e.model.end_time),o("num_players_needed",e.model.num_players_needed),o("players",e.model.players),o("bio",e.model.bio),o("status",e.model.status),k(e.model.club_id),A(e.model.user_id),R(e.model.sport),C(e.model.level),F(e.model.start_time),q(e.model.end_time),B(e.model.num_players_needed),L(e.model.players),D(e.model.bio),U(e.model.status),_(!1))}catch(e){_(!1),console.log("error",e),W(E,e.message)}})()},[]);const $=async e=>{x(!0);try{b.setTable("find_a_buddy_requests");for(let p in h){let n=new FormData;n.append("file",h[p].file);let y=await b.uploadImage(n);e[p]=y.url}const d=await b.callRestAPI({id:c.activeId?c.activeId:Number(m==null?void 0:m.id),club_id:e.club_id,user_id:e.user_id,sport:e.sport,level:e.level,start_time:e.start_time,end_time:e.end_time,num_players_needed:e.num_players_needed,players:e.players,bio:e.bio,status:e.status},"PUT");if(!d.error)Y(f,"Updated"),P("/club/find_a_buddy_requests"),f({type:"REFRESH_DATA",payload:{refreshData:!0}}),c.setSidebar(!1);else if(d.validation){const p=Object.keys(d.validation);for(let n=0;n<p.length;n++){const y=p[n];S(y,{type:"manual",message:d.validation[y]})}}x(!1)}catch(d){x(!1),console.log("Error",d),S("club_id",{type:"manual",message:d.message})}};return u.useEffect(()=>{f({type:"SETPATH",payload:{path:"find_a_buddy_requests"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Find A Buddy Requests"}),T?t.jsx(Z,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:M($),children:[t.jsx(i,{type:"number",page:"edit",name:"club_id",errors:s,label:"Club Id",placeholder:"Club Id",register:l,className:""}),t.jsx(i,{type:"number",page:"edit",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:l,className:""}),t.jsx(i,{page:"edit",name:"sport",errors:s,label:"Sport",placeholder:"Sport",register:l,className:""}),t.jsx(i,{type:"text",page:"edit",name:"level",errors:s,label:"Level",placeholder:"Level",register:l,className:""}),t.jsx(i,{type:"datetime-local",page:"edit",name:"start_time",errors:s,label:"Start Time",placeholder:"Start Time",register:l,className:""}),t.jsx(i,{type:"datetime-local",page:"edit",name:"end_time",errors:s,label:"End Time",placeholder:"End Time",register:l,className:""}),t.jsx(i,{type:"number",page:"edit",name:"num_players_needed",errors:s,label:"Num Players Needed",placeholder:"Num Players Needed",register:l,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"players",children:"Players"}),t.jsx("textarea",{placeholder:"Players",...l("players"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(N=s.players)!=null&&N.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(j=s.players)==null?void 0:j.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"bio",children:"Bio"}),t.jsx("textarea",{placeholder:"Bio",...l("bio"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(v=s.bio)!=null&&v.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(w=s.bio)==null?void 0:w.message})]}),t.jsx(i,{page:"edit",name:"status",errors:s,label:"Status",placeholder:"Status",register:l,className:""}),t.jsx(X,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{Xe as default};
