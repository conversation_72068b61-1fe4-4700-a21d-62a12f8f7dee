import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as a,f as He}from"./vendor-851db8c1.js";import{B as Le}from"./BackButton-11ba52b2.js";import{G as Re,a0 as ze,ao as We,M as Oe,$ as ae,R as Je,a1 as qe,d as Fe,H as Ge,E as Ue,J as Ke,b as A,a3 as de}from"./index-9f98cff7.js";import{C as Ve}from"./Calendar-9031b5fe.js";import{T as Xe}from"./TimeSlots-9a2fb5c0.js";import{S as Qe}from"./SportTypeSelection-ee0cc3da.js";import{h as W}from"./moment-a9aaa855.js";let O=new Oe;function ot({club:g,coaches:me,role:ue,sports:J}){const[le,q]=a.useState("selection"),[w,he]=a.useState(null),[B,xe]=a.useState(null),[$,ge]=a.useState(null),[E,Z]=a.useState(new Date),[k,pe]=a.useState(null),[H,fe]=a.useState(null),[ee,oe]=a.useState(!1),[F,ye]=a.useState([]),be=He(),je=localStorage.getItem("user"),[te,Ne]=a.useState(""),[G,ve]=a.useState(""),[ie,Se]=a.useState(""),[U,we]=a.useState("0.00"),[K,ke]=a.useState(0),[C,Ce]=a.useState("1"),[ce,V]=a.useState(!1),[S,se]=a.useState([]),[Me,re]=a.useState(!1),[c,_e]=a.useState({id:null,hours:[]}),{dispatch:M}=a.useContext(Re),Te=a.useCallback(({sport:d,type:y,subType:t})=>{he(d),xe(y),ge(t)},[]),Be=()=>{Z(new Date(E.getFullYear(),E.getMonth()+1,1))},$e=()=>{Z(new Date(E.getFullYear(),E.getMonth()-1,1))},De=async()=>{oe(!0);try{const{end_time:d,start_time:y}=ae(F),t=S.filter(s=>{var x;return!((x=s.hours)!=null&&x.length)});if(t.length>0){const s=t.map(x=>{var D,P;return`${(D=x.user)==null?void 0:D.first_name} ${(P=x.user)==null?void 0:P.last_name}`}).join(", ");throw new Error(`Please set hours for the following coaches: ${s}`)}const o=S.map(s=>({coach_id:s.id,hours:s.hours,start_time:s.hours[0],end_time:s.hours[s.hours.length-1]})),p={name:te,max_participants:parseInt(G),description:ie,cost_per_head:parseFloat(U),date:W(k).format("YYYY-MM-DD"),end_date:H?W(H).format("YYYY-MM-DD"):null,sport_id:parseInt(w),sub_type:$,type:B,start_time:y,end_time:d,club_id:g==null?void 0:g.id,recurring:K===1||K===!0?1:0,cancellation_policy_days:parseInt(C)||1},j={name:te,max_participants:G,cost_per_head:U,date:W(k).format("YYYY-MM-DD"),sport_id:w,coaches:S,cancellation_policy_days:C},N=Object.entries(j).filter(([s,x])=>!x||Array.isArray(x)&&!x.length).map(([s])=>s.replace(/_/g," "));if(N.length>0)throw new Error(`Please fill in the following required fields: ${N.join(", ")}`);const _=parseInt(C);if(isNaN(_)||_<1||_>30)throw new Error("Cancellation policy must be between 1 and 30 days");console.log("Submitting clinic data:",p),O.setTable("clinics");const m=await O.callRestAPI(p,"POST");m.error?A(M,m.message||"Failed to create clinic",3e3,"error"):(o.forEach(async s=>{const x={clinic_id:m.data,coach_id:s.coach_id,data:JSON.stringify({working_hours:s.hours,fees:U,sport_id:parseInt(w),sub_type:$,type:B,court_ids:"",number_of_players:G})};O.setTable("clinic_coaches"),await O.callRestAPI(x,"POST")}),await Ge(O,{user_id:je,activity_type:Ue.clinic,action_type:Ke.CREATE,data:p,club_id:g==null?void 0:g.id,description:"Created a clinic"}),A(M,"Clinic created successfully",3e3,"success"),be(ue==="club"?"/club/program-clinics":"/admin/program-clinics"))}catch(d){console.error("Error submitting clinic:",d),A(M,d.message,3e3,"error")}finally{oe(!1)}};a.useEffect(()=>{M({type:"SETPATH",payload:{path:"program-clinics"}})},[g]);const Pe=d=>{ye([{from:d.from,until:d.until}])},Ie=()=>{var L,I,X,Y,R,z;const{duration:d,end_time:y,start_time:t}=ae(F),o=r=>{if(!r)return 0;const[n,u]=r.split(":").map(Number);return n*60+u},p=r=>{if(!r)return"";const[n,u]=r.split(":").map(Number),i=n>=12?"PM":"AM";return`${n%12||12}:${u.toString().padStart(2,"0")} ${i}`},j=p(t),N=p(y),m=((r,n)=>{const u=[],i=o(r),h=o(n);for(let l=i;l<h;l+=30){const f=Math.floor(l/60),b=l%60,v=`${f.toString().padStart(2,"0")}:${b.toString().padStart(2,"0")}:00`;u.push({time24:v,time12:p(v)})}return u})(t,y),[s,x]=a.useState(((L=c==null?void 0:c.hours)==null?void 0:L.filter(r=>{if(!t||!y)return!0;const n=r.split(" ")[0]+":00";return o(n)>=o(t)&&o(n)<=o(y)}))||[]),D=r=>{x(n=>{const u=m.findIndex(h=>h.time12===r.time12),i=n.map(h=>m.findIndex(l=>l.time12===h)).sort((h,l)=>h-l);if(n.includes(r.time12)){if(n.length>=2){const h=i[0],l=i[i.length-1];if(u===h||u===l)return n.filter(f=>f!==r.time12)}else if(n.length===1)return[];return n}else{if(n.length===0)return[r.time12];{const h=i[0],l=i[i.length-1];if(u===h-1||u===l+1){const f=n.length+1,b=Math.floor(d*2);return f>b?(A(M,`Cannot select more than ${d} ${d===1?"hour":"hours"} (clinic duration)`,3e3,"error"),n):[...n,r.time12]}else return[r.time12]}}})},P=()=>{if(s.filter(u=>{const[i,h]=u.split(" "),[l,f]=i.split(":").map(Number);let b=l;h==="PM"&&l!==12?b=l+12:h==="AM"&&l===12&&(b=0);const v=`${b.toString().padStart(2,"0")}:${f.toString().padStart(2,"0")}:00`;return o(v)<o(t)||o(v)>=o(y)}).length>0){A(M,`Selected hours must be between ${j} and ${N}`,3e3,"error");return}const n=s.length*.5;if(n>d){A(M,`Coach hours (${n} ${n===1?"hour":"hours"}) cannot exceed clinic duration (${d} ${d===1?"hour":"hours"})`,3e3,"error");return}se(u=>u.map(i=>i.id===c.id?{...i,hours:s}:i)),re(!1)};return e.jsx(Je,{isOpen:Me,onClose:()=>re(!1),title:"Set Coach Hours",primaryButtonText:"Save Hours",onPrimaryAction:P,children:e.jsxs("div",{className:"flex flex-col space-y-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 p-4",children:[e.jsx("div",{className:"h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((I=c==null?void 0:c.user)==null?void 0:I.photo)||"/default-avatar.png",alt:`${(X=c==null?void 0:c.user)==null?void 0:X.first_name} ${(Y=c==null?void 0:c.user)==null?void 0:Y.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-semibold text-gray-900",children:[(R=c==null?void 0:c.user)==null?void 0:R.first_name," ",(z=c==null?void 0:c.user)==null?void 0:z.last_name]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Select available hours"})]})]}),j&&N&&e.jsx("div",{className:"rounded-lg border border-amber-200 bg-amber-50 p-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"h-5 w-5 text-amber-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsxs("p",{className:"font-medium text-amber-800",children:["Clinic Duration: ",j," - ",N," (",d,"h)"]})]})}),s.length>0&&e.jsx("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("svg",{className:"mt-0.5 h-5 w-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium text-green-800",children:["Selected Hours (",s.length*.5," ",s.length===2?"hour":"hours","):"]}),e.jsx("div",{className:"text-sm text-green-700",children:(()=>{var l;if(s.length===0)return"";const r=[...s].sort((f,b)=>{const v=m.findIndex(T=>T.time12===f),Q=m.findIndex(T=>T.time12===b);return v-Q}),n=r[0],u=r[r.length-1],i=m.findIndex(f=>f.time12===u),h=((l=m[i+1])==null?void 0:l.time12)||u;return`From: ${n} Until: ${h}`})()})]})]})}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Available Time Slots"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:m.map(r=>e.jsx("button",{onClick:()=>D(r),className:`w-full rounded-lg border-2 p-3 text-center font-medium transition-all duration-200 hover:scale-105
                    ${s.includes(r.time12)?"border-primaryBlue bg-primaryBlue text-white shadow-md":"border-gray-200 text-gray-700 hover:border-primaryBlue hover:bg-blue-50 hover:text-primaryBlue"}
                  `,children:r.time12},r.time12))})]})]})})},Ye=()=>{const d=t=>{const o=J==null?void 0:J.find(p=>p.id===t);return(o==null?void 0:o.name)||"Unknown Sport"},y=t=>{if(!t||t.length===0)return"No time selected";const{start_time:o,end_time:p,duration:j}=ae(t);return`${de(o)} - ${de(p)} (${j}h)`};return e.jsxs("div",{className:"mx-auto p-4 md:container",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Create New Clinic"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Fill in the details for your clinic and select coaches."})]}),e.jsxs("div",{className:"mb-8 rounded-xl border border-blue-100 bg-blue-50 p-6",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-900",children:"Selected Information"}),e.jsxs("button",{onClick:()=>q("selection"),className:"flex items-center space-x-2 rounded-lg border border-blue-200 bg-white px-3 py-2 text-sm font-medium text-blue-700 transition-colors hover:bg-blue-50",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),e.jsx("span",{children:"Edit Selection"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs("div",{className:"rounded-lg bg-white p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sport"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:w?d(w):"Not selected"}),B&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Type: ",B]}),$&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Subtype: ",$]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Start Date"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:k?W(k).format("MMM DD, YYYY"):"Not selected"})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"End Date"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:H?W(H).format("MMM DD, YYYY"):"Not set (optional)"})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Time"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:y(F)})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-2",children:[e.jsxs("div",{className:"h-fit space-y-6 rounded-xl border border-gray-100 bg-white p-6 shadow-lg",children:[e.jsxs("div",{className:"border-b border-gray-200 pb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Clinic Information"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Basic details about your clinic"})]}),e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Clinic Name *"}),e.jsx("input",{type:"text",value:te,onChange:t=>Ne(t.target.value),placeholder:"Enter clinic name",className:"block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Maximum Players *"}),e.jsx("input",{type:"number",value:G,onChange:t=>ve(t.target.value),placeholder:"Enter max number of players",min:"1",className:"block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Description"}),e.jsx("textarea",{value:ie,onChange:t=>Se(t.target.value),rows:4,placeholder:"Describe your clinic...",className:"block w-full resize-none rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Recurring Event"}),e.jsxs("select",{value:K===1||K===!0?"Yes":"No",onChange:t=>ke(t.target.value==="Yes"?1:0),className:"block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",children:[e.jsx("option",{value:"No",children:"No"}),e.jsx("option",{value:"Yes",children:"Yes"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Price per Person *"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-4",children:e.jsx("span",{className:"font-medium text-gray-500",children:"$"})}),e.jsx("input",{type:"number",value:U,onChange:t=>we(t.target.value),placeholder:"0.00",min:"0",step:"0.01",className:"block w-full rounded-lg border border-gray-300 py-3 pl-8 pr-4 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Cancellation Policy *"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"number",value:C,onChange:t=>Ce(t.target.value),placeholder:"1",min:"1",max:"30",className:"block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-4",children:e.jsx("span",{className:"text-sm font-medium text-gray-500",children:parseInt(C)===1?"day":"days"})})]}),e.jsxs("p",{className:"text-xs text-gray-600",children:["Participants can cancel their booking up to"," ",e.jsx("span",{className:"font-medium text-gray-900",children:C||"1"})," ",parseInt(C)===1?"day":"days"," ","before the clinic starts"]})]})]})]})]}),e.jsxs("div",{className:"h-fit w-full space-y-6 rounded-xl border border-gray-100 bg-white p-6 shadow-lg",children:[e.jsx("div",{className:"border-b border-gray-200 pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Coaches"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Select and set hours for coaches"})]}),e.jsxs("span",{className:"rounded-full bg-primaryBlue/10 px-3 py-1 text-sm font-semibold text-primaryBlue",children:[S.length," selected"]})]})}),S.length===0?e.jsxs("div",{className:"rounded-lg border-2 border-dashed border-gray-200 p-8 text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100",children:e.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"})})}),e.jsx("p",{className:"mb-4 text-gray-500",children:"No coaches selected yet"}),e.jsxs("button",{onClick:()=>V(!0),className:"inline-flex items-center rounded-lg bg-primaryBlue px-4 py-2 text-white transition-colors hover:bg-primaryBlue/90",children:[e.jsx("svg",{className:"mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Add coaches"]})]}):e.jsxs("div",{className:"space-y-3",children:[S.map(t=>{var o,p,j,N,_;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4 transition-all hover:shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((o=t==null?void 0:t.user)==null?void 0:o.photo)||"/default-avatar.png",alt:`${(p=t.user)==null?void 0:p.first_name} ${(j=t.user)==null?void 0:j.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:`${(N=t==null?void 0:t.user)==null?void 0:N.first_name} ${(_=t==null?void 0:t.user)==null?void 0:_.last_name}`}),e.jsx("span",{className:"text-sm text-gray-600",children:t.hours.length>0?(()=>{const m=[...t.hours].sort((h,l)=>{const f=b=>{const[v,Q]=b.split(" "),[T,Ee]=v.split(":").map(Number);let ne=T;return Q==="PM"&&T!==12&&(ne+=12),Q==="AM"&&T===12&&(ne=0),ne*60+Ee};return f(h)-f(l)}),s=m.length*.5,x=m[0],D=m[m.length-1],[P,L]=D.split(" "),[I,X]=P.split(":").map(Number);let Y=I;L==="PM"&&I!==12&&(Y+=12),L==="AM"&&I===12&&(Y=0);const R=Y*60+X+30,z=Math.floor(R/60),r=R%60,n=z>=12?"PM":"AM",i=`${z%12||12}:${r.toString().padStart(2,"0")} ${n}`;return m.length===1?`${x} - ${i} (0.5 hours)`:`${x} - ${i} (${s} ${s===1?"hour":"hours"})`})():"No hours set"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{className:"rounded-lg border border-primaryBlue bg-white px-3 py-2 text-sm font-medium text-primaryBlue transition-colors hover:bg-blue-50",onClick:()=>{_e(t),re(!0)},children:t.hours.length>0?"Edit hours":"Set hours"}),e.jsx("button",{onClick:()=>se(m=>m.filter(s=>s.id!==t.id)),className:"rounded-lg border border-red-200 p-2 text-red-400 transition-colors hover:bg-red-50 hover:text-red-600",title:"Remove coach",children:e.jsx(qe,{className:"h-4 w-4"})})]})]},t.id)}),e.jsx("button",{onClick:()=>V(!0),className:"w-full rounded-lg border-2 border-dashed border-gray-300 p-3 text-center text-sm font-medium text-gray-600 transition-colors hover:border-primaryBlue hover:text-primaryBlue",children:"+ Add more coaches"})]}),e.jsx("div",{className:"border-t border-gray-200 pt-6",children:e.jsx(Fe,{onClick:De,loading:ee,className:`w-full rounded-lg py-4 text-lg font-semibold text-white shadow-lg transition-all duration-200
                  ${ee?"cursor-not-allowed bg-primaryBlue/70":"transform bg-primaryBlue hover:-translate-y-0.5 hover:bg-primaryBlue/90 hover:shadow-xl"}
                `,children:ee?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsxs("svg",{className:"h-5 w-5 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e.jsx("span",{children:"Creating Clinic..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("span",{children:"Create Clinic"}),e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]})})})]})]})]})},Ae=()=>{if(le==="details")q("selection");else return!0;return!1};return console.log("selected coaches",S),e.jsxs(e.Fragment,{children:[e.jsx(Le,{onBack:Ae}),le==="selection"?e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(Qe,{onSelectionChange:Te,sports:J||[],initialSport:w,initialType:B,initialSubType:$},`${w}-${B}-${$}`),e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsx(Ve,{currentMonth:E,selectedDate:k,selectedEndDate:H,onDateSelect:pe,onEndDateSelect:fe,allowRangeSelection:!0,onMonthChange:Z,showNextButton:!1,nextButtonText:"Next",onNextMonth:Be,onPreviousMonth:$e,daysOff:g!=null&&g.days_off?JSON.parse(g.days_off):[],onNextButtonClick:()=>{q("details")}})}),k&&e.jsx(Xe,{onTimeClick:Pe,selectedDate:k,timeRange:F,timeSlots:ze(),onNext:()=>{q("details")},nextButtonText:"Next",startHour:0,endHour:24,interval:30,className:"h-fit rounded-lg bg-white p-4 shadow-5",multipleSlots:!1,individualSelection:!0,clubTimes:g!=null&&g.times?JSON.parse(g.times):[],isTimeSlotAvailable:()=>!0})]})}):Ye(),ce&&e.jsx(We,{coaches:me,selectedCoaches:S,setSelectedCoaches:se,isOpen:ce,onClose:()=>V(!1),onSave:()=>V(!1)}),e.jsx(Ie,{})]})}export{ot as A};
