import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,f as L,r as c,j as M}from"./vendor-851db8c1.js";import{u as G}from"./react-hook-form-687afde5.js";import{o as O}from"./yup-2824f222.js";import{c as B,a as g}from"./yup-54691517.js";import{M as U,A as q,G as K,t as V,d as z,b as J}from"./index-9f98cff7.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as Q}from"./MkdInput-ebcf18ce.js";import{S as W}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let n=new U;const Be=i=>{var S,w,N,E,v,T;const{dispatch:k}=r.useContext(q),I=B({slug:g(),subject:g(),tag:g(),html:g()}).required(),{dispatch:b}=r.useContext(K),[f,X]=r.useState({}),[j,x]=r.useState(!1),[A,h]=r.useState(!1),F=L(),[Y,R]=c.useState(""),[Z,D]=c.useState(""),[_,P]=c.useState(""),[ee,C]=c.useState(""),{register:d,handleSubmit:H,setError:y,setValue:u,formState:{errors:s}}=G({resolver:O(I)}),o=M();c.useEffect(function(){(async function(){try{h(!0),n.setTable("email");const e=await n.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id)},"GET");e.error||(u("slug",e.model.slug),u("subject",e.model.subject),u("tag",e.model.tag),u("html",e.model.html),R(e.model.slug),D(e.model.subject),P(e.model.tag),C(e.model.html),h(!1))}catch(e){h(!1),console.log("error",e),V(k,e.message)}})()},[]);const $=async e=>{x(!0);try{n.setTable("email");for(let m in f){let l=new FormData;l.append("file",f[m].file);let p=await n.uploadImage(l);e[m]=p.url}const a=await n.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id),slug:e.slug,subject:e.subject,tag:e.tag,html:e.html},"PUT");if(!a.error)J(b,"Updated"),F("/club/email"),b({type:"REFRESH_DATA",payload:{refreshData:!0}}),i.setSidebar(!1);else if(a.validation){const m=Object.keys(a.validation);for(let l=0;l<m.length;l++){const p=m[l];y(p,{type:"manual",message:a.validation[p]})}}x(!1)}catch(a){x(!1),console.log("Error",a),y("slug",{type:"manual",message:a.message})}};return r.useEffect(()=>{b({type:"SETPATH",payload:{path:"email"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Email"}),A?t.jsx(W,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:H($),children:[t.jsx(Q,{type:"text",page:"edit",name:"slug",errors:s,label:"Slug",placeholder:"Slug",register:d,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"subject",children:"Subject"}),t.jsx("textarea",{placeholder:"Subject",...d("subject"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(S=s.subject)!=null&&S.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(w=s.subject)==null?void 0:w.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"tag",children:"Tag"}),t.jsx("textarea",{placeholder:"Tag",...d("tag"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(N=s.tag)!=null&&N.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(E=s.tag)==null?void 0:E.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"html",children:"Html"}),t.jsx("textarea",{placeholder:"Html",...d("html"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(v=s.html)!=null&&v.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(T=s.html)==null?void 0:T.message})]}),t.jsx(z,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:j,disable:j,children:"Submit"})]})]})};export{Be as default};
