import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as m,j as n}from"./vendor-851db8c1.js";import"./yup-54691517.js";import{M as h,G as r,t as o}from"./index-a0784e19.js";import{S as N}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let c=new h;const Z=()=>{m.useContext(r);const{dispatch:d}=m.useContext(r),[e,t]=m.useState({}),[x,i]=m.useState(!0),l=n();return m.useEffect(function(){(async function(){try{i(!0),c.setTable("buddy");const a=await c.callRestAPI({id:Number(l==null?void 0:l.id),join:""},"GET");a.error||(t(a.model),i(!1))}catch(a){i(!1),console.log("error",a),o(d,a.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:x?s.jsx(N,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Buddy"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Sport Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.sport_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Type"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.type})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Ntrp"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.ntrp})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Reservation Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.reservation_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Num Players"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.num_players})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Num Needed"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.num_needed})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Need Coach"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.need_coach})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Notes"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.notes})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Player Ids"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.player_ids})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Start Time"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.start_time})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"End Time"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.end_time})]})})]})})};export{Z as default};
