import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as i,f as O,r as n,j as P}from"./vendor-851db8c1.js";import{u as C}from"./react-hook-form-687afde5.js";import{o as F}from"./yup-2824f222.js";import{c as U,a as j}from"./yup-54691517.js";import{M as L,A as M,G,t as $,d as B,b as H}from"./index-a0784e19.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as _}from"./MkdInput-e4d2bb0b.js";import{S as q}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let m=new L;const Ce=a=>{var y,v;const{dispatch:I}=i.useContext(M),w=U({user_id:j(),stripe_id:j(),object:j()}).required(),{dispatch:p}=i.useContext(G),[h,K]=i.useState({}),[g,u]=i.useState(!1),[E,b]=i.useState(!1),N=O(),[V,k]=n.useState(0),[z,T]=n.useState(""),[J,A]=n.useState(""),{register:f,handleSubmit:R,setError:S,setValue:x,formState:{errors:d}}=C({resolver:F(w)}),s=P();n.useEffect(function(){(async function(){try{b(!0),m.setTable("stripe_invoice");const e=await m.callRestAPI({id:a.activeId?a.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(x("user_id",e.model.user_id),x("stripe_id",e.model.stripe_id),x("object",e.model.object),k(e.model.user_id),T(e.model.stripe_id),A(e.model.object),b(!1))}catch(e){b(!1),console.log("error",e),$(I,e.message)}})()},[]);const D=async e=>{u(!0);try{m.setTable("stripe_invoice");for(let l in h){let r=new FormData;r.append("file",h[l].file);let c=await m.uploadImage(r);e[l]=c.url}const o=await m.callRestAPI({id:a.activeId?a.activeId:Number(s==null?void 0:s.id),user_id:e.user_id,stripe_id:e.stripe_id,object:e.object},"PUT");if(!o.error)H(p,"Updated"),N("/club/stripe_invoice"),p({type:"REFRESH_DATA",payload:{refreshData:!0}}),a.setSidebar(!1);else if(o.validation){const l=Object.keys(o.validation);for(let r=0;r<l.length;r++){const c=l[r];S(c,{type:"manual",message:o.validation[c]})}}u(!1)}catch(o){u(!1),console.log("Error",o),S("user_id",{type:"manual",message:o.message})}};return i.useEffect(()=>{p({type:"SETPATH",payload:{path:"stripe_invoice"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Stripe Invoice"}),E?t.jsx(q,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:R(D),children:[t.jsx(_,{type:"number",page:"edit",name:"user_id",errors:d,label:"User Id",placeholder:"User Id",register:f,className:""}),t.jsx(_,{type:"text",page:"edit",name:"stripe_id",errors:d,label:"Stripe Id",placeholder:"Stripe Id",register:f,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),t.jsx("textarea",{placeholder:"Object",...f("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=d.object)!=null&&y.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(v=d.object)==null?void 0:v.message})]}),t.jsx(B,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{Ce as default};
