import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as oe}from"./index.esm-51ae62c8.js";import{r as x,b as re}from"./vendor-851db8c1.js";import{u as ce,d as de,G as ue,v as be,R as xe,K as me,M as ge,b as w,E as ne,J as ie}from"./index-9f98cff7.js";import{b as m}from"./@headlessui/react-a5400090.js";import{C as pe}from"./ChevronRightIcon-efb4c46c.js";import{T as he}from"./TrashIcon-7d213648.js";function _e({onSubmit:A,onClose:g,initialData:r,mode:p,onDelete:F}){var E,f;const{sports:b}=ce(),[C,d]=x.useState({name:p==="create",price:p==="create",features:new Set});console.log("currentMode",p);const[B,S]=x.useState(!1),L=r?{...r,advance_booking_days:r.advance_booking_days||{court:10,lesson:10,clinic:10,buddy:10},advance_booking_enabled:r.advance_booking_enabled||{court:!0,lesson:!0,clinic:!0,buddy:!0},applicable_sports:r.applicable_sports||[]}:{plan_id:null,plan_name:"",price:0,allow_clinic:!1,allow_buddy:!1,allow_coach:!1,allow_groups:!1,allow_court:!1,features:[],advance_booking_days:{court:10,lesson:10,clinic:10,buddy:10},advance_booking_enabled:{court:!0,lesson:!0,clinic:!0,buddy:!0},applicable_sports:[]},[l,i]=x.useState(L),$=async()=>{S(!0);const a={...l,advance_booking_enabled:{court:!!l.advance_booking_enabled.court,lesson:!!l.advance_booking_enabled.lesson,clinic:!!l.advance_booking_enabled.clinic,buddy:!!l.advance_booking_enabled.buddy}};console.log("Submitting plan data:",a),await A(a,p),S(!1)},u=(a,t)=>{i(n=>({...n,features:n.features.map(o=>o.id===a?{...o,text:t}:o)})),d(n=>{const o=new Set(n.features);return o.delete(a),{...n,features:o}})},D=a=>{i(t=>({...t,features:t.features.filter(n=>n.id!==a)}))},q=()=>{const a=Math.max(...l.features.map(t=>t.id),0)+1;i(t=>({...t,features:[...t.features,{id:a,text:""}]})),d(t=>({...t,features:new Set([...t.features,a])}))},R={allow_court:"Court booking",allow_clinic:"Clinics",allow_coach:"Lesson",allow_buddy:"Find a Buddy",allow_groups:"My Groups"},h=(a,t)=>{i(n=>({...n,[a]:t})),d(n=>({...n,[a]:!1}))},T=a=>{i(t=>({...t,[a]:!t[a]}))},I=a=>{i(t=>{const n=t.applicable_sports||[];return n.includes(a)?{...t,applicable_sports:n.filter(J=>J!==a)}:{...t,applicable_sports:[...n,a]}})},y=()=>{const t=((b==null?void 0:b.filter(n=>n.status===1))||[]).map(n=>n.id);i(n=>({...n,applicable_sports:t}))},G=()=>{i(a=>({...a,applicable_sports:[]}))};return e.jsxs("div",{className:"flex h-full flex-col gap-4",children:[e.jsxs("div",{className:"flex-1 space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Plan name"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>d(a=>({...a,name:!0})),children:"Edit"})]}),C.name?e.jsx("input",{type:"text",value:l.plan_name,onChange:a=>i(t=>({...t,plan_name:a.target.value})),onBlur:()=>h("plan_name",l.plan_name),className:"w-full rounded-md border border-gray-300 px-3 py-2",autoFocus:!0}):e.jsx("div",{children:l==null?void 0:l.plan_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Price"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>d(a=>({...a,price:!0})),children:"Edit"})]}),C.price?e.jsx("input",{type:"number",value:l.price,onChange:a=>i(t=>({...t,price:parseFloat(a.target.value)})),onBlur:()=>h("price",l.price),className:"w-full rounded-md border border-gray-300 px-3 py-2",step:"0.01",autoFocus:!0}):e.jsx("div",{children:l.price===0?e.jsx("span",{className:"font-semibold text-green-600",children:"Free"}):`$${(E=l.price)==null?void 0:E.toFixed(2)}`})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Module"}),e.jsx("div",{className:"space-y-4",children:Object.entries(R).map(([a,t])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:t}),e.jsx(m,{checked:l[a],onChange:()=>T(a),className:`${l[a]?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l[a]?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]},a))})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Applicable Sports"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",onClick:y,className:"text-xs text-blue-600 hover:text-blue-800",children:"Select All"}),e.jsx("span",{className:"text-xs text-gray-400",children:"|"}),e.jsx("button",{type:"button",onClick:G,className:"text-xs text-blue-600 hover:text-blue-800",children:"Clear All"})]})]}),e.jsx("div",{className:"space-y-3",children:(b==null?void 0:b.filter(a=>a.status===1).length)>0?b.filter(a=>a.status===1).map(a=>{var t;return e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",id:`sport-${a.id}`,checked:((t=l.applicable_sports)==null?void 0:t.includes(a.id))||!1,onChange:()=>I(a.id),className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx("label",{htmlFor:`sport-${a.id}`,className:"cursor-pointer text-sm font-medium text-gray-700",children:a.name})]})},a.id)}):e.jsx("div",{className:"text-sm italic text-gray-500",children:"No active sports available. Please add sports in your club settings first."})}),((f=l.applicable_sports)==null?void 0:f.length)===0&&e.jsx("div",{className:"rounded-md bg-amber-50 p-2 text-xs text-amber-600",children:"⚠️ No sports selected. This membership will not apply to any specific sports."})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Advance Booking Days"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Court Reservation"}),e.jsx(m,{checked:l.advance_booking_enabled.court,onChange:()=>{const a=!l.advance_booking_enabled.court;console.log("Toggling court booking enabled:",a),i(t=>({...t,advance_booking_enabled:{...t.advance_booking_enabled,court:a}}))},className:`${l.advance_booking_enabled.court?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l.advance_booking_enabled.court?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),l.advance_booking_enabled.court&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:l.advance_booking_days.court,onChange:a=>i(t=>({...t,advance_booking_days:{...t.advance_booking_days,court:parseInt(a.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Lesson Booking"}),e.jsx(m,{checked:l.advance_booking_enabled.lesson,onChange:()=>{const a=!l.advance_booking_enabled.lesson;console.log("Toggling lesson booking enabled:",a),i(t=>({...t,advance_booking_enabled:{...t.advance_booking_enabled,lesson:a}}))},className:`${l.advance_booking_enabled.lesson?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l.advance_booking_enabled.lesson?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),l.advance_booking_enabled.lesson&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:l.advance_booking_days.lesson,onChange:a=>i(t=>({...t,advance_booking_days:{...t.advance_booking_days,lesson:parseInt(a.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Clinic/Program Booking"}),e.jsx(m,{checked:l.advance_booking_enabled.clinic,onChange:()=>{const a=!l.advance_booking_enabled.clinic;console.log("Toggling clinic booking enabled:",a),i(t=>({...t,advance_booking_enabled:{...t.advance_booking_enabled,clinic:a}}))},className:`${l.advance_booking_enabled.clinic?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l.advance_booking_enabled.clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),l.advance_booking_enabled.clinic&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:l.advance_booking_days.clinic,onChange:a=>i(t=>({...t,advance_booking_days:{...t.advance_booking_days,clinic:parseInt(a.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Find-a-Buddy Booking"}),e.jsx(m,{checked:l.advance_booking_enabled.buddy,onChange:()=>{const a=!l.advance_booking_enabled.buddy;console.log("Toggling buddy booking enabled:",a),i(t=>({...t,advance_booking_enabled:{...t.advance_booking_enabled,buddy:a}}))},className:`${l.advance_booking_enabled.buddy?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l.advance_booking_enabled.buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),l.advance_booking_enabled.buddy&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:l.advance_booking_days.buddy,onChange:a=>i(t=>({...t,advance_booking_days:{...t.advance_booking_days,buddy:parseInt(a.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Plan features"}),e.jsxs("div",{className:"space-y-4",children:[l.features.map(a=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Feature ",a.id]}),e.jsxs("div",{className:"space-x-4",children:[e.jsx("button",{className:"text-sm text-red-600",onClick:()=>D(a.id),children:"Delete"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>d(t=>({...t,features:new Set([...t.features,a.id])})),children:"Edit"})]})]}),C.features.has(a.id)?e.jsx("input",{type:"text",value:a.text,onChange:t=>i(n=>({...n,features:n.features.map(o=>o.id===a.id?{...o,text:t.target.value}:o)})),onBlur:()=>u(a.id,a.text),className:"w-full rounded-md border border-gray-300 px-3 py-2",autoFocus:!0}):e.jsx("div",{children:a.text})]},a.id)),e.jsx("button",{className:"text-sm text-blue-600",onClick:q,children:"+ Add feature"})]})]})]}),e.jsxs("div",{className:"flex  flex-shrink-0 justify-between gap-4 border-t border-gray-200 px-4 py-4",children:[e.jsx("div",{className:"flex gap-2",children:p==="edit"&&F&&e.jsx("button",{type:"button",className:"rounded-xl border border-red-200 bg-red-50 px-3 py-2 text-sm font-semibold text-red-600 hover:bg-red-100",onClick:()=>F(r),children:"Delete Plan"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:"rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:g,children:"Cancel"}),e.jsx(de,{loading:B,type:"submit",className:"rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:$,children:p=="edit"?"Save changes":"Create plan"})]})]})]})}let j=new ge;function Ce({fetchProfileSettings:A,membershipPlans:g,profileSettings:r,role:p}){const[F,b]=x.useState(null),[C,d]=x.useState(!1),[B,S]=x.useState(""),[L,l]=x.useState(!1),[i,$]=x.useState(!1),[u,D]=x.useState(null),[q,R]=x.useState(!1),{club:h,sports:T}=ce(),I=localStorage.getItem("user"),{dispatch:y,state:G}=re.useContext(ue),E=s=>{if(!s||s.length===0)return"All Sports";if(!T||T.length===0)return"Loading...";const c=s.map(_=>{var v;return(v=T.find(k=>k.id===_))==null?void 0:v.name}).filter(Boolean);return c.length===0?"No Sports":c.length>2?`${c.slice(0,2).join(", ")} +${c.length-2} more`:c.join(", ")},f=s=>{s.preventDefault(),l(!0),w(y,"Please use the edit button to modify plan settings",3e3,"warning"),setTimeout(()=>l(!1),820)},a=s=>{b(s),d(!0)},t=async(s,c)=>{var _,v,k,V,U,X,K,z,H,Q,W;try{let N;if(c==="edit")N={membership_settings:g.map(M=>{var O,Z,P,ee,ae,se,te,le;return M.plan_id===s.plan_id?{plan_id:s.plan_id,plan_name:s.plan_name,price:s.price,allow_clinic:s.allow_clinic,allow_buddy:s.allow_buddy,allow_coach:s.allow_coach,allow_groups:s.allow_groups,allow_court:s.allow_court,features:s.features,applicable_sports:s.applicable_sports||[],advance_booking_days:{court:((O=s.advance_booking_days)==null?void 0:O.court)||10,lesson:((Z=s.advance_booking_days)==null?void 0:Z.lesson)||10,clinic:((P=s.advance_booking_days)==null?void 0:P.clinic)||10,buddy:((ee=s.advance_booking_days)==null?void 0:ee.buddy)||10},advance_booking_enabled:{court:((ae=s.advance_booking_enabled)==null?void 0:ae.court)!==!1,lesson:((se=s.advance_booking_enabled)==null?void 0:se.lesson)!==!1,clinic:((te=s.advance_booking_enabled)==null?void 0:te.clinic)!==!1,buddy:((le=s.advance_booking_enabled)==null?void 0:le.buddy)!==!1}}:M})};else{const M=await j.callRawAPI("/v3/api/custom/courtmatchup/stripe/product",{name:s.plan_name,description:s.plan_name,club_id:(v=(_=G.clubProfile)==null?void 0:_.club)==null?void 0:v.id},"POST");console.log("stripeProductResponse",M);const O=await j.callRawAPI("/v3/api/custom/courtmatchup/stripe/price",{product_id:M.model,name:s.plan_name,amount:s.price,type:"recurring",interval:"month",interval_count:1,trial_days:0,usage_type:"licenced",usage_limit:0},"POST");N={membership_settings:[...g,{plan_id:O.model,plan_name:s.plan_name,price:s.price,allow_clinic:s.allow_clinic,allow_buddy:s.allow_buddy,allow_coach:s.allow_coach,allow_groups:s.allow_groups,allow_court:s.allow_court,features:s.features,applicable_sports:s.applicable_sports||[],advance_booking_days:{court:((k=s.advance_booking_days)==null?void 0:k.court)||10,lesson:((V=s.advance_booking_days)==null?void 0:V.lesson)||10,clinic:((U=s.advance_booking_days)==null?void 0:U.clinic)||10,buddy:((X=s.advance_booking_days)==null?void 0:X.buddy)||10},advance_booking_enabled:{court:((K=s.advance_booking_enabled)==null?void 0:K.court)||!0,lesson:((z=s.advance_booking_enabled)==null?void 0:z.lesson)||!0,clinic:((H=s.advance_booking_enabled)==null?void 0:H.clinic)||!0,buddy:((Q=s.advance_booking_enabled)==null?void 0:Q.buddy)||!0}}]}}console.log("Submitting membership plan data:",JSON.stringify(N,null,2));const Y=await j.callRawAPI(p==="club"?"/v3/api/custom/courtmatchup/club/profile-edit":`/v3/api/custom/courtmatchup/admin/profile-edit/${(W=r==null?void 0:r.user)==null?void 0:W.id}`,N,"POST");j.setTable("activity_logs"),await j.callRestAPI({user_id:I,activity_type:ne.club_ui,action_type:ie.UPDATE,data:JSON.stringify(N),club_id:h==null?void 0:h.id,description:"Updated membership plans"},"POST"),Y.error&&w(y,Y.message||"Failed to save plan",3e3,"error"),d(!1),A(),n()}catch(N){w(y,N.message||"Failed to save plan",3e3,"error")}},n=()=>{d(!1),b(null)},o=s=>{D(s),$(!0)},J=async()=>{var s;if(u){R(!0);try{const c=g.filter(k=>k.plan_id!==u.plan_id),_={membership_settings:c};console.log("Deleting membership plan:",u.plan_name,"Remaining plans:",c.length);const v=await j.callRawAPI(p==="club"?"/v3/api/custom/courtmatchup/club/profile-edit":`/v3/api/custom/courtmatchup/admin/profile-edit/${(s=r==null?void 0:r.user)==null?void 0:s.id}`,_,"POST");j.setTable("activity_logs"),await j.callRestAPI({user_id:I,activity_type:ne.club_ui,action_type:ie.DELETE,data:JSON.stringify({deleted_plan:u,remaining_plans:c.length}),club_id:h==null?void 0:h.id,description:`Deleted membership plan: ${u.plan_name}`},"POST"),v.error?w(y,v.message||"Failed to delete plan",3e3,"error"):(w(y,`Successfully deleted plan: ${u.plan_name}`,3e3,"success"),A()),$(!1),D(null)}catch(c){w(y,c.message||"Failed to delete plan",3e3,"error")}finally{R(!1)}}};return e.jsxs("div",{className:"flex flex-col gap-4 p-5",children:[e.jsx("style",{children:`
          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
            20%, 40%, 60%, 80% { transform: translateX(4px); }
          }
          .shake {
            animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
          }
        `}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xl font-medium",children:"Membership settings"}),e.jsxs("button",{onClick:()=>{S("create"),d(!0)},className:"flex items-center gap-2 rounded-lg border bg-primaryBlue px-3 py-2 text-sm text-white ",children:[e.jsx("span",{children:"New plan"}),e.jsx(pe,{className:"h-4 w-4"})]})]}),e.jsx("div",{className:`overflow-x-auto ${L?"shake":""}`,children:e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"pb-4",children:"Plan"}),e.jsx("th",{className:"pb-4",children:"Price"}),e.jsx("th",{className:"pb-4 text-center",children:"Court booking"}),e.jsx("th",{className:"pb-4 text-center",children:"Lessons"}),e.jsx("th",{className:"pb-4 text-center",children:"Clinics"}),e.jsx("th",{className:"pb-4 text-center",children:"Find a Buddy"}),e.jsx("th",{className:"pb-4 text-center",children:"My Groups"}),e.jsx("th",{className:"pb-4 text-center",children:"Sports Covered"}),e.jsx("th",{className:"pb-4 text-center",children:"Advanced Booking"}),e.jsx("th",{className:"pb-4 text-center",children:"Actions"})]})}),e.jsx("tbody",{children:g.length>0?g==null?void 0:g.map(s=>{var c,_;return e.jsxs("tr",{className:"overflow-hidden",children:[e.jsx("td",{className:"rounded-l-xl bg-white px-4 py-3 text-gray-600",children:s.plan_name}),e.jsx("td",{className:"bg-white px-4 py-3",children:be(s.price)}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(m,{checked:s.allow_court,onClick:f,className:`${s.allow_court?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 cursor-not-allowed items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${s.allow_court?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(m,{checked:s.allow_coach,onClick:f,className:`${s.allow_coach?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${s.allow_coach?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(m,{checked:s.allow_clinic,onClick:f,className:`${s.allow_clinic?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${s.allow_clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(m,{checked:s.allow_buddy,onClick:f,className:`${s.allow_buddy?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${s.allow_buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex items-center justify-center gap-2",children:e.jsx(m,{checked:s.allow_groups,onClick:f,className:`${s.allow_groups?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${s.allow_groups?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-xs text-gray-600",children:E(s.applicable_sports)})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"whitespace-nowrap text-gray-500",children:((c=s.advance_booking_enabled)==null?void 0:c.court)===!1?"Disabled":`${((_=s.advance_booking_days)==null?void 0:_.court)||10}d`})})}),e.jsx("td",{className:"rounded-r-xl bg-white px-4 py-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("button",{className:"flex items-center justify-center text-gray-500 transition-colors hover:text-gray-700",onClick:()=>{S("edit"),a(s)},title:"Edit plan",children:e.jsx(oe,{className:"h-4 w-4"})}),e.jsx("button",{className:"flex items-center justify-center text-red-500 transition-colors hover:text-red-700",onClick:()=>o(s),title:"Delete plan",children:e.jsx(he,{className:"h-4 w-4"})})]})})]},s.name)}):e.jsx("tr",{className:"text-center text-sm text-gray-500",children:e.jsx("td",{colSpan:"10",children:"No plans available"})})})]})}),e.jsx(xe,{isOpen:C,onClose:n,title:"Plan details",onPrimaryAction:()=>{d(!1)},showFooter:!1,children:e.jsx(_e,{initialData:F,mode:B,onSubmit:s=>t(s,B),onClose:n,onDelete:o})}),e.jsx(me,{isOpen:i,onClose:()=>{$(!1),D(null)},title:"Delete Membership Plan",message:`Are you sure you want to delete the "${u==null?void 0:u.plan_name}" membership plan? This action cannot be undone and may affect existing members with this plan.`,onDelete:J,buttonText:"Delete Plan",loading:q,requireConfirmation:!0})]})}export{Ce as M};
