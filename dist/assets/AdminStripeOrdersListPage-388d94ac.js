import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,f as H}from"./vendor-851db8c1.js";import{M as K,G as U,A as V,t as q,g as b}from"./index-9f98cff7.js";import{o as I}from"./yup-2824f222.js";import{u as J}from"./react-hook-form-687afde5.js";import{c as Q,a as w}from"./yup-54691517.js";import{P as W}from"./index-eb1bc208.js";import"./AddButton.module-98aac587.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";const y=[{header:"Product",accessor:"product_name"},{header:"Customer",accessor:"customer"},{header:"Currency",accessor:"currency"},{header:"Amount",accessor:"amount",type:"currency"},{header:"Created at",accessor:"created_at",type:"timestamp"},{header:"Status",accessor:"status"}],Fe=()=>{var x,g;const j=new K,{dispatch:p}=r.useContext(U),{dispatch:N}=r.useContext(V);r.useState("");const[v,S]=r.useState([]),[i,d]=r.useState(10),[l,P]=r.useState(0),[X,C]=r.useState(0),[n,_]=r.useState(0),[k,D]=r.useState(!1),[E,R]=r.useState(!1);H();const T=Q({product_name:w(),customer_email:w()}),{register:u,handleSubmit:A,formState:{errors:h}}=J({resolver:I(T)});function O(t){(async function(){d(t),await c(1,t)})()}function z(){(async function(){await c(n-1>1?n-1:1,i)})()}function F(){(async function(){await c(n+1<=l?n+1:1,i)})()}async function c(t,o,a){try{const{list:s,total:G,limit:L,num_pages:f,page:m,error:M,message:B}=await j.getStripeOrders({page:t,limit:o},a);if(M){showToast(p,B,5e3);return}S(s),d(+L),P(+f),_(+m),C(+G),D(+m>1),R(+m+1<=+f)}catch(s){console.log("ERROR",s),q(N,s.message)}}const $=t=>{const o=b(t.customer_email),a=b(t.product_name);c(1,i,{customer_email:o,product_name:a})};return r.useEffect(()=>{p({type:"SETPATH",payload:{path:"orders"}}),async function(){await c(1,i)}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"p-5 bg-white shadow rounded mb-10",onSubmit:A($),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsxs("div",{className:"filter-form-holder mt-10 flex flex-wrap",children:[e.jsxs("div",{className:"mb-4 w-full md:w-1/2 pr-2 pl-2",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Customer"}),e.jsx("input",{type:"text",placeholder:"Email",...u("customer_email"),className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(x=h.customer_email)==null?void 0:x.message})]}),e.jsxs("div",{className:"mb-4 w-full md:w-1/2 pr-2 pl-2",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Product"}),e.jsx("input",{type:"text",placeholder:"Product name",...u("product_name"),className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=h.product_name)==null?void 0:g.message})]})]}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block px-6 py-2.5 bg-blue-600 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg transition duration-150 ease-in-out",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>c(1,i),className:"inline-block px-6 py-2.5 bg-gray-800 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg transition duration-150 ease-in-out",children:"Reset"})]})]}),e.jsxs("div",{className:"overflow-x-auto  p-5 bg-white shadow rounded",children:[e.jsx("div",{className:"mb-3 text-center justify-between w-full flex  ",children:e.jsx("h4",{className:"text-2xl font-medium",children:"Orders "})}),e.jsx("div",{className:"shadow overflow-x-auto border rounded-md border-gray-200 ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:y.map((t,o)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},o))})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:v.map((t,o)=>e.jsx("tr",{children:y.map((a,s)=>a.accessor==""?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap"},s):a.mapping?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.mapping[t[a.accessor]]},s):a.type==="timestamp"?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:new Date(t[a.accessor]*1e3).toLocaleString("en-US")},s):a.type==="currency"?e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:["$",Number(t[a.accessor]/100).toFixed(2)]},s):a.type==="metadata"?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t[a.pre_accessor][a.accessor]??"n/a"},s):e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t[a.accessor]},s))},o))})]})})]}),e.jsx(W,{currentPage:n,pageCount:l,pageSize:i,canPreviousPage:k,canNextPage:E,updatePageSize:O,previousPage:z,nextPage:F})]})};export{Fe as default};
